import React, { useMemo, useEffect, useRef, useState } from 'react';
import { View, StyleSheet } from 'react-native';
import { Canvas, Group, Circle, Line, Skia, vec } from '@shopify/react-native-skia';
import { PoseKeyPoint } from '../types';

interface Pose3DRendererProps {
  landmarks: PoseKeyPoint[];
  worldLandmarks?: PoseKeyPoint[];
  width: number;
  height: number;
  enableDepthVisualization?: boolean;
  showConfidenceColors?: boolean;
  enableAnimation?: boolean;
}

// 3D pose connections for skeleton rendering
const POSE_CONNECTIONS_3D = [
  // Torso
  [11, 12], [11, 23], [12, 24], [23, 24], // Shoulders to hips
  
  // Arms
  [11, 13], [13, 15], [15, 17], [15, 19], [15, 21], // Left arm
  [12, 14], [14, 16], [16, 18], [16, 20], [16, 22], // Right arm
  
  // Legs
  [23, 25], [25, 27], [27, 29], [27, 31], // Left leg
  [24, 26], [26, 28], [28, 30], [28, 32], // Right leg
  
  // Face (simplified)
  [0, 1], [1, 2], [2, 3], [3, 7], // Left eye region
  [0, 4], [4, 5], [5, 6], [6, 8], // Right eye region
];

const Pose3DRenderer: React.FC<Pose3DRendererProps> = ({
  landmarks,
  worldLandmarks,
  width,
  height,
  enableDepthVisualization = true,
  showConfidenceColors = true,
  enableAnimation = true,
}) => {
  const animationRef = useRef<number>(0);

  // Convert landmarks to 3D-like screen coordinates with enhanced depth simulation
  const convertTo3DScreenCoords = (landmarks: PoseKeyPoint[]) => {
    return landmarks.map((landmark, index) => {
      // Use world landmarks for better depth if available
      const worldLandmark = worldLandmarks?.[index];

      // Convert normalized coordinates to screen space
      let x = landmark.x * width;
      let y = landmark.y * height;

      // Apply enhanced 3D depth transformation
      const z = worldLandmark?.z || landmark.z || 0;

      if (enableDepthVisualization && z !== 0) {
        // Enhanced perspective projection with rotation effect
        const perspective = 1 + (z * 0.4); // Increased depth scaling
        const centerX = width / 2;
        const centerY = height / 2;

        // Apply perspective transformation
        x = centerX + (x - centerX) * perspective;
        y = centerY + (y - centerY) * perspective;

        // Add subtle rotation effect for depth perception
        const rotationAngle = z * 0.1; // Small rotation based on depth
        const cosAngle = Math.cos(rotationAngle);
        const sinAngle = Math.sin(rotationAngle);

        const relativeX = x - centerX;
        const relativeY = y - centerY;

        x = centerX + (relativeX * cosAngle - relativeY * sinAngle);
        y = centerY + (relativeX * sinAngle + relativeY * cosAngle);
      }

      return {
        x,
        y,
        z,
        visibility: landmark.visibility || 0,
        originalIndex: index,
        depth: Math.abs(z), // Absolute depth for scaling
      };
    });
  };

  // Get color based on confidence
  const getConfidenceColor = (visibility: number): string => {
    if (visibility > 0.8) return '#00FF00'; // Green
    if (visibility > 0.6) return '#FFFF00'; // Yellow
    if (visibility > 0.4) return '#FF8800'; // Orange
    return '#FF0000'; // Red
  };

  // Get depth-based color for 3D effect
  const getDepthColor = (z: number, baseColor: string): string => {
    if (!enableDepthVisualization) return baseColor;

    // Adjust color brightness based on depth
    const depthFactor = Math.max(0.3, 1 - Math.abs(z) * 0.5);

    // Convert hex to RGB and apply depth factor
    const hex = baseColor.replace('#', '');
    const r = Math.floor(parseInt(hex.substring(0, 2), 16) * depthFactor);
    const g = Math.floor(parseInt(hex.substring(2, 4), 16) * depthFactor);
    const b = Math.floor(parseInt(hex.substring(4, 6), 16) * depthFactor);

    return `rgb(${r}, ${g}, ${b})`;
  };

  // Create paint objects for rendering
  const connectionPaint = useMemo(() => {
    const paint = Skia.Paint();
    paint.setStyle(1); // Stroke
    paint.setStrokeWidth(3);
    paint.setAntiAlias(true);
    return paint;
  }, []);

  const landmarkPaint = useMemo(() => {
    const paint = Skia.Paint();
    paint.setStyle(0); // Fill
    paint.setAntiAlias(true);
    return paint;
  }, []);

  // Shadow paint for depth effect
  const shadowPaint = useMemo(() => {
    const paint = Skia.Paint();
    paint.setStyle(0); // Fill
    paint.setColor(Skia.Color('#000000'));
    paint.setAlphaf(0.3);
    paint.setAntiAlias(true);
    return paint;
  }, []);

  // Process landmarks for rendering
  const processedLandmarks = useMemo(() => {
    if (landmarks.length === 0) return [];
    return convertTo3DScreenCoords(landmarks);
  }, [landmarks, worldLandmarks, width, height, enableDepthVisualization]);

  // Animation effect for smooth updates
  useEffect(() => {
    if (enableAnimation) {
      animationRef.current = Date.now();
    }
  }, [landmarks, enableAnimation]);

  return (
    <View style={[styles.container, { width, height }]}>
      <Canvas style={StyleSheet.absoluteFillObject}>
        <Group>
          {/* Render pose connections (skeleton) with 3D depth effect */}
          {POSE_CONNECTIONS_3D.map((connection, index) => {
            const [startIdx, endIdx] = connection;
            const startPoint = processedLandmarks[startIdx];
            const endPoint = processedLandmarks[endIdx];

            if (!startPoint || !endPoint) return null;

            // Only render if both landmarks have sufficient confidence
            if (startPoint.visibility < 0.5 || endPoint.visibility < 0.5) return null;

            // Calculate average depth and visibility
            const avgZ = (startPoint.z + endPoint.z) / 2;
            const avgVisibility = (startPoint.visibility + endPoint.visibility) / 2;

            // Choose color based on confidence or depth
            let color = showConfidenceColors
              ? getConfidenceColor(avgVisibility)
              : '#FF6B35';

            // Apply depth-based color modification
            color = getDepthColor(avgZ, color);

            // Set paint properties
            connectionPaint.setColor(Skia.Color(color));
            connectionPaint.setAlphaf(avgVisibility);

            // Apply depth-based stroke width
            const depthStrokeWidth = enableDepthVisualization
              ? 3 * (1 + Math.abs(avgZ) * 0.5)
              : 3;
            connectionPaint.setStrokeWidth(depthStrokeWidth);

            return (
              <Line
                key={`connection-${index}`}
                p1={vec(startPoint.x, startPoint.y)}
                p2={vec(endPoint.x, endPoint.y)}
                paint={connectionPaint}
              />
            );
          })}

          {/* Render pose landmarks with enhanced 3D depth effect */}
          {processedLandmarks.map((point, index) => {
            // Only render visible landmarks
            if (point.visibility < 0.5) return null;

            // Calculate radius with depth and confidence scaling
            const baseRadius = 6;
            const depthScale = enableDepthVisualization
              ? 1 + (Math.abs(point.z) * 0.8)
              : 1;
            const confidenceScale = 0.5 + (point.visibility * 0.5);
            const radius = baseRadius * depthScale * confidenceScale;

            // Shadow effect for depth perception
            const shadowOffset = enableDepthVisualization ? Math.abs(point.z) * 3 : 0;
            const shadowRadius = radius * 1.2;

            // Choose color based on confidence
            let color = showConfidenceColors
              ? getConfidenceColor(point.visibility)
              : '#FF6B35';

            // Apply depth-based color modification
            color = getDepthColor(point.z, color);

            // Set paint properties
            landmarkPaint.setColor(Skia.Color(color));
            landmarkPaint.setAlphaf(point.visibility);

            return (
              <Group key={`landmark-group-${index}`}>
                {/* Shadow for depth effect */}
                {enableDepthVisualization && shadowOffset > 0 && (
                  <Circle
                    cx={point.x + shadowOffset}
                    cy={point.y + shadowOffset}
                    r={shadowRadius}
                    paint={shadowPaint}
                  />
                )}

                {/* Main landmark */}
                <Circle
                  cx={point.x}
                  cy={point.y}
                  r={radius}
                  paint={landmarkPaint}
                />
              </Group>
            );
          })}
        </Group>
      </Canvas>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    zIndex: 5,
  },
});

export default Pose3DRenderer;
