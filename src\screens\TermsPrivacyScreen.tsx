import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  StatusBar,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { LinearGradient } from 'expo-linear-gradient';
import { RootStackParamList } from '../types';

type TermsPrivacyScreenNavigationProp = StackNavigationProp<RootStackParamList>;

const TermsPrivacyScreen: React.FC = () => {
  const { t, i18n } = useTranslation();
  const navigation = useNavigation<TermsPrivacyScreenNavigationProp>();
  const [activeTab, setActiveTab] = useState<'terms' | 'privacy'>('terms');

  const renderTermsContent = () => (
    <View style={styles.contentSection}>
      <Text style={styles.sectionTitle}>
        {i18n.language === 'hi' ? 'सेवा की शर्तें' : 'Terms of Service'}
      </Text>
      <Text style={styles.lastUpdated}>
        {i18n.language === 'hi' ? 'अंतिम अपडेट: 30 अगस्त, 2025' : 'Last updated: August 30, 2025'}
      </Text>

      <View style={styles.section}>
        <Text style={styles.subTitle}>
          {i18n.language === 'hi' ? '1. सेवा की स्वीकृति' : '1. Acceptance of Terms'}
        </Text>
        <Text style={styles.content}>
          {i18n.language === 'hi'
            ? 'पोस्चर ऐप का उपयोग करके, आप इन नियमों और शर्तों से सहमत होते हैं। यदि आप इन शर्तों से सहमत नहीं हैं, तो कृपया ऐप का उपयोग न करें।'
            : 'By using the Posture App, you agree to be bound by these Terms of Service. If you do not agree to these terms, please do not use the app.'}
        </Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.subTitle}>
          {i18n.language === 'hi' ? '2. सेवा का विवरण' : '2. Service Description'}
        </Text>
        <Text style={styles.content}>
          {i18n.language === 'hi'
            ? 'पोस्चर ऐप एक स्वास्थ्य और कल्याण एप्लिकेशन है जो AI-संचालित पोस्चर विश्लेषण, योग व्यायाम, और व्यक्तिगत सुधार सुझाव प्रदान करता है।'
            : 'Posture App is a health and wellness application that provides AI-powered posture analysis, yoga exercises, and personalized improvement recommendations.'}
        </Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.subTitle}>
          {i18n.language === 'hi' ? '3. उपयोगकर्ता दायित्व' : '3. User Responsibilities'}
        </Text>
        <Text style={styles.content}>
          {i18n.language === 'hi'
            ? '• सटीक और वर्तमान जानकारी प्रदान करें\n• ऐप का दुरुपयोग न करें\n• अन्य उपयोगकर्ताओं के अधिकारों का सम्मान करें\n• स्वास्थ्य सलाह के लिए पेशेवर चिकित्सक से सलाह लें'
            : '• Provide accurate and current information\n• Do not misuse the app\n• Respect other users\' rights\n• Consult healthcare professionals for medical advice'}
        </Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.subTitle}>
          {i18n.language === 'hi' ? '4. स्वास्थ्य अस्वीकरण' : '4. Health Disclaimer'}
        </Text>
        <Text style={styles.content}>
          {i18n.language === 'hi'
            ? 'यह ऐप चिकित्सा सलाह, निदान या उपचार का विकल्प नहीं है। किसी भी स्वास्थ्य संबंधी चिंता के लिए हमेशा योग्य स्वास्थ्य पेशेवर से सलाह लें।'
            : 'This app is not a substitute for medical advice, diagnosis, or treatment. Always consult qualified healthcare professionals for any health concerns.'}
        </Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.subTitle}>
          {i18n.language === 'hi' ? '5. बौद्धिक संपदा' : '5. Intellectual Property'}
        </Text>
        <Text style={styles.content}>
          {i18n.language === 'hi'
            ? 'ऐप की सभी सामग्री, डिज़ाइन, और सुविधाएं हमारी बौद्धिक संपदा हैं और कॉपीराइट कानूनों द्वारा संरक्षित हैं।'
            : 'All app content, design, and features are our intellectual property and protected by copyright laws.'}
        </Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.subTitle}>
          {i18n.language === 'hi' ? '6. सेवा में परिवर्तन' : '6. Service Modifications'}
        </Text>
        <Text style={styles.content}>
          {i18n.language === 'hi'
            ? 'हम बिना पूर्व सूचना के सेवा को संशोधित, निलंबित या बंद करने का अधिकार सुरक्षित रखते हैं।'
            : 'We reserve the right to modify, suspend, or discontinue the service without prior notice.'}
        </Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.subTitle}>
          {i18n.language === 'hi' ? '7. संपर्क जानकारी' : '7. Contact Information'}
        </Text>
        <Text style={styles.content}>
          {i18n.language === 'hi'
            ? 'प्रश्नों के लिए संपर्क करें: <EMAIL>'
            : 'For questions, contact us at: <EMAIL>'}
        </Text>
      </View>
    </View>
  );

  const renderPrivacyContent = () => (
    <View style={styles.contentSection}>
      <Text style={styles.sectionTitle}>
        {i18n.language === 'hi' ? 'गोपनीयता नीति' : 'Privacy Policy'}
      </Text>
      <Text style={styles.lastUpdated}>
        {i18n.language === 'hi' ? 'अंतिम अपडेट: 30 अगस्त, 2025' : 'Last updated: August 30, 2025'}
      </Text>

      <View style={styles.section}>
        <Text style={styles.subTitle}>
          {i18n.language === 'hi' ? '1. जानकारी संग्रह' : '1. Information Collection'}
        </Text>
        <Text style={styles.content}>
          {i18n.language === 'hi'
            ? 'हम निम्नलिखित जानकारी एकत्र करते हैं:\n• व्यक्तिगत जानकारी (नाम, ईमेल)\n• पोस्चर विश्लेषण डेटा\n• ऐप उपयोग आंकड़े\n• डिवाइस जानकारी'
            : 'We collect the following information:\n• Personal information (name, email)\n• Posture analysis data\n• App usage statistics\n• Device information'}
        </Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.subTitle}>
          {i18n.language === 'hi' ? '2. डेटा का उपयोग' : '2. Data Usage'}
        </Text>
        <Text style={styles.content}>
          {i18n.language === 'hi'
            ? 'आपका डेटा निम्नलिखित उद्देश्यों के लिए उपयोग किया जाता है:\n• व्यक्तिगत पोस्चर विश्लेषण प्रदान करना\n• ऐप अनुभव में सुधार\n• प्रगति ट्रैकिंग\n• ग्राहक सहायता'
            : 'Your data is used for:\n• Providing personalized posture analysis\n• Improving app experience\n• Progress tracking\n• Customer support'}
        </Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.subTitle}>
          {i18n.language === 'hi' ? '3. डेटा सुरक्षा' : '3. Data Security'}
        </Text>
        <Text style={styles.content}>
          {i18n.language === 'hi'
            ? 'हम आपके डेटा की सुरक्षा के लिए उद्योग-मानक एन्क्रिप्शन और सुरक्षा उपायों का उपयोग करते हैं। सभी डेटा सुरक्षित सर्वर पर संग्रहीत किया जाता है।'
            : 'We use industry-standard encryption and security measures to protect your data. All data is stored on secure servers.'}
        </Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.subTitle}>
          {i18n.language === 'hi' ? '4. डेटा साझाकरण' : '4. Data Sharing'}
        </Text>
        <Text style={styles.content}>
          {i18n.language === 'hi'
            ? 'हम आपकी व्यक्तिगत जानकारी तीसरे पक्ष के साथ बेचते या साझा नहीं करते हैं, सिवाय:\n• कानूनी आवश्यकताओं के लिए\n• सेवा प्रदाताओं के साथ (गुमनाम डेटा)\n• आपकी स्पष्ट सहमति के साथ'
            : 'We do not sell or share your personal information with third parties, except:\n• For legal requirements\n• With service providers (anonymized data)\n• With your explicit consent'}
        </Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.subTitle}>
          {i18n.language === 'hi' ? '5. कैमरा और फोटो डेटा' : '5. Camera and Photo Data'}
        </Text>
        <Text style={styles.content}>
          {i18n.language === 'hi'
            ? 'पोस्चर विश्लेषण के लिए ली गई तस्वीरें केवल विश्लेषण के लिए उपयोग की जाती हैं और तुरंत डिलीट कर दी जाती हैं। हम आपकी तस्वीरें संग्रहीत नहीं करते।'
            : 'Photos taken for posture analysis are used only for analysis and immediately deleted. We do not store your photos.'}
        </Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.subTitle}>
          {i18n.language === 'hi' ? '6. आपके अधिकार' : '6. Your Rights'}
        </Text>
        <Text style={styles.content}>
          {i18n.language === 'hi'
            ? 'आपके पास निम्नलिखित अधिकार हैं:\n• अपना डेटा एक्सेस करना\n• डेटा को सुधारना या अपडेट करना\n• डेटा डिलीट करने का अनुरोध\n• डेटा प्रोसेसिंग पर आपत्ति'
            : 'You have the right to:\n• Access your data\n• Correct or update data\n• Request data deletion\n• Object to data processing'}
        </Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.subTitle}>
          {i18n.language === 'hi' ? '7. कुकीज़ और ट्रैकिंग' : '7. Cookies and Tracking'}
        </Text>
        <Text style={styles.content}>
          {i18n.language === 'hi'
            ? 'हम ऐप के प्रदर्शन को बेहतर बनाने और उपयोग पैटर्न को समझने के लिए एनालिटिक्स का उपयोग करते हैं। यह डेटा गुमनाम है।'
            : 'We use analytics to improve app performance and understand usage patterns. This data is anonymized.'}
        </Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.subTitle}>
          {i18n.language === 'hi' ? '8. संपर्क करें' : '8. Contact Us'}
        </Text>
        <Text style={styles.content}>
          {i18n.language === 'hi'
            ? 'गोपनीयता संबंधी प्रश्नों के लिए: <EMAIL>'
            : 'For privacy questions: <EMAIL>'}
        </Text>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#000000" />

      {/* Background Gradient */}
      <LinearGradient
        colors={['#000000', '#1a1a1a', '#2d2d2d']}
        style={StyleSheet.absoluteFillObject}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>
          {i18n.language === 'hi' ? 'नियम और गोपनीयता' : 'Terms & Privacy'}
        </Text>
        <View style={styles.placeholder} />
      </View>

      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'terms' && styles.activeTab]}
          onPress={() => setActiveTab('terms')}
        >
          <Text style={[styles.tabText, activeTab === 'terms' && styles.activeTabText]}>
            {i18n.language === 'hi' ? 'नियम' : 'Terms'}
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'privacy' && styles.activeTab]}
          onPress={() => setActiveTab('privacy')}
        >
          <Text style={[styles.tabText, activeTab === 'privacy' && styles.activeTabText]}>
            {i18n.language === 'hi' ? 'गोपनीयता' : 'Privacy'}
          </Text>
        </TouchableOpacity>
      </View>

      {/* Content */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {activeTab === 'terms' ? renderTermsContent() : renderPrivacyContent()}
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
    paddingBottom: 16,
    paddingHorizontal: 20,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  placeholder: {
    width: 40,
  },
  tabContainer: {
    flexDirection: 'row',
    marginHorizontal: 20,
    marginBottom: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 12,
    padding: 4,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 8,
  },
  activeTab: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  tabText: {
    fontSize: 14,
    fontWeight: '500',
    color: 'rgba(255, 255, 255, 0.6)',
  },
  activeTabText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  content: {
    flex: 1,
  },
  contentSection: {
    paddingHorizontal: 20,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#FFFFFF',
    marginBottom: 8,
    letterSpacing: 0.3,
  },
  lastUpdated: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.6)',
    marginBottom: 24,
  },
  section: {
    marginBottom: 24,
  },
  subTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: 8,
    letterSpacing: 0.2,
  },
  contentText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    lineHeight: 22,
    letterSpacing: 0.1,
  },
  bottomSpacing: {
    height: Platform.OS === 'ios' ? 122 : 102,
  },
});

export default TermsPrivacyScreen;