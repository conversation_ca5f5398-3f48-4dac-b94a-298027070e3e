import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  StatusBar,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { LinearGradient } from 'expo-linear-gradient';
import { useAuth } from '../contexts/AuthContext';
import { useAlert } from '../contexts/AlertContext';
import { RootStackParamList } from '../types';
import { postureAnalysisService, PostureAnalysisRecord, PostureAnalysisStats } from '../services/postureAnalysisService';

type PostureHistoryNavigationProp = StackNavigationProp<RootStackParamList>;

const PostureHistoryScreen: React.FC = () => {
  const { t, i18n } = useTranslation();
  const navigation = useNavigation<PostureHistoryNavigationProp>();
  const { user } = useAuth();
  const { showAlert } = useAlert();

  const [analyses, setAnalyses] = useState<PostureAnalysisRecord[]>([]);
  const [stats, setStats] = useState<PostureAnalysisStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    if (!user) return;

    try {
      setLoading(true);
      const [analysesData, statsData] = await Promise.all([
        postureAnalysisService.getUserAnalyses(user.id),
        postureAnalysisService.getUserStats(user.id)
      ]);

      setAnalyses(analysesData);
      setStats(statsData);
    } catch (error) {
      console.error('Error loading posture data:', error);
      showAlert({
        title: t('common.error'),
        message: i18n.language === 'hi'
          ? 'डेटा लोड करने में त्रुटि हुई'
          : 'Error loading data',
        type: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  const deleteAnalysis = async (analysisId: string) => {
    if (!user) return;

    showAlert({
      title: i18n.language === 'hi' ? 'विश्लेषण हटाएं' : 'Delete Analysis',
      message: i18n.language === 'hi'
        ? 'क्या आप वाकई इस विश्लेषण को हटाना चाहते हैं?'
        : 'Are you sure you want to delete this analysis?',
      type: 'warning',
      buttons: [
        {
          text: t('common.cancel'),
          style: 'cancel'
        },
        {
          text: i18n.language === 'hi' ? 'हटाएं' : 'Delete',
          style: 'destructive',
          onPress: async () => {
            const success = await postureAnalysisService.deleteAnalysis(analysisId, user.id);
            if (success) {
              setAnalyses(prev => prev.filter(a => a.id !== analysisId));
              // Reload stats
              const newStats = await postureAnalysisService.getUserStats(user.id);
              setStats(newStats);
            } else {
              showAlert({
                title: t('common.error'),
                message: i18n.language === 'hi'
                  ? 'विश्लेषण हटाने में त्रुटि हुई'
                  : 'Error deleting analysis',
                type: 'error',
              });
            }
          }
        }
      ]
    });
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return '#10B981';
    if (score >= 75) return '#F59E0B';
    if (score >= 60) return '#EF4444';
    return '#DC2626';
  };

  const getScoreText = (score: number) => {
    if (score >= 90) return i18n.language === 'hi' ? 'उत्कृष्ट' : 'Excellent';
    if (score >= 75) return i18n.language === 'hi' ? 'अच्छा' : 'Good';
    if (score >= 60) return i18n.language === 'hi' ? 'ठीक' : 'Fair';
    return i18n.language === 'hi' ? 'खराब' : 'Poor';
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(i18n.language === 'hi' ? 'hi-IN' : 'en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const renderAnalysisItem = ({ item }: { item: PostureAnalysisRecord }) => (
    <View style={styles.analysisCard}>
      <View style={styles.analysisHeader}>
        <View style={styles.scoreContainer}>
          <Text style={[styles.scoreText, { color: getScoreColor(item.overall_score) }]}>
            {item.overall_score}
          </Text>
          <Text style={styles.scoreLabel}>/100</Text>
        </View>
        <View style={styles.analysisInfo}>
          <Text style={styles.statusText}>
            {getScoreText(item.overall_score)}
          </Text>
          <Text style={styles.dateText}>
            {formatDate(item.created_at)}
          </Text>
        </View>
        <TouchableOpacity
          style={styles.deleteButton}
          onPress={() => deleteAnalysis(item.id)}
        >
          <Ionicons name="trash-outline" size={20} color="#FF3B30" />
        </TouchableOpacity>
      </View>

      <View style={styles.metricsContainer}>
        <View style={styles.metric}>
          <Text style={styles.metricLabel}>
            {i18n.language === 'hi' ? 'गर्दन' : 'Neck'}
          </Text>
          <Text style={styles.metricValue}>{item.neck_angle}°</Text>
        </View>
        <View style={styles.metric}>
          <Text style={styles.metricLabel}>
            {i18n.language === 'hi' ? 'कंधे' : 'Shoulders'}
          </Text>
          <Text style={styles.metricValue}>{item.shoulder_alignment}%</Text>
        </View>
        <View style={styles.metric}>
          <Text style={styles.metricLabel}>
            {i18n.language === 'hi' ? 'रीढ़' : 'Spine'}
          </Text>
          <Text style={styles.metricValue}>{item.spine_alignment}%</Text>
        </View>
        <View style={styles.metric}>
          <Text style={styles.metricLabel}>
            {i18n.language === 'hi' ? 'कूल्हे' : 'Hips'}
          </Text>
          <Text style={styles.metricValue}>{item.hip_alignment}%</Text>
        </View>
      </View>

      {item.recommendations && item.recommendations.length > 0 && (
        <View style={styles.recommendationsContainer}>
          <Text style={styles.recommendationsTitle}>
            {i18n.language === 'hi' ? 'सुझाव:' : 'Recommendations:'}
          </Text>
          {item.recommendations.slice(0, 2).map((rec, index) => (
            <Text key={index} style={styles.recommendationText}>
              • {rec}
            </Text>
          ))}
        </View>
      )}
    </View>
  );

  const renderStatsCard = () => {
    if (!stats) return null;

    return (
      <View style={styles.statsCard}>
        <Text style={styles.statsTitle}>
          {i18n.language === 'hi' ? 'आपकी प्रगति' : 'Your Progress'}
        </Text>

        <View style={styles.statsGrid}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{stats.totalAnalyses}</Text>
            <Text style={styles.statLabel}>
              {i18n.language === 'hi' ? 'कुल विश्लेषण' : 'Total Analyses'}
            </Text>
          </View>

          <View style={styles.statItem}>
            <Text style={[styles.statValue, { color: getScoreColor(stats.averageScore) }]}>
              {stats.averageScore}
            </Text>
            <Text style={styles.statLabel}>
              {i18n.language === 'hi' ? 'औसत स्कोर' : 'Average Score'}
            </Text>
          </View>

          <View style={styles.statItem}>
            <Text style={[styles.statValue, { color: getScoreColor(stats.bestScore) }]}>
              {stats.bestScore}
            </Text>
            <Text style={styles.statLabel}>
              {i18n.language === 'hi' ? 'सर्वोत्तम स्कोर' : 'Best Score'}
            </Text>
          </View>

          <View style={styles.statItem}>
            <Text style={[
              styles.statValue,
              { color: stats.improvementTrend >= 0 ? '#10B981' : '#EF4444' }
            ]}>
              {stats.improvementTrend >= 0 ? '+' : ''}{stats.improvementTrend}%
            </Text>
            <Text style={styles.statLabel}>
              {i18n.language === 'hi' ? 'सुधार' : 'Improvement'}
            </Text>
          </View>
        </View>
      </View>
    );
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <LinearGradient
          colors={['#000000', '#1a1a1a', '#2d2d2d']}
          style={StyleSheet.absoluteFillObject}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        />
        <ActivityIndicator size="large" color="#FFFFFF" />
        <Text style={styles.loadingText}>
          {i18n.language === 'hi' ? 'लोड हो रहा है...' : 'Loading...'}
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#000000" />

      {/* Background Gradient */}
      <LinearGradient
        colors={['#000000', '#1a1a1a', '#2d2d2d']}
        style={StyleSheet.absoluteFillObject}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      />
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>
          {i18n.language === 'hi' ? 'मुद्रा इतिहास' : 'Posture History'}
        </Text>
        <TouchableOpacity
          style={styles.refreshButton}
          onPress={onRefresh}
        >
          <Ionicons name="refresh" size={24} color="#FFFFFF" />
        </TouchableOpacity>
      </View>

      <FlatList
        data={analyses}
        renderItem={renderAnalysisItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        ListHeaderComponent={renderStatsCard}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="analytics-outline" size={64} color="rgba(255, 255, 255, 0.3)" />
            <Text style={styles.emptyTitle}>
              {i18n.language === 'hi' ? 'कोई विश्लेषण नहीं' : 'No Analyses Yet'}
            </Text>
            <Text style={styles.emptyText}>
              {i18n.language === 'hi'
                ? 'अपना पहला मुद्रा विश्लेषण करने के लिए होम स्क्रीन पर जाएं'
                : 'Go to the home screen to perform your first posture analysis'
              }
            </Text>
          </View>
        }
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={['#6366F1']}
            tintColor="#FFFFFF"
          />
        }
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    flex: 1,
    textAlign: 'center',
  },
  refreshButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContainer: {
    padding: 20,
    paddingTop: 0,
  },
  statsCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  statsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 16,
    textAlign: 'center',
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statItem: {
    width: '48%',
    alignItems: 'center',
    marginBottom: 16,
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#6366F1',
  },
  statLabel: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.7)',
    marginTop: 4,
    textAlign: 'center',
  },
  analysisCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    elevation: 4,
  },
  analysisHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  scoreContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
  },
  scoreText: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  scoreLabel: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.7)',
    marginLeft: 2,
  },
  analysisInfo: {
    flex: 1,
    marginLeft: 16,
  },
  statusText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  dateText: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.7)',
    marginTop: 2,
  },
  deleteButton: {
    padding: 8,
  },
  metricsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  metric: {
    alignItems: 'center',
    flex: 1,
  },
  metricLabel: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.7)',
    marginBottom: 4,
  },
  metricValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  recommendationsContainer: {
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    paddingTop: 12,
  },
  recommendationsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 8,
  },
  recommendationText: {
    fontSize: 12,
    color: '#64748B',
    marginBottom: 4,
    lineHeight: 16,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'rgba(255, 255, 255, 0.9)',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
    paddingHorizontal: 40,
    lineHeight: 24,
  },
});

export default PostureHistoryScreen;