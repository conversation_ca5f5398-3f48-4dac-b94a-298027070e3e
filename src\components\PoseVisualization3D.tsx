import React, { useMemo, useEffect, useRef } from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import { Canvas, Group, Circle, Line, Paint, Skia } from '@shopify/react-native-skia';
import { PoseKeyPoint } from '../types';

interface PoseVisualization3DProps {
  landmarks: PoseKeyPoint[];
  worldLandmarks?: PoseKeyPoint[];
  confidence: number;
  width: number;
  height: number;
  showSkeleton?: boolean;
  showLandmarks?: boolean;
  colorScheme?: 'default' | 'confidence' | 'depth';
  enableSmoothing?: boolean;
}

// MediaPipe pose connections for skeleton rendering
const POSE_CONNECTIONS = [
  // Face
  ['left_ear', 'left_eye_outer'], ['left_eye_outer', 'left_eye'],
  ['left_eye', 'left_eye_inner'], ['left_eye_inner', 'nose'],
  ['nose', 'right_eye_inner'], ['right_eye_inner', 'right_eye'],
  ['right_eye', 'right_eye_outer'], ['right_eye_outer', 'right_ear'],
  
  // Torso
  ['left_shoulder', 'right_shoulder'], ['left_shoulder', 'left_elbow'],
  ['left_elbow', 'left_wrist'], ['right_shoulder', 'right_elbow'],
  ['right_elbow', 'right_wrist'], ['left_shoulder', 'left_hip'],
  ['right_shoulder', 'right_hip'], ['left_hip', 'right_hip'],
  
  // Arms
  ['left_wrist', 'left_thumb'], ['left_wrist', 'left_pinky'],
  ['left_wrist', 'left_index'], ['right_wrist', 'right_thumb'],
  ['right_wrist', 'right_pinky'], ['right_wrist', 'right_index'],
  
  // Legs
  ['left_hip', 'left_knee'], ['left_knee', 'left_ankle'],
  ['right_hip', 'right_knee'], ['right_knee', 'right_ankle'],
  ['left_ankle', 'left_heel'], ['left_ankle', 'left_foot_index'],
  ['right_ankle', 'right_heel'], ['right_ankle', 'right_foot_index'],
];

const PoseVisualization3D: React.FC<PoseVisualization3DProps> = ({
  landmarks,
  worldLandmarks,
  confidence,
  width,
  height,
  showSkeleton = true,
  showLandmarks = true,
  colorScheme = 'confidence',
  enableSmoothing = true,
}) => {
  const previousLandmarksRef = useRef<PoseKeyPoint[]>([]);

  // Smooth landmark transitions
  const smoothedLandmarks = useMemo(() => {
    if (!enableSmoothing || previousLandmarksRef.current.length === 0) {
      previousLandmarksRef.current = landmarks;
      return landmarks;
    }

    const smoothed = landmarks.map((landmark, index) => {
      const prev = previousLandmarksRef.current[index];
      if (!prev) return landmark;

      // Apply exponential smoothing
      const alpha = 0.7; // Smoothing factor
      return {
        ...landmark,
        x: alpha * landmark.x + (1 - alpha) * prev.x,
        y: alpha * landmark.y + (1 - alpha) * prev.y,
        z: alpha * (landmark.z || 0) + (1 - alpha) * (prev.z || 0),
      };
    });

    previousLandmarksRef.current = smoothed;
    return smoothed;
  }, [landmarks, enableSmoothing]);

  // Convert normalized coordinates to screen coordinates
  const toScreenCoords = (landmark: PoseKeyPoint) => ({
    x: landmark.x * width,
    y: landmark.y * height,
    z: landmark.z || 0,
    visibility: landmark.visibility || 1.0,
  });

  // Get color based on confidence and depth
  const getLandmarkColor = (landmark: PoseKeyPoint): string => {
    const visibility = landmark.visibility || 1.0;
    
    switch (colorScheme) {
      case 'confidence':
        if (visibility > 0.8) return '#00FF00'; // Green - high confidence
        if (visibility > 0.6) return '#FFFF00'; // Yellow - medium confidence
        return '#FF6B35'; // Orange - low confidence
        
      case 'depth':
        const depth = landmark.z || 0;
        const normalizedDepth = Math.max(0, Math.min(1, (depth + 1) / 2));
        return `hsl(${240 - normalizedDepth * 120}, 100%, 50%)`; // Blue to red gradient
        
      default:
        return '#FF6B35'; // Brand color
    }
  };

  // Get connection color based on landmark confidence
  const getConnectionColor = (start: PoseKeyPoint, end: PoseKeyPoint): string => {
    const avgVisibility = ((start.visibility || 0) + (end.visibility || 0)) / 2;
    
    if (avgVisibility > 0.8) return '#00FF00';
    if (avgVisibility > 0.6) return '#FFFF00';
    return '#FF6B35';
  };

  // Find landmark by name
  const findLandmark = (name: string) => 
    smoothedLandmarks.find(landmark => landmark.name === name);

  // Update landmarks when they change
  useEffect(() => {
    // Simple update without animation for now
  }, [landmarks]);

  // Create paint objects for rendering
  const landmarkPaint = useMemo(() => {
    const paint = Skia.Paint();
    paint.setAntiAlias(true);
    return paint;
  }, []);

  const connectionPaint = useMemo(() => {
    const paint = Skia.Paint();
    paint.setAntiAlias(true);
    paint.setStrokeWidth(3);
    paint.setStyle(1); // Stroke style
    return paint;
  }, []);

  return (
    <View style={[styles.container, { width, height }]}>
      <Canvas style={StyleSheet.absoluteFillObject}>
        <Group>
          {/* Render pose connections (skeleton) */}
          {showSkeleton && POSE_CONNECTIONS.map((connection, index) => {
            const [startName, endName] = connection;
            const startLandmark = findLandmark(startName);
            const endLandmark = findLandmark(endName);

            if (!startLandmark || !endLandmark) return null;

            const start = toScreenCoords(startLandmark);
            const end = toScreenCoords(endLandmark);

            // Only render if both landmarks have sufficient confidence
            if (start.visibility < 0.5 || end.visibility < 0.5) return null;

            const color = getConnectionColor(startLandmark, endLandmark);
            connectionPaint.setColor(Skia.Color(color));

            return (
              <Line
                key={`connection-${index}`}
                p1={{ x: start.x, y: start.y }}
                p2={{ x: end.x, y: end.y }}
                paint={connectionPaint}
              />
            );
          })}

          {/* Render pose landmarks */}
          {showLandmarks && smoothedLandmarks.map((landmark, index) => {
            const screenCoords = toScreenCoords(landmark);
            
            // Only render visible landmarks
            if (screenCoords.visibility < 0.5) return null;

            const color = getLandmarkColor(landmark);
            landmarkPaint.setColor(Skia.Color(color));

            // Vary circle size based on confidence and depth
            const baseRadius = 6;
            const depthScale = 1 + (screenCoords.z * 0.5); // Scale based on depth
            const confidenceScale = 0.5 + (screenCoords.visibility * 0.5);
            const radius = baseRadius * depthScale * confidenceScale;

            return (
              <Circle
                key={`landmark-${index}`}
                cx={screenCoords.x}
                cy={screenCoords.y}
                r={radius}
                paint={landmarkPaint}
              />
            );
          })}
        </Group>
      </Canvas>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    zIndex: 10,
  },
});

export default PoseVisualization3D;
