import { supabase } from '../lib/supabase';
import { logger } from './logger';

/**
 * Utility functions for robust authentication handling
 */
export class AuthUtils {
  /**
   * Wait for authentication state to be established with retry logic
   * Useful after signup/login when auth state needs time to propagate
   */
  static async waitForAuthState(maxWaitMs: number = 10000): Promise<{ user: any; session: any } | null> {
    const startTime = Date.now();
    const retryInterval = 500; // Check every 500ms
    
    while (Date.now() - startTime < maxWaitMs) {
      try {
        // Check session first
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();
        
        if (!sessionError && session?.user) {
          logger.info('Auth state established via session', {
            userId: session.user.id,
            emailConfirmed: !!session.user.email_confirmed_at
          }, 'AuthUtils');
          return { user: session.user, session };
        }

        // Fallback to getUser
        const { data: { user }, error: userError } = await supabase.auth.getUser();

        if (!userError && user) {
          logger.info('Auth state established via getUser', {
            userId: user.id,
            emailConfirmed: !!user.email_confirmed_at
          }, 'AuthUtils');
          return { user, session: null };
        }

        // Wait before next retry
        await new Promise(resolve => setTimeout(resolve, retryInterval));
        
      } catch (error) {
        logger.warn('Auth state check failed, retrying...', error, 'AuthUtils');
        await new Promise(resolve => setTimeout(resolve, retryInterval));
      }
    }
    
    logger.error('Auth state not established within timeout', { maxWaitMs }, 'AuthUtils');
    return null;
  }

  /**
   * Get current authenticated user with retry logic
   * More robust than direct supabase calls
   */
  static async getCurrentUser(maxRetries: number = 3): Promise<any | null> {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        // Try session first
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();
        
        if (!sessionError && session?.user) {
          return session.user;
        }

        // Fallback to getUser
        const { data: { user }, error: userError } = await supabase.auth.getUser();
        
        if (!userError && user) {
          return user;
        }

        // If not the last attempt, wait and retry
        if (attempt < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
        
      } catch (error) {
        logger.warn(`Auth attempt ${attempt} failed`, error, 'AuthUtils');
        if (attempt < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }
    }
    
    return null;
  }

  /**
   * Check if user is authenticated with retry logic
   */
  static async isAuthenticated(maxRetries: number = 3): Promise<boolean> {
    const user = await this.getCurrentUser(maxRetries);
    return !!user;
  }

  /**
   * Get current user ID with retry logic
   */
  static async getCurrentUserId(maxRetries: number = 3): Promise<string | null> {
    const user = await this.getCurrentUser(maxRetries);
    return user?.id || null;
  }

  /**
   * Verify user matches expected ID (for security)
   */
  static async verifyUserMatch(expectedUserId: string, maxRetries: number = 3): Promise<boolean> {
    const currentUserId = await this.getCurrentUserId(maxRetries);
    return currentUserId === expectedUserId;
  }

  /**
   * Check if current user's email is confirmed
   */
  static async isEmailConfirmed(maxRetries: number = 3): Promise<boolean> {
    const user = await this.getCurrentUser(maxRetries);
    return !!(user?.email_confirmed_at);
  }

  /**
   * Get email confirmation status
   */
  static async getEmailConfirmationStatus(maxRetries: number = 3): Promise<{
    isConfirmed: boolean;
    email: string | null;
    confirmedAt: string | null;
  }> {
    const user = await this.getCurrentUser(maxRetries);
    return {
      isConfirmed: !!(user?.email_confirmed_at),
      email: user?.email || null,
      confirmedAt: user?.email_confirmed_at || null,
    };
  }
}
