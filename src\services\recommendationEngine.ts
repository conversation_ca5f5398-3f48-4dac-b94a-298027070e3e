import {
  QuestionnaireResponse,
  UserInsight,
  PersonalizedRecommendation,
  UserPreferences,
  RecommendationType,
  InsightCategory,
} from '../types';
import { dataService } from './dataService';
import { logger } from '../utils/logger';

/**
 * Enterprise-level recommendation engine for personalized posture improvement
 * Implements machine learning-inspired algorithms for intelligent recommendations
 */
export class RecommendationEngine {
  private static instance: RecommendationEngine;

  private constructor() {}

  public static getInstance(): RecommendationEngine {
    if (!RecommendationEngine.instance) {
      RecommendationEngine.instance = new RecommendationEngine();
    }
    return RecommendationEngine.instance;
  }

  /**
   * Generate comprehensive insights from questionnaire responses
   */
  async generateInsights(
    userId: string,
    responses: QuestionnaireResponse[]
  ): Promise<UserInsight[]> {
    try {
      const insights: UserInsight[] = [];
      const responseMap = this.createResponseMap(responses);

      // Analyze work environment and posture risks
      const workInsights = this.analyzeWorkEnvironment(responseMap);
      insights.push(...workInsights);

      // Analyze pain patterns and body alignment
      const painInsights = this.analyzePainPatterns(responseMap);
      insights.push(...painInsights);

      // Analyze lifestyle and activity levels
      const lifestyleInsights = this.analyzeLifestyle(responseMap);
      insights.push(...lifestyleInsights);

      // Analyze fitness level and exercise preferences
      const fitnessInsights = this.analyzeFitnessLevel(responseMap);
      insights.push(...fitnessInsights);

      // Analyze screen time and digital wellness
      const digitalInsights = this.analyzeDigitalWellness(responseMap);
      insights.push(...digitalInsights);

      // Save insights to database
      await this.saveInsights(userId, insights);

      logger.info('Generated user insights', {
        userId,
        insightCount: insights.length,
        categories: insights.map(i => i.category),
      }, 'RecommendationEngine');

      return insights;
    } catch (error) {
      logger.error('Failed to generate insights', error, 'RecommendationEngine');
      throw error;
    }
  }

  /**
   * Generate personalized recommendations based on insights and user preferences
   */
  async generateRecommendations(
    userId: string,
    insights: UserInsight[],
    preferences: UserPreferences
  ): Promise<PersonalizedRecommendation[]> {
    try {
      const recommendations: PersonalizedRecommendation[] = [];

      // Generate exercise recommendations
      const exerciseRecs = this.generateExerciseRecommendations(insights, preferences);
      recommendations.push(...exerciseRecs);

      // Generate posture improvement recommendations
      const postureRecs = this.generatePostureRecommendations(insights, preferences);
      recommendations.push(...postureRecs);

      // Generate lifestyle modification recommendations
      const lifestyleRecs = this.generateLifestyleRecommendations(insights, preferences);
      recommendations.push(...lifestyleRecs);

      // Generate workspace ergonomics recommendations
      const ergonomicsRecs = this.generateErgonomicsRecommendations(insights, preferences);
      recommendations.push(...ergonomicsRecs);

      // Generate mindfulness and stress management recommendations
      const mindfulnessRecs = this.generateMindfulnessRecommendations(insights, preferences);
      recommendations.push(...mindfulnessRecs);

      // Prioritize and limit recommendations based on user capacity
      const prioritizedRecs = this.prioritizeRecommendations(recommendations, preferences);

      // Save recommendations to database
      await this.saveRecommendations(userId, prioritizedRecs);

      logger.info('Generated personalized recommendations', {
        userId,
        recommendationCount: prioritizedRecs.length,
        types: prioritizedRecs.map(r => r.type),
      }, 'RecommendationEngine');

      return prioritizedRecs;
    } catch (error) {
      logger.error('Failed to generate recommendations', error, 'RecommendationEngine');
      throw error;
    }
  }

  /**
   * Create a map of responses for easy lookup
   */
  private createResponseMap(responses: QuestionnaireResponse[]): Map<string, QuestionnaireResponse> {
    const map = new Map<string, QuestionnaireResponse>();
    responses.forEach(response => {
      map.set(response.questionId, response);
    });
    return map;
  }

  /**
   * Analyze work environment for posture risks
   */
  private analyzeWorkEnvironment(responseMap: Map<string, QuestionnaireResponse>): UserInsight[] {
    const insights: UserInsight[] = [];
    
    const workSetup = responseMap.get('work_setup')?.answerValue;
    const screenTime = responseMap.get('daily_screen_time')?.answerValue;
    const workBreaks = responseMap.get('work_breaks')?.answerValue;

    if (workSetup === 'poor' || screenTime === 'high' || workBreaks === 'rarely') {
      insights.push({
        id: `work_env_${Date.now()}`,
        category: 'work_environment' as InsightCategory,
        type: 'risk_assessment',
        title: 'High Posture Risk Work Environment',
        description: 'Your work setup and habits indicate elevated risk for posture-related issues.',
        severity: 'high',
        priority: 9,
        confidence: 8,
        actionable: true,
        relatedQuestions: ['work_setup', 'daily_screen_time', 'work_breaks'],
        data: {
          workSetup,
          screenTime,
          workBreaks,
          riskFactors: ['prolonged_sitting', 'poor_ergonomics', 'insufficient_breaks'],
        },
        createdAt: new Date(),
      });
    }

    return insights;
  }

  /**
   * Analyze pain patterns and identify problem areas
   */
  private analyzePainPatterns(responseMap: Map<string, QuestionnaireResponse>): UserInsight[] {
    const insights: UserInsight[] = [];
    
    const neckPain = responseMap.get('neck_pain_frequency')?.answerValue;
    const backPain = responseMap.get('back_pain_frequency')?.answerValue;
    const shoulderPain = responseMap.get('shoulder_pain_frequency')?.answerValue;

    const painAreas = [neckPain, backPain, shoulderPain].filter(pain => 
      pain === 'frequent' || pain === 'daily'
    );

    if (painAreas.length > 0) {
      insights.push({
        id: `pain_pattern_${Date.now()}`,
        category: 'health_assessment' as InsightCategory,
        type: 'pain_analysis',
        title: 'Chronic Pain Pattern Detected',
        description: 'Multiple areas showing frequent pain indicate systemic posture issues.',
        severity: 'high',
        priority: 10,
        confidence: 9,
        actionable: true,
        relatedQuestions: ['neck_pain_frequency', 'back_pain_frequency', 'shoulder_pain_frequency'],
        data: {
          painAreas: { neckPain, backPain, shoulderPain },
          frequency: painAreas.length,
          recommendation: 'immediate_intervention',
        },
        createdAt: new Date(),
      });
    }

    return insights;
  }

  /**
   * Analyze lifestyle factors affecting posture
   */
  private analyzeLifestyle(responseMap: Map<string, QuestionnaireResponse>): UserInsight[] {
    const insights: UserInsight[] = [];
    
    const exerciseFreq = responseMap.get('exercise_frequency')?.answerValue;
    const sleepQuality = responseMap.get('sleep_quality')?.answerValue;
    const stressLevel = responseMap.get('stress_level')?.answerValue;

    if (exerciseFreq === 'rarely' && (sleepQuality === 'poor' || stressLevel === 'high')) {
      insights.push({
        id: `lifestyle_${Date.now()}`,
        category: 'lifestyle_factor' as InsightCategory,
        type: 'holistic_assessment',
        title: 'Lifestyle Factors Affecting Posture',
        description: 'Low activity, poor sleep, or high stress can significantly impact posture.',
        severity: 'medium',
        priority: 7,
        confidence: 7,
        actionable: true,
        relatedQuestions: ['exercise_frequency', 'sleep_quality', 'stress_level'],
        data: {
          exerciseFreq,
          sleepQuality,
          stressLevel,
          interventions: ['increase_activity', 'stress_management', 'sleep_hygiene'],
        },
        createdAt: new Date(),
      });
    }

    return insights;
  }

  /**
   * Analyze fitness level and exercise readiness
   */
  private analyzeFitnessLevel(responseMap: Map<string, QuestionnaireResponse>): UserInsight[] {
    const insights: UserInsight[] = [];
    
    const fitnessLevel = responseMap.get('fitness_level')?.answerValue;
    const exerciseExp = responseMap.get('exercise_experience')?.answerValue;
    const timeAvailable = responseMap.get('available_time')?.answerValue;

    if (fitnessLevel === 'beginner' && timeAvailable === 'limited') {
      insights.push({
        id: `fitness_${Date.now()}`,
        category: 'goal_alignment' as InsightCategory,
        type: 'capacity_analysis',
        title: 'Beginner-Friendly Approach Recommended',
        description: 'Starting with short, simple exercises will build sustainable habits.',
        severity: 'low',
        priority: 5,
        confidence: 6,
        actionable: true,
        relatedQuestions: ['fitness_level', 'exercise_experience', 'available_time'],
        data: {
          fitnessLevel,
          exerciseExp,
          timeAvailable,
          approach: 'gradual_progression',
        },
        createdAt: new Date(),
      });
    }

    return insights;
  }

  /**
   * Analyze digital wellness and screen time impact
   */
  private analyzeDigitalWellness(responseMap: Map<string, QuestionnaireResponse>): UserInsight[] {
    const insights: UserInsight[] = [];
    
    const screenTime = responseMap.get('daily_screen_time')?.answerValue;
    const deviceUsage = responseMap.get('device_usage_pattern')?.answerValue;
    const eyeStrain = responseMap.get('eye_strain_frequency')?.answerValue;

    if (screenTime === 'high' && eyeStrain === 'frequent') {
      insights.push({
        id: `digital_wellness_${Date.now()}`,
        category: 'ergonomic_issue' as InsightCategory,
        type: 'screen_time_analysis',
        title: 'Digital Eye Strain and Posture Impact',
        description: 'High screen time with eye strain indicates need for digital wellness strategies.',
        severity: 'medium',
        priority: 6,
        confidence: 7,
        actionable: true,
        relatedQuestions: ['daily_screen_time', 'device_usage_pattern', 'eye_strain_frequency'],
        data: {
          screenTime,
          deviceUsage,
          eyeStrain,
          interventions: ['20_20_20_rule', 'screen_breaks', 'blue_light_management'],
        },
        createdAt: new Date(),
      });
    }

    return insights;
  }

  /**
   * Generate exercise recommendations based on insights and preferences
   */
  private generateExerciseRecommendations(
    insights: UserInsight[],
    preferences: UserPreferences
  ): PersonalizedRecommendation[] {
    const recommendations: PersonalizedRecommendation[] = [];
    
    // Neck and shoulder exercises for pain relief
    const hasNeckIssues = insights.some(i => 
      i.data?.painAreas?.neckPain === 'frequent' || 
      i.data?.riskFactors?.includes('forward_head_posture')
    );

    if (hasNeckIssues) {
      recommendations.push({
        id: `neck_exercise_${Date.now()}`,
        type: 'exercise' as RecommendationType,
        title: 'Neck Strengthening Routine',
        description: 'Gentle neck exercises to reduce tension and improve alignment.',
        priority: 8,
        estimatedTimeMinutes: 10,
        difficultyLevel: preferences.fitnessLevel || 'beginner',
        category: 'neck_shoulders',
        tags: ['pain_relief', 'strengthening', 'daily'],
        data: {
          exercises: ['chin_tucks', 'neck_stretches', 'shoulder_rolls'],
          frequency: 'daily',
          duration: '10_minutes',
        },
        createdAt: new Date(),
      });
    }

    return recommendations;
  }

  /**
   * Generate posture improvement recommendations
   */
  private generatePostureRecommendations(
    insights: UserInsight[],
    preferences: UserPreferences
  ): PersonalizedRecommendation[] {
    const recommendations: PersonalizedRecommendation[] = [];
    
    const hasPostureIssues = insights.some(i =>
      i.category === 'ergonomic_issue' && i.severity === 'high'
    );

    if (hasPostureIssues) {
      recommendations.push({
        id: `posture_awareness_${Date.now()}`,
        type: 'habit' as RecommendationType,
        title: 'Posture Check Reminders',
        description: 'Regular posture checks throughout the day to build awareness.',
        priority: 7,
        estimatedTimeMinutes: 2,
        difficultyLevel: 'beginner',
        category: 'posture_awareness',
        tags: ['habit_building', 'awareness', 'workplace'],
        data: {
          frequency: 'hourly',
          reminderType: 'gentle',
          checkpoints: ['head_position', 'shoulder_alignment', 'spine_curve'],
        },
        createdAt: new Date(),
      });
    }

    return recommendations;
  }

  /**
   * Generate lifestyle modification recommendations
   */
  private generateLifestyleRecommendations(
    insights: UserInsight[],
    preferences: UserPreferences
  ): PersonalizedRecommendation[] {
    const recommendations: PersonalizedRecommendation[] = [];
    
    const hasLifestyleIssues = insights.some(i =>
      i.category === 'lifestyle_factor' && i.data?.interventions?.includes('increase_activity')
    );

    if (hasLifestyleIssues) {
      recommendations.push({
        id: `movement_breaks_${Date.now()}`,
        type: 'lifestyle' as RecommendationType,
        title: 'Movement Breaks',
        description: 'Short movement breaks every hour to counteract prolonged sitting.',
        priority: 6,
        estimatedTimeMinutes: 5,
        difficultyLevel: 'beginner',
        category: 'movement',
        tags: ['workplace', 'movement', 'energy'],
        data: {
          breakType: 'micro_movement',
          frequency: 'hourly',
          activities: ['desk_stretches', 'walking', 'standing'],
        },
        createdAt: new Date(),
      });
    }

    return recommendations;
  }

  /**
   * Generate workspace ergonomics recommendations
   */
  private generateErgonomicsRecommendations(
    insights: UserInsight[],
    preferences: UserPreferences
  ): PersonalizedRecommendation[] {
    const recommendations: PersonalizedRecommendation[] = [];
    
    const hasWorkspaceIssues = insights.some(i =>
      i.category === 'ergonomic_issue' &&
      i.data?.riskFactors?.includes('poor_ergonomics')
    );

    if (hasWorkspaceIssues) {
      recommendations.push({
        id: `ergonomics_${Date.now()}`,
        type: 'environment' as RecommendationType,
        title: 'Workspace Ergonomics Setup',
        description: 'Optimize your workspace to support better posture naturally.',
        priority: 8,
        estimatedTimeMinutes: 30,
        difficultyLevel: 'beginner',
        category: 'ergonomics',
        tags: ['workspace', 'setup', 'prevention'],
        data: {
          setupType: 'comprehensive',
          areas: ['monitor_height', 'chair_adjustment', 'keyboard_position'],
          checklist: true,
        },
        createdAt: new Date(),
      });
    }

    return recommendations;
  }

  /**
   * Generate mindfulness and stress management recommendations
   */
  private generateMindfulnessRecommendations(
    insights: UserInsight[],
    preferences: UserPreferences
  ): PersonalizedRecommendation[] {
    const recommendations: PersonalizedRecommendation[] = [];
    
    const hasStressIssues = insights.some(i => 
      i.data?.stressLevel === 'high' || 
      i.data?.interventions?.includes('stress_management')
    );

    if (hasStressIssues) {
      recommendations.push({
        id: `mindfulness_${Date.now()}`,
        type: 'mindfulness' as RecommendationType,
        title: 'Stress-Relief Breathing',
        description: 'Simple breathing exercises to reduce stress and muscle tension.',
        priority: 5,
        estimatedTimeMinutes: 5,
        difficultyLevel: 'beginner',
        category: 'stress_management',
        tags: ['breathing', 'relaxation', 'mental_health'],
        data: {
          technique: 'box_breathing',
          duration: '5_minutes',
          frequency: 'as_needed',
        },
        createdAt: new Date(),
      });
    }

    return recommendations;
  }

  /**
   * Prioritize recommendations based on user capacity and preferences
   */
  private prioritizeRecommendations(
    recommendations: PersonalizedRecommendation[],
    preferences: UserPreferences
  ): PersonalizedRecommendation[] {
    // Sort by priority (higher first)
    const sorted = recommendations.sort((a, b) => {
      const aPriority = typeof a.priority === 'number' ? a.priority : 5;
      const bPriority = typeof b.priority === 'number' ? b.priority : 5;
      return bPriority - aPriority;
    });
    
    // Limit based on available time and fitness level
    const timeLimit = preferences.availableTimeMinutes || 15;
    const maxRecommendations = preferences.fitnessLevel === 'beginner' ? 3 : 5;
    
    return sorted.slice(0, maxRecommendations).filter(rec => 
      (rec.estimatedTimeMinutes || 0) <= timeLimit
    );
  }

  /**
   * Save insights to database
   */
  private async saveInsights(userId: string, insights: UserInsight[]): Promise<void> {
    try {
      await dataService.saveUserInsights(userId, insights);
    } catch (error) {
      logger.error('Failed to save insights', error, 'RecommendationEngine');
      throw error;
    }
  }

  /**
   * Save recommendations to database
   */
  private async saveRecommendations(
    userId: string, 
    recommendations: PersonalizedRecommendation[]
  ): Promise<void> {
    try {
      await dataService.savePersonalizedRecommendations(userId, recommendations);
    } catch (error) {
      logger.error('Failed to save recommendations', error, 'RecommendationEngine');
      throw error;
    }
  }

  /**
   * Update recommendations based on user feedback and progress
   */
  async updateRecommendations(
    userId: string,
    completedRecommendations: string[],
    userFeedback: { [recommendationId: string]: number }
  ): Promise<PersonalizedRecommendation[]> {
    try {
      // Get current user preferences and insights
      const preferences = await dataService.getUserPreferences(userId);
      const insights = await dataService.getUserInsights(userId);
      
      // Generate updated recommendations
      const updatedRecommendations = await this.generateRecommendations(
        userId,
        insights,
        preferences
      );

      logger.info('Updated recommendations based on user progress', {
        userId,
        completedCount: completedRecommendations.length,
        newCount: updatedRecommendations.length,
      }, 'RecommendationEngine');

      return updatedRecommendations;
    } catch (error) {
      logger.error('Failed to update recommendations', error, 'RecommendationEngine');
      throw error;
    }
  }
}

export const recommendationEngine = RecommendationEngine.getInstance();
