import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Animated, StatusBar } from 'react-native';

interface ThemeColors {
  primary: string;
  secondary: string;
  background: string;
  surface: string;
  text: string;
  textSecondary: string;
  border: string;
  accent: string;
}

interface Theme {
  colors: ThemeColors;
  isDark: boolean;
  name: string;
}

interface ThemeContextType {
  theme: Theme;
  isInputActive: boolean;
  setInputActive: (active: boolean) => void;
  switchTheme: (themeName: string) => void;
  animatedValues: {
    backgroundOpacity: Animated.Value;
    borderOpacity: Animated.Value;
    textOpacity: Animated.Value;
  };
}

const defaultTheme: Theme = {
  colors: {
    primary: '#FFFFFF',
    secondary: 'rgba(255, 255, 255, 0.8)',
    background: '#000000',
    surface: 'rgba(255, 255, 255, 0.05)',
    text: '#FFFFFF',
    textSecondary: 'rgba(255, 255, 255, 0.7)',
    border: 'rgba(255, 255, 255, 0.15)',
    accent: '#4ADE80',
  },
  isDark: true,
  name: 'default',
};

const inputActiveTheme: Theme = {
  colors: {
    primary: '#FFFFFF',
    secondary: 'rgba(255, 255, 255, 0.9)',
    background: '#000000',
    surface: 'rgba(255, 255, 255, 0.08)',
    text: '#FFFFFF',
    textSecondary: 'rgba(255, 255, 255, 0.8)',
    border: 'rgba(255, 255, 255, 0.25)',
    accent: '#60EFFF',
  },
  isDark: true,
  name: 'inputActive',
};

const successTheme: Theme = {
  colors: {
    primary: '#4ADE80',
    secondary: 'rgba(74, 222, 128, 0.8)',
    background: '#000000',
    surface: 'rgba(74, 222, 128, 0.1)',
    text: '#FFFFFF',
    textSecondary: 'rgba(255, 255, 255, 0.8)',
    border: 'rgba(74, 222, 128, 0.3)',
    accent: '#4ADE80',
  },
  isDark: true,
  name: 'success',
};

const themes = {
  default: defaultTheme,
  inputActive: inputActiveTheme,
  success: successTheme,
};

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
  children: ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [currentTheme, setCurrentTheme] = useState<Theme>(defaultTheme);
  const [isInputActive, setIsInputActive] = useState(false);
  
  // Animated values for smooth transitions
  const backgroundOpacity = new Animated.Value(1);
  const borderOpacity = new Animated.Value(1);
  const textOpacity = new Animated.Value(1);

  const animatedValues = {
    backgroundOpacity,
    borderOpacity,
    textOpacity,
  };

  const setInputActive = (active: boolean) => {
    setIsInputActive(active);
    
    if (active) {
      switchTheme('inputActive');
    } else {
      switchTheme('default');
    }
  };

  const switchTheme = (themeName: string) => {
    const newTheme = themes[themeName as keyof typeof themes] || defaultTheme;
    
    // Animate theme transition
    Animated.parallel([
      Animated.timing(backgroundOpacity, {
        toValue: 0.7,
        duration: 200,
        useNativeDriver: false,
      }),
      Animated.timing(borderOpacity, {
        toValue: 0.7,
        duration: 200,
        useNativeDriver: false,
      }),
      Animated.timing(textOpacity, {
        toValue: 0.7,
        duration: 200,
        useNativeDriver: false,
      }),
    ]).start(() => {
      setCurrentTheme(newTheme);
      
      // Animate back to full opacity
      Animated.parallel([
        Animated.timing(backgroundOpacity, {
          toValue: 1,
          duration: 300,
          useNativeDriver: false,
        }),
        Animated.timing(borderOpacity, {
          toValue: 1,
          duration: 300,
          useNativeDriver: false,
        }),
        Animated.timing(textOpacity, {
          toValue: 1,
          duration: 300,
          useNativeDriver: false,
        }),
      ]).start();
    });
  };

  // Update status bar based on theme
  useEffect(() => {
    StatusBar.setBarStyle(currentTheme.isDark ? 'light-content' : 'dark-content');
  }, [currentTheme]);

  const value: ThemeContextType = {
    theme: currentTheme,
    isInputActive,
    setInputActive,
    switchTheme,
    animatedValues,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export type { Theme, ThemeColors };