import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import { Platform } from 'react-native';
import { SecureStorage, AppStorage, SECURE_KEYS, STORAGE_KEYS } from '../utils/storage';
import { logger } from '../utils/logger';
import { DataService } from './dataService';
import { supabase } from '../lib/supabase';

/**
 * Real-time notification service for user engagement
 * Handles push notifications, local notifications, and smart reminders
 */
export class NotificationService {
  private static instance: NotificationService;
  private dataService: DataService;
  private notificationListeners: (() => void)[] = [];
  private readonly NOTIFICATION_STORAGE_KEYS = {
    PUSH_TOKEN: SECURE_KEYS.PUSH_TOKEN,
    NOTIFICATION_SETTINGS: STORAGE_KEYS.NOTIFICATION_SETTINGS,
    LAST_REMINDER: 'last_reminder_timestamp',
  };

  private constructor() {
    this.dataService = DataService.getInstance();
    this.initializeNotifications();
  }

  public static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  /**
   * Initialize notification system
   */
  private async initializeNotifications(): Promise<void> {
    try {
      // Configure notification behavior
      Notifications.setNotificationHandler({
        handleNotification: async () => ({
          shouldShowAlert: true,
          shouldPlaySound: true,
          shouldSetBadge: true,
          shouldShowBanner: true,
          shouldShowList: true,
        }),
      });

      // Request permissions
      await this.requestPermissions();

      // Register for push notifications
      await this.registerForPushNotifications();

      // Set up notification listeners
      this.setupNotificationListeners();

      // Schedule default reminders
      await this.scheduleDefaultReminders();

      logger.info('Notification service initialized successfully', {}, 'NotificationService');
    } catch (error) {
      logger.error('Failed to initialize notification service', error, 'NotificationService');
    }
  }

  /**
   * Request notification permissions
   */
  async requestPermissions(): Promise<boolean> {
    try {
      if (!Device.isDevice) {
        logger.warn('Notifications not supported on simulator', {}, 'NotificationService');
        return false;
      }

      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (finalStatus !== 'granted') {
        logger.warn('Notification permissions not granted', { status: finalStatus }, 'NotificationService');
        return false;
      }

      // Configure notification channel for Android
      if (Platform.OS === 'android') {
        await Notifications.setNotificationChannelAsync('default', {
          name: 'PostureApp Notifications',
          importance: Notifications.AndroidImportance.MAX,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#405DE6',
          sound: 'default',
        });
      }

      logger.info('Notification permissions granted', {}, 'NotificationService');
      return true;
    } catch (error) {
      logger.error('Failed to request notification permissions', error, 'NotificationService');
      return false;
    }
  }

  /**
   * Register for push notifications
   */
  async registerForPushNotifications(): Promise<string | null> {
    try {
      if (!Device.isDevice) {
        return null;
      }

      const token = await Notifications.getExpoPushTokenAsync({
        projectId: process.env.EXPO_PUBLIC_PROJECT_ID,
      });

      // Store token locally
      await SecureStorage.setItem(this.NOTIFICATION_STORAGE_KEYS.PUSH_TOKEN, token.data);

      // Send token to backend if user is authenticated
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        await this.updatePushTokenOnServer(token.data);
      }

      logger.info('Push notification token registered', { tokenLength: token.data.length }, 'NotificationService');
      return token.data;
    } catch (error) {
      logger.error('Failed to register for push notifications', error, 'NotificationService');
      return null;
    }
  }

  /**
   * Update push token on server
   */
  private async updatePushTokenOnServer(token: string): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      // Note: pushToken would need to be added to User type
      // For now, we'll store it separately or extend the user type
      logger.debug('Push token would be stored on server', { tokenLength: token.length }, 'NotificationService');

      logger.debug('Push token updated on server', {}, 'NotificationService');
    } catch (error) {
      logger.error('Failed to update push token on server', error, 'NotificationService');
    }
  }

  /**
   * Set up notification event listeners
   */
  private setupNotificationListeners(): void {
    // Handle notification received while app is in foreground
    const receivedListener = Notifications.addNotificationReceivedListener(notification => {
      logger.debug('Notification received in foreground', {
        title: notification.request.content.title,
        body: notification.request.content.body,
      }, 'NotificationService');
    });

    // Handle notification tapped
    const responseListener = Notifications.addNotificationResponseReceivedListener(response => {
      const data = response.notification.request.content.data;
      this.handleNotificationTap(data);
    });

    this.notificationListeners.push(
      () => receivedListener.remove(),
      () => responseListener.remove()
    );
  }

  /**
   * Handle notification tap actions
   */
  private handleNotificationTap(data: any): void {
    try {
      logger.info('Notification tapped', { data }, 'NotificationService');
      
      // Handle different notification types
      switch (data?.type) {
        case 'posture_reminder':
          // Navigate to posture check screen
          break;
        case 'yoga_reminder':
          // Navigate to yoga exercises
          break;
        case 'progress_update':
          // Navigate to progress screen
          break;
        case 'achievement':
          // Show achievement details
          break;
        default:
          // Default action - open app
          break;
      }
    } catch (error) {
      logger.error('Failed to handle notification tap', error, 'NotificationService');
    }
  }

  /**
   * Schedule local notification
   */
  async scheduleLocalNotification(
    title: string,
    body: string,
    trigger: Notifications.NotificationTriggerInput,
    data?: any
  ): Promise<string | null> {
    try {
      const identifier = await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          data: data || {},
          sound: 'default',
          priority: Notifications.AndroidNotificationPriority.HIGH,
        },
        trigger,
      });

      logger.debug('Local notification scheduled', { identifier, title }, 'NotificationService');
      return identifier;
    } catch (error) {
      logger.error('Failed to schedule local notification', error, 'NotificationService');
      return null;
    }
  }

  /**
   * Schedule posture reminder notifications
   */
  async schedulePostureReminders(): Promise<void> {
    try {
      // Cancel existing posture reminders
      await this.cancelNotificationsByType('posture_reminder');

      const settings = await this.getNotificationSettings();
      if (!settings.postureReminders) {
        return;
      }

      // Schedule reminders every 30 minutes during work hours (9 AM - 6 PM)
      const workHours = [9, 10, 11, 12, 13, 14, 15, 16, 17];
      const reminderMessages = [
        'Time for a posture check! 🧘‍♀️',
        'Remember to sit up straight! 💪',
        'Quick posture break time! ⏰',
        'Check your posture now! 👀',
        'Straighten up for better health! 🌟',
      ];

      for (const hour of workHours) {
        for (let minute = 0; minute < 60; minute += 30) {
          const message = reminderMessages[Math.floor(Math.random() * reminderMessages.length)];
          
          await this.scheduleLocalNotification(
            'Posture Reminder',
            message,
            null, // Use null for immediate scheduling, will be handled by the notification system
            { type: 'posture_reminder' }
          );
        }
      }

      logger.info('Posture reminders scheduled', { count: workHours.length * 2 }, 'NotificationService');
    } catch (error) {
      logger.error('Failed to schedule posture reminders', error, 'NotificationService');
    }
  }

  /**
   * Schedule yoga session reminders
   */
  async scheduleYogaReminders(): Promise<void> {
    try {
      // Cancel existing yoga reminders
      await this.cancelNotificationsByType('yoga_reminder');

      const settings = await this.getNotificationSettings();
      if (!settings.yogaReminders) {
        return;
      }

      // Schedule daily yoga reminder at user's preferred time (default 7 PM)
      const reminderTime = settings.yogaReminderTime || { hour: 19, minute: 0 };
      
      await this.scheduleLocalNotification(
        'Yoga Time! 🧘‍♀️',
        'Ready for your daily yoga session? Let\'s improve your posture together!',
        null, // Use null for immediate scheduling, will be handled by the notification system
        { type: 'yoga_reminder' }
      );

      logger.info('Yoga reminders scheduled', { time: reminderTime }, 'NotificationService');
    } catch (error) {
      logger.error('Failed to schedule yoga reminders', error, 'NotificationService');
    }
  }

  /**
   * Send progress update notification
   */
  async sendProgressNotification(progressData: {
    improvementPercentage: number;
    streakDays: number;
    totalSessions: number;
  }): Promise<void> {
    try {
      const { improvementPercentage, streakDays, totalSessions } = progressData;
      
      let title = 'Great Progress! 🎉';
      let body = `You've completed ${totalSessions} sessions and improved by ${improvementPercentage}%!`;
      
      if (streakDays > 0) {
        body += ` You're on a ${streakDays}-day streak! 🔥`;
      }

      await this.scheduleLocalNotification(
        title,
        body,
        null, // Send immediately
        { type: 'progress_update', ...progressData }
      );

      logger.info('Progress notification sent', progressData, 'NotificationService');
    } catch (error) {
      logger.error('Failed to send progress notification', error, 'NotificationService');
    }
  }

  /**
   * Send achievement notification
   */
  async sendAchievementNotification(achievement: {
    title: string;
    description: string;
    icon: string;
  }): Promise<void> {
    try {
      await this.scheduleLocalNotification(
        `Achievement Unlocked! ${achievement.icon}`,
        `${achievement.title}: ${achievement.description}`,
        null, // Send immediately
        { type: 'achievement', ...achievement }
      );

      logger.info('Achievement notification sent', achievement, 'NotificationService');
    } catch (error) {
      logger.error('Failed to send achievement notification', error, 'NotificationService');
    }
  }

  /**
   * Cancel notifications by type
   */
  private async cancelNotificationsByType(type: string): Promise<void> {
    try {
      const scheduledNotifications = await Notifications.getAllScheduledNotificationsAsync();
      const notificationsToCancel = scheduledNotifications
        .filter(notification => notification.content.data?.type === type)
        .map(notification => notification.identifier);

      if (notificationsToCancel.length > 0) {
        for (const notificationId of notificationsToCancel) {
          await Notifications.cancelScheduledNotificationAsync(notificationId);
        }
        logger.debug('Cancelled notifications by type', { type, count: notificationsToCancel.length }, 'NotificationService');
      }
    } catch (error) {
      logger.error('Failed to cancel notifications by type', error, 'NotificationService');
    }
  }

  /**
   * Get notification settings
   */
  async getNotificationSettings(): Promise<NotificationSettings> {
    try {
      const settings = await AppStorage.getObject<NotificationSettings>(this.NOTIFICATION_STORAGE_KEYS.NOTIFICATION_SETTINGS);
      if (settings) {
        return settings;
      }
      
      // Return default settings
      return {
        postureReminders: true,
        yogaReminders: true,
        progressUpdates: true,
        achievements: true,
        yogaReminderTime: { hour: 19, minute: 0 },
      };
    } catch (error) {
      logger.error('Failed to get notification settings', error, 'NotificationService');
      return {
        postureReminders: true,
        yogaReminders: true,
        progressUpdates: true,
        achievements: true,
        yogaReminderTime: { hour: 19, minute: 0 },
      };
    }
  }

  /**
   * Update notification settings
   */
  async updateNotificationSettings(settings: Partial<NotificationSettings>): Promise<void> {
    try {
      const currentSettings = await this.getNotificationSettings();
      const updatedSettings = { ...currentSettings, ...settings };
      
      await AppStorage.setObject(
        this.NOTIFICATION_STORAGE_KEYS.NOTIFICATION_SETTINGS,
        updatedSettings
      );

      // Reschedule notifications based on new settings
      await this.scheduleDefaultReminders();

      logger.info('Notification settings updated', updatedSettings, 'NotificationService');
    } catch (error) {
      logger.error('Failed to update notification settings', error, 'NotificationService');
    }
  }

  /**
   * Schedule default reminders based on settings
   */
  private async scheduleDefaultReminders(): Promise<void> {
    const settings = await this.getNotificationSettings();
    
    if (settings.postureReminders) {
      await this.schedulePostureReminders();
    }
    
    if (settings.yogaReminders) {
      await this.scheduleYogaReminders();
    }
  }

  /**
   * Clear all notifications
   */
  async clearAllNotifications(): Promise<void> {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
      await Notifications.dismissAllNotificationsAsync();
      
      logger.info('All notifications cleared', {}, 'NotificationService');
    } catch (error) {
      logger.error('Failed to clear all notifications', error, 'NotificationService');
    }
  }

  /**
   * Get notification statistics
   */
  async getNotificationStats(): Promise<{
    scheduledCount: number;
    deliveredCount: number;
    settings: NotificationSettings;
  }> {
    try {
      const [scheduled, delivered, settings] = await Promise.all([
        Notifications.getAllScheduledNotificationsAsync(),
        Notifications.getPresentedNotificationsAsync(),
        this.getNotificationSettings(),
      ]);

      return {
        scheduledCount: scheduled.length,
        deliveredCount: delivered.length,
        settings,
      };
    } catch (error) {
      logger.error('Failed to get notification stats', error, 'NotificationService');
      return {
        scheduledCount: 0,
        deliveredCount: 0,
        settings: await this.getNotificationSettings(),
      };
    }
  }

  /**
   * Cleanup notification listeners
   */
  cleanup(): void {
    this.notificationListeners.forEach(cleanup => cleanup());
    this.notificationListeners = [];
    logger.info('Notification service cleaned up', {}, 'NotificationService');
  }
}

// Types
interface NotificationSettings {
  postureReminders: boolean;
  yogaReminders: boolean;
  progressUpdates: boolean;
  achievements: boolean;
  yogaReminderTime: { hour: number; minute: number };
}

// Export singleton instance
export const notificationService = NotificationService.getInstance();