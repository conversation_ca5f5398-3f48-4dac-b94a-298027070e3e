{"logs": [{"outputFile": "com.postureapp.android.app-mergeDebugResources-68:/values-sv/values-sv.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6c6e309b72c7c7f21fbfce9b95a8faf6\\transformed\\core-1.13.1\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,449,557,662,783", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "145,247,345,444,552,657,778,879"}, "to": {"startLines": "56,57,58,59,60,61,62,235", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4133,4228,4330,4428,4527,4635,4740,19467", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "4223,4325,4423,4522,4630,4735,4856,19563"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bf4e5700f050532bbd59ead7b0c07184\\transformed\\play-services-base-18.5.0\\res\\values-sv\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,449,572,678,815,936,1055,1155,1299,1403,1561,1685,1835,1987,2049,2108", "endColumns": "102,152,122,105,136,120,118,99,143,103,157,123,149,151,61,58,74", "endOffsets": "295,448,571,677,814,935,1054,1154,1298,1402,1560,1684,1834,1986,2048,2107,2182"}, "to": {"startLines": "67,68,69,70,71,72,73,74,76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5243,5350,5507,5634,5744,5885,6010,6133,6385,6533,6641,6803,6931,7085,7241,7307,7370", "endColumns": "106,156,126,109,140,124,122,103,147,107,161,127,153,155,65,62,78", "endOffsets": "5345,5502,5629,5739,5880,6005,6128,6232,6528,6636,6798,6926,7080,7236,7302,7365,7444"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7bb9f3beb2453d006dfcb077349e135e\\transformed\\exoplayer-core-2.18.1\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,191,255,330,411,485,579,665", "endColumns": "72,62,63,74,80,73,93,85,72", "endOffsets": "123,186,250,325,406,480,574,660,733"}, "to": {"startLines": "113,114,115,116,117,118,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9763,9836,9899,9963,10038,10119,10193,10287,10373", "endColumns": "72,62,63,74,80,73,93,85,72", "endOffsets": "9831,9894,9958,10033,10114,10188,10282,10368,10441"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8c47125c9a335e989accf2286b6eb952\\transformed\\play-services-basement-18.4.0\\res\\values-sv\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "143", "endOffsets": "338"}, "to": {"startLines": "75", "startColumns": "4", "startOffsets": "6237", "endColumns": "147", "endOffsets": "6380"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\df4af9fc07c95635f45d375fd55e05f1\\transformed\\play-services-wallet-18.1.3\\res\\values-sv\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "69", "endOffsets": "271"}, "to": {"startLines": "239", "startColumns": "4", "startOffsets": "19801", "endColumns": "73", "endOffsets": "19870"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f3b883529050a580787eb1fe00d61c7b\\transformed\\exoplayer-ui-2.18.1\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,480,674,760,847,930,1018,1102,1170,1234,1332,1430,1495,1563,1629,1702,1822,1942,2060,2135,2217,2293,2361,2451,2542,2607,2671,2724,2784,2832,2893,2957,3028,3092,3157,3222,3281,3346,3410,3476,3528,3588,3671,3754", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,86,82,87,83,67,63,97,97,64,67,65,72,119,119,117,74,81,75,67,89,90,64,63,52,59,47,60,63,70,63,64,64,58,64,63,65,51,59,82,82,51", "endOffsets": "280,475,669,755,842,925,1013,1097,1165,1229,1327,1425,1490,1558,1624,1697,1817,1937,2055,2130,2212,2288,2356,2446,2537,2602,2666,2719,2779,2827,2888,2952,3023,3087,3152,3217,3276,3341,3405,3471,3523,3583,3666,3749,3801"}, "to": {"startLines": "2,11,15,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,575,7766,7852,7939,8022,8110,8194,8262,8326,8424,8522,8587,8655,8721,8794,8914,9034,9152,9227,9309,9385,9453,9543,9634,9699,10446,10499,10559,10607,10668,10732,10803,10867,10932,10997,11056,11121,11185,11251,11303,11363,11446,11529", "endLines": "10,14,18,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139", "endColumns": "17,12,12,85,86,82,87,83,67,63,97,97,64,67,65,72,119,119,117,74,81,75,67,89,90,64,63,52,59,47,60,63,70,63,64,64,58,64,63,65,51,59,82,82,51", "endOffsets": "375,570,764,7847,7934,8017,8105,8189,8257,8321,8419,8517,8582,8650,8716,8789,8909,9029,9147,9222,9304,9380,9448,9538,9629,9694,9758,10494,10554,10602,10663,10727,10798,10862,10927,10992,11051,11116,11180,11246,11298,11358,11441,11524,11576"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ecf58a1d5a3b5cc4faabc13e873181f2\\transformed\\react-android-0.79.5-debug\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,211,282,350,431,498,565,639,716,798,877,946,1028,1111,1188,1271,1350,1427,1497,1566,1651,1731,1806", "endColumns": "72,82,70,67,80,66,66,73,76,81,78,68,81,82,76,82,78,76,69,68,84,79,74,77", "endOffsets": "123,206,277,345,426,493,560,634,711,793,872,941,1023,1106,1183,1266,1345,1422,1492,1561,1646,1726,1801,1879"}, "to": {"startLines": "50,66,151,153,154,156,170,171,172,219,220,221,222,227,228,229,230,231,232,233,234,236,237,238", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3606,5160,12986,13121,13189,13330,14331,14398,14472,18204,18286,18365,18434,18844,18927,19004,19087,19166,19243,19313,19382,19568,19648,19723", "endColumns": "72,82,70,67,80,66,66,73,76,81,78,68,81,82,76,82,78,76,69,68,84,79,74,77", "endOffsets": "3674,5238,13052,13184,13265,13392,14393,14467,14544,18281,18360,18429,18511,18922,18999,19082,19161,19238,19308,19377,19462,19643,19718,19796"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af9a9d7ace362c65c6063a55648731b1\\transformed\\appcompat-1.7.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,422,506,606,719,796,871,964,1059,1154,1248,1350,1445,1542,1640,1736,1829,1909,2015,2114,2210,2315,2418,2520,2674,2776", "endColumns": "102,102,110,83,99,112,76,74,92,94,94,93,101,94,96,97,95,92,79,105,98,95,104,102,101,153,101,79", "endOffsets": "203,306,417,501,601,714,791,866,959,1054,1149,1243,1345,1440,1537,1635,1731,1824,1904,2010,2109,2205,2310,2413,2515,2669,2771,2851"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "935,1038,1141,1252,1336,1436,1549,1626,1701,1794,1889,1984,2078,2180,2275,2372,2470,2566,2659,2739,2845,2944,3040,3145,3248,3350,3504,18516", "endColumns": "102,102,110,83,99,112,76,74,92,94,94,93,101,94,96,97,95,92,79,105,98,95,104,102,101,153,101,79", "endOffsets": "1033,1136,1247,1331,1431,1544,1621,1696,1789,1884,1979,2073,2175,2270,2367,2465,2561,2654,2734,2840,2939,3035,3140,3243,3345,3499,3601,18591"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7becb12098085a58be85c10a694bf84d\\transformed\\material-1.12.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,266,365,457,538,640,720,818,940,1019,1078,1141,1233,1297,1357,1449,1514,1577,1639,1706,1770,1824,1929,1988,2049,2103,2172,2291,2374,2451,2541,2625,2709,2845,2924,3008,3130,3216,3294,3348,3399,3465,3534,3608,3679,3755,3827,3904,3975,4049,4160,4251,4330,4417,4505,4577,4651,4736,4787,4866,4933,5014,5098,5160,5224,5287,5355,5462,5561,5660,5755,5813,5868,5946,6027,6106", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,98,91,80,101,79,97,121,78,58,62,91,63,59,91,64,62,61,66,63,53,104,58,60,53,68,118,82,76,89,83,83,135,78,83,121,85,77,53,50,65,68,73,70,75,71,76,70,73,110,90,78,86,87,71,73,84,50,78,66,80,83,61,63,62,67,106,98,98,94,57,54,77,80,78,87", "endOffsets": "261,360,452,533,635,715,813,935,1014,1073,1136,1228,1292,1352,1444,1509,1572,1634,1701,1765,1819,1924,1983,2044,2098,2167,2286,2369,2446,2536,2620,2704,2840,2919,3003,3125,3211,3289,3343,3394,3460,3529,3603,3674,3750,3822,3899,3970,4044,4155,4246,4325,4412,4500,4572,4646,4731,4782,4861,4928,5009,5093,5155,5219,5282,5350,5457,5556,5655,5750,5808,5863,5941,6022,6101,6189"}, "to": {"startLines": "19,51,52,53,54,55,63,64,65,87,88,140,152,155,157,158,159,160,161,162,163,164,165,166,167,168,169,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,224,225,226", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "769,3679,3778,3870,3951,4053,4861,4959,5081,7644,7703,11581,13057,13270,13397,13489,13554,13617,13679,13746,13810,13864,13969,14028,14089,14143,14212,14549,14632,14709,14799,14883,14967,15103,15182,15266,15388,15474,15552,15606,15657,15723,15792,15866,15937,16013,16085,16162,16233,16307,16418,16509,16588,16675,16763,16835,16909,16994,17045,17124,17191,17272,17356,17418,17482,17545,17613,17720,17819,17918,18013,18071,18126,18596,18677,18756", "endLines": "22,51,52,53,54,55,63,64,65,87,88,140,152,155,157,158,159,160,161,162,163,164,165,166,167,168,169,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,224,225,226", "endColumns": "12,98,91,80,101,79,97,121,78,58,62,91,63,59,91,64,62,61,66,63,53,104,58,60,53,68,118,82,76,89,83,83,135,78,83,121,85,77,53,50,65,68,73,70,75,71,76,70,73,110,90,78,86,87,71,73,84,50,78,66,80,83,61,63,62,67,106,98,98,94,57,54,77,80,78,87", "endOffsets": "930,3773,3865,3946,4048,4128,4954,5076,5155,7698,7761,11668,13116,13325,13484,13549,13612,13674,13741,13805,13859,13964,14023,14084,14138,14207,14326,14627,14704,14794,14878,14962,15098,15177,15261,15383,15469,15547,15601,15652,15718,15787,15861,15932,16008,16080,16157,16228,16302,16413,16504,16583,16670,16758,16830,16904,16989,17040,17119,17186,17267,17351,17413,17477,17540,17608,17715,17814,17913,18008,18066,18121,18199,18672,18751,18839"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\88b9f6ad7ed87d7d30486cdd2fcbb5f7\\transformed\\biometric-1.1.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,164,250,371,510,640,762,891,1027,1132,1284,1436", "endColumns": "108,85,120,138,129,121,128,135,104,151,151,126", "endOffsets": "159,245,366,505,635,757,886,1022,1127,1279,1431,1558"}, "to": {"startLines": "85,86,141,142,143,144,145,146,147,148,149,150", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7449,7558,11673,11794,11933,12063,12185,12314,12450,12555,12707,12859", "endColumns": "108,85,120,138,129,121,128,135,104,151,151,126", "endOffsets": "7553,7639,11789,11928,12058,12180,12309,12445,12550,12702,12854,12981"}}]}]}