import { PoseDetectionService } from '../services/poseDetectionService';
import { realTimePostureService } from '../services/realTimePostureService';
import { performanceMonitor } from '../services/performanceMonitorService';
import { PoseKeyPoint, ARPoseDetection } from '../types';

// Mock MediaPipe for testing
jest.mock('@mediapipe/tasks-vision', () => ({
  FilesetResolver: {
    forVisionTasks: jest.fn().mockResolvedValue({}),
  },
  PoseLandmarker: {
    createFromOptions: jest.fn().mockResolvedValue({
      detectForVideo: jest.fn().mockReturnValue({
        landmarks: [[]],
        worldLandmarks: [[]],
      }),
    }),
  },
}));

describe('Production Pose Detection System', () => {
  let poseService: PoseDetectionService;
  
  beforeEach(() => {
    poseService = PoseDetectionService.getInstance({
      debugMode: true,
      targetFPS: 30,
      minDetectionConfidence: 0.7,
    });
  });

  afterEach(() => {
    poseService.stopCamera();
    realTimePostureService.stopMonitoring();
    performanceMonitor.stopMonitoring();
  });

  describe('PoseDetectionService', () => {
    test('should initialize successfully', async () => {
      await expect(poseService.initialize()).resolves.not.toThrow();
    });

    test('should handle camera start/stop', async () => {
      const mockCallback = jest.fn();
      
      await poseService.startCamera(mockCallback);
      expect(mockCallback).toBeDefined();
      
      poseService.stopCamera();
      // Should not crash
    });

    test('should analyze posture correctly', () => {
      const mockLandmarks: PoseKeyPoint[] = [
        { name: 'nose', x: 0.5, y: 0.15, z: 0, visibility: 0.9 },
        { name: 'left_shoulder', x: 0.4, y: 0.32, z: 0, visibility: 0.9 },
        { name: 'right_shoulder', x: 0.6, y: 0.32, z: 0, visibility: 0.9 },
        { name: 'left_hip', x: 0.45, y: 0.65, z: 0, visibility: 0.9 },
        { name: 'right_hip', x: 0.55, y: 0.65, z: 0, visibility: 0.9 },
      ];

      const analysis = PoseDetectionService.analyzePosture(mockLandmarks, 'test_user');
      
      expect(analysis).toBeDefined();
      expect(analysis.overallScore).toBeGreaterThanOrEqual(0);
      expect(analysis.overallScore).toBeLessThanOrEqual(100);
      expect(analysis.landmarks).toEqual(mockLandmarks);
    });

    test('should detect forward head posture', () => {
      const mockLandmarks: PoseKeyPoint[] = [
        { name: 'nose', x: 0.6, y: 0.15, z: 0, visibility: 0.9 }, // Forward head
        { name: 'left_shoulder', x: 0.4, y: 0.32, z: 0, visibility: 0.9 },
        { name: 'right_shoulder', x: 0.6, y: 0.32, z: 0, visibility: 0.9 },
        { name: 'left_hip', x: 0.45, y: 0.65, z: 0, visibility: 0.9 },
        { name: 'right_hip', x: 0.55, y: 0.65, z: 0, visibility: 0.9 },
      ];

      const analysis = PoseDetectionService.analyzePosture(mockLandmarks, 'test_user');
      
      expect(analysis.issues).toBeDefined();
      expect(analysis.issues?.some(issue => issue.type === 'forward_head')).toBe(true);
      expect(analysis.overallScore).toBeLessThan(90); // Should be penalized
    });

    test('should detect uneven shoulders', () => {
      const mockLandmarks: PoseKeyPoint[] = [
        { name: 'nose', x: 0.5, y: 0.15, z: 0, visibility: 0.9 },
        { name: 'left_shoulder', x: 0.4, y: 0.25, z: 0, visibility: 0.9 }, // Higher
        { name: 'right_shoulder', x: 0.6, y: 0.35, z: 0, visibility: 0.9 }, // Lower
        { name: 'left_hip', x: 0.45, y: 0.65, z: 0, visibility: 0.9 },
        { name: 'right_hip', x: 0.55, y: 0.65, z: 0, visibility: 0.9 },
      ];

      const analysis = PoseDetectionService.analyzePosture(mockLandmarks, 'test_user');
      
      expect(analysis.issues?.some(issue => issue.type === 'uneven_shoulders')).toBe(true);
    });
  });

  describe('RealTimePostureService', () => {
    test('should start and stop monitoring', () => {
      realTimePostureService.startMonitoring();
      expect(realTimePostureService.getCurrentMetrics().totalAnalyses).toBe(0);
      
      realTimePostureService.stopMonitoring();
      // Should not crash
    });

    test('should analyze real-time detection', () => {
      const mockDetection: ARPoseDetection = {
        landmarks: [
          { name: 'nose', x: 0.5, y: 0.15, z: 0, visibility: 0.9 },
          { name: 'left_shoulder', x: 0.4, y: 0.32, z: 0, visibility: 0.9 },
          { name: 'right_shoulder', x: 0.6, y: 0.32, z: 0, visibility: 0.9 },
        ],
        confidence: 0.85,
        timestamp: Date.now(),
      };

      realTimePostureService.startMonitoring();
      const analysis = realTimePostureService.analyzeRealTime(mockDetection, 'test_user');
      
      expect(analysis).toBeDefined();
      if (analysis) {
        expect(analysis.overallScore).toBeGreaterThanOrEqual(0);
        expect(analysis.overallScore).toBeLessThanOrEqual(100);
      }
    });

    test('should track metrics correctly', () => {
      realTimePostureService.startMonitoring();
      realTimePostureService.reset();
      
      const metrics = realTimePostureService.getCurrentMetrics();
      expect(metrics.totalAnalyses).toBe(0);
      expect(metrics.currentScore).toBe(0);
      expect(metrics.trendDirection).toBe('stable');
    });
  });

  describe('PerformanceMonitorService', () => {
    test('should monitor performance metrics', () => {
      performanceMonitor.startMonitoring(100); // Fast interval for testing
      
      // Simulate some metrics
      performanceMonitor.updateMetric('frameRate', 30);
      performanceMonitor.updateMetric('processingTime', 25);
      
      const metrics = performanceMonitor.getCurrentMetrics();
      expect(metrics.frameRate).toBe(30);
      expect(metrics.processingTime).toBe(25);
      
      performanceMonitor.stopMonitoring();
    });

    test('should provide performance summary', () => {
      const summary = performanceMonitor.getPerformanceSummary();
      
      expect(summary).toHaveProperty('current');
      expect(summary).toHaveProperty('averages');
      expect(summary).toHaveProperty('alerts');
      expect(summary).toHaveProperty('isOptimal');
    });
  });

  describe('Integration Tests', () => {
    test('should handle complete pose detection pipeline', async () => {
      const detections: ARPoseDetection[] = [];
      const analyses: any[] = [];

      const handleDetection = (detection: ARPoseDetection) => {
        detections.push(detection);
      };

      const handleAnalysis = (analysis: any) => {
        analyses.push(analysis);
      };

      // Start services
      realTimePostureService.startMonitoring();
      performanceMonitor.startMonitoring();
      
      await poseService.startCamera(handleDetection);
      
      // Wait for some processing
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Stop services
      poseService.stopCamera();
      realTimePostureService.stopMonitoring();
      performanceMonitor.stopMonitoring();
      
      // Verify pipeline worked
      expect(detections.length).toBeGreaterThan(0);
    });

    test('should maintain performance under load', async () => {
      const startTime = Date.now();
      const detections: ARPoseDetection[] = [];
      
      performanceMonitor.startMonitoring();
      
      await poseService.startCamera((detection) => {
        detections.push(detection);
      });
      
      // Run for 2 seconds
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      poseService.stopCamera();
      const endTime = Date.now();
      
      const metrics = performanceMonitor.getCurrentMetrics();
      const duration = endTime - startTime;
      const expectedDetections = Math.floor(duration / 33); // ~30 FPS
      
      // Should maintain reasonable performance
      expect(detections.length).toBeGreaterThan(expectedDetections * 0.8); // Allow 20% tolerance
      expect(metrics.frameRate).toBeGreaterThan(20); // Should maintain >20 FPS
      
      performanceMonitor.stopMonitoring();
    });
  });
});
