import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  RefreshControl,
  ActivityIndicator,
  Alert,
  StatusBar,
  Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useNavigation } from '@react-navigation/native';
import { CompositeNavigationProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { BottomTabNavigationProp } from '@react-navigation/bottom-tabs';
import { useAuth } from '../contexts/AuthContext';
import { useAlert } from '../contexts/AlertContext';
import { useGuest } from '../contexts/GuestContext';
import SignupPrompt from '../components/SignupPrompt';
import { RootStackParamList, MainTabParamList, UserAnalytics, PostureAnalysis, YogaSession } from '../types';
import { dataService } from '../services/dataService';
import { offlineSyncService } from '../services/offlineSync';
import { notificationService } from '../services/notificationService';
import { postureAnalysisService, PostureAnalysisStats } from '../services/postureAnalysisService';
import { logger } from '../utils/logger';

type HomeScreenNavigationProp = CompositeNavigationProp<
  BottomTabNavigationProp<MainTabParamList, 'Home'>,
  StackNavigationProp<RootStackParamList>
>;

const { width } = Dimensions.get('window');

interface DailyTip {
  id: string;
  text: string;
  textHindi: string;
}

const HomeScreen: React.FC = () => {
  const { t, i18n } = useTranslation();
  const navigation = useNavigation<HomeScreenNavigationProp>();
  const { user } = useAuth();
  const { showAlert } = useAlert();
  const {
    guestData,
    canUsePostureCheck,
    canUseYogaExercises,
    shouldShowSignupPrompt,
    markSignupPromptSeen,
    getFeatureLimitMessage
  } = useGuest();

  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Real-time data states
  const [analytics, setAnalytics] = useState<UserAnalytics | null>(null);
  const [recentAnalyses, setRecentAnalyses] = useState<PostureAnalysis[]>([]);
  const [recentSessions, setRecentSessions] = useState<YogaSession[]>([]);
  const [postureStats, setPostureStats] = useState<PostureAnalysisStats | null>(null);

  // Subscription cleanup
  const [unsubscribers, setUnsubscribers] = useState<(() => void)[]>([]);

  // Signup prompt state
  const [showSignupPrompt, setShowSignupPrompt] = useState(false);
  const [signupPromptData, setSignupPromptData] = useState({
    title: '',
    message: '',
    feature: '',
  });

  const currentHour = new Date().getHours();
  const timeOfDay = currentHour < 12 ? 'morning' : currentHour < 17 ? 'afternoon' : 'evening';

  const dailyTips: DailyTip[] = [
    {
      id: '1',
      text: 'Keep your screen at eye level to avoid neck strain',
      textHindi: 'गर्दन के तनाव से बचने के लिए अपनी स्क्रीन को आंखों के स्तर पर रखें',
    },
    {
      id: '2',
      text: 'Take a 2-minute posture break every 30 minutes',
      textHindi: 'हर 30 मिनट में 2 मिनट का पोस्चर ब्रेक लें',
    },
    {
      id: '3',
      text: 'Sit with your feet flat on the floor',
      textHindi: 'अपने पैरों को फर्श पर सपाट रखकर बैठें',
    },
  ];

  const [currentTip] = useState(dailyTips[Math.floor(Math.random() * dailyTips.length)]);

  // Helper function to get score color
  const getScoreColor = (score: number) => {
    if (score >= 90) return '#10B981';
    if (score >= 75) return '#F59E0B';
    if (score >= 60) return '#EF4444';
    return '#DC2626';
  };

  /**
   * Load real-time data from Firebase
   */
  const loadData = useCallback(async () => {
    // For guest users, no data to load
    if (!user) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Try to load from cache first for offline support
      const cachedAnalyses = await offlineSyncService.getCachedData<PostureAnalysis[]>(`recent_analyses_${user.id}`);
      const cachedSessions = await offlineSyncService.getCachedData<YogaSession[]>(`recent_sessions_${user.id}`);

      if (cachedAnalyses) {
        setRecentAnalyses(cachedAnalyses);
      }
      if (cachedSessions) {
        setRecentSessions(cachedSessions);
      }

      // Load fresh data and update cache
      try {
        const [analyticsData, analysesData, sessionsData] = await Promise.all([
          loadUserAnalytics(),
          dataService.getPostureAnalyses(user.id, 10),
          dataService.getYogaSessions(user.id, 5),
        ]);

        setAnalytics(analyticsData);
        setRecentAnalyses(analysesData);
        setRecentSessions(sessionsData);

        // Load posture stats
        try {
          const stats = await postureAnalysisService.getUserStats(user.id);
          setPostureStats(stats);
        } catch (error) {
          console.error('Error loading posture stats:', error);
        }

        // Cache the fresh data
        await offlineSyncService.cacheData(`recent_analyses_${user.id}`, analysesData);
        await offlineSyncService.cacheData(`recent_sessions_${user.id}`, sessionsData);
      } catch (onlineError) {
        logger.warn('Failed to load fresh data, using cached data if available', onlineError, 'HomeScreen');

        // If we don't have cached data, get offline data
        if (!cachedAnalyses) {
          const offlineAnalyses = await offlineSyncService.getOfflinePostureAnalyses(user.id);
          setRecentAnalyses(offlineAnalyses);
        }

        // Show user-friendly message about offline mode
        if (!cachedAnalyses && !cachedSessions) {
          setError('You\'re currently offline. Some data may not be up to date.');
        }
      }

      logger.info('Home screen data loaded successfully', {
        analyticsLoaded: !!analytics,
        analysesCount: recentAnalyses.length,
        sessionsCount: recentSessions.length,
      }, 'HomeScreen');

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load data';
      setError(errorMessage);
      logger.error('HomeScreen component failed to load data with details: ${error}', error, 'HomeScreen');

      // Show error to user
      showAlert({
        title: t('common.error'),
        message: errorMessage,
        type: 'error',
        buttons: [{ text: t('common.retry'), onPress: loadData }],
      });
    } finally {
      setLoading(false);
    }
  }, [user, t]);

  /**
   * Load user analytics with real-time subscription
   */
  const loadUserAnalytics = useCallback(async (): Promise<UserAnalytics | null> => {
    if (!user) return null;

    try {
      // Subscribe to real-time analytics updates
      const unsubscribe = dataService.subscribeToPostureAnalytics(
        user.id,
        (analyticsData) => {
          setAnalytics(analyticsData);
          logger.debug('Analytics updated via subscription', analyticsData, 'HomeScreen');
        }
      );

      setUnsubscribers(prev => [...prev, unsubscribe]);

      // Return initial analytics (will be updated via subscription)
      return analytics;
    } catch (error) {
      logger.error('Failed to subscribe to analytics', error, 'HomeScreen');
      return null;
    }
  }, [user, analytics]);

  /**
   * Handle pull-to-refresh
   */
  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await loadData();
    } finally {
      setRefreshing(false);
    }
  }, [loadData]);

  /**
   * Format time ago
   */
  const formatTimeAgo = (timestamp: any) => {
    if (!timestamp) return '';

    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);

    if (diffDays > 0) {
      return i18n.language === 'hi' ? `${diffDays} दिन पहले` : `${diffDays} days ago`;
    } else if (diffHours > 0) {
      return i18n.language === 'hi' ? `${diffHours} घंटे पहले` : `${diffHours} hours ago`;
    } else {
      return i18n.language === 'hi' ? 'अभी' : 'Just now';
    }
  };

  const getGreeting = () => {
    const greetings = {
      morning: i18n.language === 'hi' ? 'सुप्रभात' : 'Good morning',
      afternoon: i18n.language === 'hi' ? 'नमस्कार' : 'Good afternoon',
      evening: i18n.language === 'hi' ? 'शुभ संध्या' : 'Good evening',
    };
    return greetings[timeOfDay as keyof typeof greetings];
  };

  const getScoreText = (score: number) => {
    if (score >= 90) return t('postureCheck.excellent');
    if (score >= 75) return t('postureCheck.good');
    if (score >= 60) return t('postureCheck.fair');
    return t('postureCheck.poor');
  };

  /**
   * Handle posture check press
   */
  const handlePostureCheckPress = () => {
    if (!user && !canUsePostureCheck) {
      setSignupPromptData({
        title: 'Unlock Unlimited Posture Checks',
        message: getFeatureLimitMessage('posture_check'),
        feature: 'posture_check',
      });
      setShowSignupPrompt(true);
      return;
    }

    navigation.navigate('PostureCheck');

    // Check if should show signup prompt after navigation
    if (!user && shouldShowSignupPrompt()) {
      setTimeout(() => {
        setSignupPromptData({
          title: 'You\'re Making Great Progress!',
          message: 'Sign up to save your progress and unlock unlimited features.',
          feature: 'posture_check',
        });
        setShowSignupPrompt(true);
        markSignupPromptSeen();
      }, 2000);
    }
  };

  /**
   * Handle yoga session press
   */
  const handleYogaSessionPress = () => {
    if (!user && !canUseYogaExercises) {
      setSignupPromptData({
        title: 'Continue Your Wellness Journey',
        message: getFeatureLimitMessage('yoga_exercises'),
        feature: 'yoga_exercises',
      });
      setShowSignupPrompt(true);
      return;
    }

    navigation.navigate('YogaSession', {});

    // Check if should show signup prompt after navigation
    if (!user && shouldShowSignupPrompt()) {
      setTimeout(() => {
        setSignupPromptData({
          title: 'Great Session!',
          message: 'Create an account to track your progress and unlock personalized routines.',
          feature: 'yoga_exercises',
        });
        setShowSignupPrompt(true);
        markSignupPromptSeen();
      }, 2000);
    }
  };

  /**
   * Handle progress navigation
   */
  const handleProgressPress = () => {
    if (!user) {
      setSignupPromptData({
        title: 'Track Your Progress',
        message: getFeatureLimitMessage('progress'),
        feature: 'progress',
      });
      setShowSignupPrompt(true);
      return;
    }

    navigation.navigate('Progress');
  };

  /**
   * Initialize data loading and subscriptions
   */
  useEffect(() => {
    loadData(); // Load data for both authenticated and guest users

    // Cleanup subscriptions on unmount
    return () => {
      unsubscribers.forEach(unsubscribe => unsubscribe());
      if (user) {
        dataService.cleanup();
      }
    };
  }, [user, loadData]);

  /**
   * Handle user changes
   */
  useEffect(() => {
    if (user) {
      logger.setUserId(user.id);
    }
  }, [user]);

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#000000" />

      {/* Background Gradient */}
      <LinearGradient
        colors={['#000000', '#1a1a1a', '#2d2d2d']}
        style={StyleSheet.absoluteFillObject}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      />

      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <View>
            <Text style={styles.greeting}>
              {getGreeting()}, {user?.name?.split(' ')[0] || 'Guest'}
            </Text>
            <Text style={styles.date}>
              {new Date().toLocaleDateString(i18n.language === 'hi' ? 'hi-IN' : 'en-IN', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
              })}
            </Text>
          </View>
          <TouchableOpacity style={styles.profileButton}>
            <View style={styles.profileIconWrapper}>
              <Ionicons name="person" size={24} color="#FFFFFF" />
            </View>
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={['#FFFFFF']}
            tintColor="#FFFFFF"
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Loading State */}
        {loading && (
          <View style={styles.loadingCard}>
            <ActivityIndicator size="large" color="#FFFFFF" />
            <Text style={styles.loadingText}>
              {i18n.language === 'hi' ? 'डेटा लोड हो रहा है...' : 'Loading your data...'}
            </Text>
          </View>
        )}

        {/* Error State */}
        {error && !loading && (
          <View style={styles.errorCard}>
            <Ionicons name="alert-circle" size={24} color="#FF3B30" />
            <Text style={styles.errorText}>{error}</Text>
            <TouchableOpacity style={styles.retryButton} onPress={loadData}>
              <Text style={styles.retryButtonText}>{t('common.retry')}</Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Guest Mode Banner */}
        {!user && (
          <TouchableOpacity
            style={styles.guestBanner}
            onPress={() => {
              setSignupPromptData({
                title: 'Unlock Your Full Potential',
                message: 'Create a free account to access unlimited features and track your wellness journey.',
                feature: 'signup',
              });
              setShowSignupPrompt(true);
            }}
            activeOpacity={0.8}
          >
            <View style={styles.guestBannerContent}>
              <View style={styles.guestBannerIcon}>
                <Ionicons name="star" size={20} color="#FFD700" />
              </View>
              <View style={styles.guestBannerText}>
                <Text style={styles.guestBannerTitle}>
                  {i18n.language === 'hi' ? 'मुफ्त खाता बनाएं' : 'Create Free Account'}
                </Text>
                <Text style={styles.guestBannerSubtitle}>
                  {i18n.language === 'hi'
                    ? 'असीमित सुविधाओं को अनलॉक करें'
                    : 'Unlock unlimited features & progress tracking'}
                </Text>
              </View>
              <Ionicons name="arrow-forward" size={16} color="rgba(255, 255, 255, 0.8)" />
            </View>
          </TouchableOpacity>
        )}

        {/* Posture Score Card */}
        {!loading && !error && (user ? analytics : true) && (
          <View style={styles.scoreCard}>
            <Text style={styles.scoreTitle}>{t('home.postureScore')}</Text>
            <View style={styles.scoreContainer}>
              <View style={styles.scoreCircle}>
                <Text style={[styles.scoreNumber, { color: getScoreColor(user ? (analytics?.totalSessions === 0 ? 0 : Math.round(analytics?.averageScore || 0)) : 85) }]}>
                  {user ? (analytics?.totalSessions === 0 ? 0 : Math.round(analytics?.averageScore || 0)) : 0}
                </Text>
                <Text style={styles.scoreOutOf}>/100</Text>
              </View>
              <View style={styles.scoreInfo}>
                <Text style={[styles.scoreStatus, { color: getScoreColor(user ? (analytics?.totalSessions === 0 ? 0 : Math.round(analytics?.averageScore || 0)) : 85) }]}>
                  {user ? (analytics?.totalSessions === 0 ? (i18n.language === 'hi' ? 'शुरू करें' : 'Get Started') : getScoreText(Math.round(analytics?.averageScore || 0))) : (i18n.language === 'hi' ? 'साइन अप करें' : 'Sign Up to Track')}
                </Text>
                <Text style={styles.scoreDescription}>
                  {user
                    ? (analytics?.totalSessions === 0
                      ? (i18n.language === 'hi'
                        ? 'अपना पहला पोस्चर चेक करें'
                        : 'Take your first posture check')
                      : (i18n.language === 'hi'
                        ? `${analytics?.totalSessions} सत्रों का औसत`
                        : `Average from ${analytics?.totalSessions} sessions`))
                    : (i18n.language === 'hi'
                      ? 'बेहतर ट्रैकिंग के लिए साइन अप करें'
                      : 'Sign up for personalized tracking')
                  }
                </Text>
              </View>
            </View>
          </View>
        )}

        {/* Posture Stats Widget - Only show for authenticated users */}
        {user && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>
                {i18n.language === 'hi' ? 'मुद्रा सांख्यिकी' : 'Posture Stats'}
              </Text>
              <TouchableOpacity
                onPress={() => navigation.navigate('PostureHistory')}
                style={styles.viewAllButton}
              >
                <Text style={styles.viewAllText}>
                  {i18n.language === 'hi' ? 'सभी देखें' : 'View All'}
                </Text>
                <Ionicons name="chevron-forward" size={16} color="#6366F1" />
              </TouchableOpacity>
            </View>

            <View style={styles.postureStatsCard}>
              <View style={styles.statsRow}>
                <View style={styles.statItem}>
                  <Text style={styles.statValue}>
                    {postureStats?.totalAnalyses || 0}
                  </Text>
                  <Text style={styles.postureStatLabel}>
                    {i18n.language === 'hi' ? 'कुल विश्लेषण' : 'Total Analyses'}
                  </Text>
                </View>
                <View style={styles.statItem}>
                  <Text style={[styles.statValue, { color: getScoreColor(postureStats?.averageScore || 0) }]}>
                    {postureStats?.averageScore || 0}
                  </Text>
                  <Text style={styles.postureStatLabel}>
                    {i18n.language === 'hi' ? 'औसत स्कोर' : 'Average Score'}
                  </Text>
                </View>
                <View style={styles.statItem}>
                  <Text style={[
                    styles.statValue,
                    { color: (postureStats?.improvementTrend || 0) >= 0 ? '#10B981' : '#EF4444' }
                  ]}>
                    {(postureStats?.improvementTrend || 0) >= 0 ? '+' : ''}{postureStats?.improvementTrend || 0}%
                  </Text>
                  <Text style={styles.postureStatLabel}>
                    {i18n.language === 'hi' ? 'सुधार' : 'Improvement'}
                  </Text>
                </View>
              </View>

              {postureStats?.totalAnalyses === 0 && (
                <TouchableOpacity
                  style={styles.firstAnalysisPrompt}
                  onPress={() => navigation.navigate('PostureCheck')}
                >
                  <Ionicons name="analytics-outline" size={24} color="#6366F1" />
                  <Text style={styles.firstAnalysisText}>
                    {i18n.language === 'hi'
                      ? 'अपना पहला मुद्रा विश्लेषण करें'
                      : 'Perform your first posture analysis'
                    }
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          </View>
        )}

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('home.quickActions', 'Quick Actions')}</Text>
          <View style={styles.actionGrid}>
            <TouchableOpacity
              style={styles.actionCard}
              onPress={() => handlePostureCheckPress()}
              activeOpacity={0.8}
            >
              <View style={[styles.actionIcon, { backgroundColor: '#FFFFFF' }]}>
                <Ionicons name="camera" size={24} color="#000000" />
              </View>
              <Text style={styles.actionTitle}>{t('home.quickCheck')}</Text>
              <Text style={styles.actionSubtitle}>
                {i18n.language === 'hi' ? '2 मिनट' : '2 min'}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionCard}
              onPress={() => handleYogaSessionPress()}
              activeOpacity={0.8}
            >
              <View style={[styles.actionIcon, { backgroundColor: '#34C759' }]}>
                <Ionicons name="fitness" size={24} color="#FFFFFF" />
              </View>
              <Text style={styles.actionTitle}>{t('home.startYoga')}</Text>
              <Text style={styles.actionSubtitle}>
                {i18n.language === 'hi' ? '15 मिनट' : '15 min'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Stats Row */}
        <View style={styles.statsContainer}>
          <View style={styles.statCard}>
            <Ionicons name="camera" size={24} color="#FFFFFF" />
            <Text style={styles.statNumber}>
              {user
                ? `${analytics?.streakDays || 3}`
                : `${guestData.postureChecksUsed}/3`
              }
            </Text>
            <Text style={styles.statLabel}>
              {user
                ? (i18n.language === 'hi' ? 'दिन की लगातार अभ्यास' : 'Day Streak')
                : (i18n.language === 'hi' ? 'पोस्चर चेक' : 'Posture Checks')
              }
            </Text>
          </View>

          <View style={styles.statCard}>
            <Ionicons name="fitness" size={24} color="#34C759" />
            <Text style={styles.statNumber}>
              {user
                ? `${(analytics?.improvementRate || 12) > 0 ? '+' : ''}${(analytics?.improvementRate || 12).toFixed(1)}%`
                : `${guestData.yogaSessionsCompleted}/2`
              }
            </Text>
            <Text style={styles.statLabel}>
              {user
                ? (i18n.language === 'hi' ? 'सुधार दर' : 'Improvement')
                : (i18n.language === 'hi' ? 'योग सत्र' : 'Yoga Sessions')
              }
            </Text>
          </View>

          <View style={styles.statCard}>
            <Ionicons name={user ? "trophy" : "person-add"} size={24} color="#FFD700" />
            <Text style={styles.statNumber}>
              {user
                ? `${analytics?.totalSessions || 5}`
                : '∞'
              }
            </Text>
            <Text style={styles.statLabel}>
              {user
                ? (i18n.language === 'hi' ? 'कुल सत्र' : 'Total Sessions')
                : (i18n.language === 'hi' ? 'साइन अप करें' : 'Sign Up')
              }
            </Text>
          </View>
        </View>

        {/* Daily Tip */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('home.tips')}</Text>
          <View style={styles.tipCard}>
            <View style={styles.tipHeader}>
              <Ionicons name="bulb" size={20} color="#FFD700" />
              <Text style={styles.tipTitle}>
                {i18n.language === 'hi' ? 'आज का सुझाव' : 'Tip of the Day'}
              </Text>
            </View>
            <Text style={styles.tipText}>
              {i18n.language === 'hi' ? currentTip.textHindi : currentTip.text}
            </Text>
          </View>
        </View>

        {/* Recent Activity */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>{t('home.recentActivity')}</Text>
            <TouchableOpacity onPress={handleProgressPress}>
              <Text style={styles.viewAllText}>{t('home.viewProgress')}</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.activityCard}>
            {/* Show recent analyses */}
            {recentAnalyses.length > 0 ? (
              recentAnalyses.slice(0, 1).map((analysis, index) => (
                <View key={`analysis-${index}`} style={styles.activityItem}>
                  <View style={styles.activityIcon}>
                    <Ionicons name="camera" size={16} color="#FFFFFF" />
                  </View>
                  <View style={styles.activityContent}>
                    <Text style={styles.activityTitle}>
                      {i18n.language === 'hi' ? 'पोस्चर जांच पूरी' : 'Posture Check Completed'}
                    </Text>
                    <Text style={styles.activityTime}>
                      {formatTimeAgo(analysis.createdAt)}
                    </Text>
                  </View>
                  <Text style={styles.activityScore}>{Math.round(analysis.overallScore)}/100</Text>
                </View>
              ))
            ) : (
              <View style={styles.activityItem}>
                <View style={styles.activityIcon}>
                  <Ionicons name="help-circle" size={16} color="#64748B" />
                </View>
                <View style={styles.activityContent}>
                  <Text style={styles.activityTitle}>
                    {i18n.language === 'hi' ? 'कोई गतिविधि नहीं' : 'No Activity Yet'}
                  </Text>
                  <Text style={styles.activityTime}>
                    {i18n.language === 'hi' ? 'अपना पहला चेक करें' : 'Perform your first check'}
                  </Text>
                </View>
                <Text style={styles.activityScore}>--</Text>
              </View>
            )}

            {/* Show recent sessions */}
            {recentSessions.length > 0 ? (
              recentSessions.slice(0, 1).map((session, index) => (
                <View key={`session-${index}`} style={styles.activityItem}>
                  <View style={styles.activityIcon}>
                    <Ionicons name="fitness" size={16} color="#34C759" />
                  </View>
                  <View style={styles.activityContent}>
                    <Text style={styles.activityTitle}>
                      {i18n.language === 'hi' ? 'योग सत्र पूरा' : 'Yoga Session Completed'}
                    </Text>
                    <Text style={styles.activityTime}>
                      {formatTimeAgo(session.createdAt)}
                    </Text>
                  </View>
                  <Text style={styles.activityScore}>{Math.round(session.duration / 60)} min</Text>
                </View>
              ))
            ) : null}


          </View>
        </View>

        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Signup Prompt */}
      <SignupPrompt
        visible={showSignupPrompt}
        onClose={() => setShowSignupPrompt(false)}
        title={signupPromptData.title}
        message={signupPromptData.message}
        feature={signupPromptData.feature}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  header: {
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
    paddingBottom: 20,
    paddingHorizontal: 24,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  greeting: {
    fontSize: 18,
    fontWeight: '300',
    color: '#FFFFFF',
    marginBottom: 4,
    letterSpacing: 0.5,
  },
  date: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.7)',
    fontWeight: '400',
  },
  profileButton: {
    padding: 8,
  },
  profileIconWrapper: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  content: {
    flex: 1,
  },
  scoreCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    marginHorizontal: 24,
    marginTop: 20,
    borderRadius: 20,
    padding: 24,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  scoreTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: 20,
    letterSpacing: 0.3,
  },
  scoreContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  scoreCircle: {
    alignItems: 'center',
    marginRight: 24,
  },
  scoreNumber: {
    fontSize: 48,
    fontWeight: '300',
    letterSpacing: -1,
  },
  scoreOutOf: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.6)',
    marginTop: -8,
  },
  scoreInfo: {
    flex: 1,
  },
  scoreStatus: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
    letterSpacing: 0.3,
  },
  scoreDescription: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.7)',
    lineHeight: 20,
  },
  section: {
    marginHorizontal: 24,
    marginTop: 32,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: 16,
    letterSpacing: 0.3,
  },
  viewAllText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    fontWeight: '500',
  },
  actionGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 16,
  },
  actionCard: {
    flex: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    padding: 20,
    borderRadius: 16,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  actionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  actionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 4,
    letterSpacing: 0.2,
  },
  actionSubtitle: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.7)',
  },
  statsContainer: {
    flexDirection: 'row',
    marginHorizontal: 24,
    marginTop: 32,
    gap: 12,
  },
  statCard: {
    flex: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    padding: 16,
    borderRadius: 16,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  statNumber: {
    fontSize: 20,
    fontWeight: '600',
    color: '#FFFFFF',
    marginTop: 8,
    marginBottom: 4,
    letterSpacing: 0.3,
  },
  statLabel: {
    fontSize: 11,
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
    lineHeight: 14,
  },
  loadingCard: {
    alignItems: 'center',
    paddingVertical: 40,
    marginHorizontal: 24,
    marginTop: 20,
  },
  loadingText: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 16,
    textAlign: 'center',
  },
  errorCard: {
    alignItems: 'center',
    paddingVertical: 32,
    marginHorizontal: 24,
    marginTop: 20,
    backgroundColor: 'rgba(255, 59, 48, 0.1)',
    borderRadius: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 59, 48, 0.3)',
  },
  errorText: {
    fontSize: 14,
    color: '#FF3B30',
    textAlign: 'center',
    marginVertical: 16,
    lineHeight: 20,
  },
  retryButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },
  tipCard: {
    backgroundColor: 'rgba(255, 215, 0, 0.1)',
    padding: 16,
    borderRadius: 16,
    borderLeftWidth: 4,
    borderLeftColor: '#FFD700',
    borderWidth: 1,
    borderColor: 'rgba(255, 215, 0, 0.3)',
  },
  tipHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  tipTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFD700',
    marginLeft: 8,
    letterSpacing: 0.2,
  },
  tipText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.9)',
    lineHeight: 20,
  },
  activityCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  activityIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  activityContent: {
    flex: 1,
  },
  activityTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#FFFFFF',
    marginBottom: 2,
    letterSpacing: 0.2,
  },
  activityTime: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.6)',
  },
  activityScore: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  bottomSpacing: {
    height: Platform.OS === 'ios' ? 122 : 102, // Tab bar height + extra spacing
  },

  guestBanner: {
    backgroundColor: 'rgba(255, 215, 0, 0.1)',
    marginHorizontal: 24,
    marginTop: 20,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 215, 0, 0.3)',
    padding: 16,
  },
  guestBannerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  guestBannerIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(255, 215, 0, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  guestBannerText: {
    flex: 1,
  },
  guestBannerTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFD700',
    marginBottom: 2,
    letterSpacing: 0.2,
  },
  guestBannerSubtitle: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.8)',
    lineHeight: 16,
  },
  // Posture Stats Widget Styles
  postureStatsCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#6366F1',
    marginBottom: 4,
  },
  postureStatLabel: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
  },
  firstAnalysisPrompt: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.1)',
  },
  firstAnalysisText: {
    fontSize: 14,
    color: '#6366F1',
    marginLeft: 8,
    fontWeight: '500',
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});

export default HomeScreen;