import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Animated,
  StatusBar,
  BackHandler,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useNavigation, useRoute, useFocusEffect } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { useAuth } from '../contexts/AuthContext';
import { useAlert } from '../contexts/AlertContext';
import { RootStackParamList, QuestionnaireQuestion, QuestionnaireSession, QuestionnaireResponse } from '../types';
import { enhancedQuestionnaireService } from '../services/enhancedQuestionnaireService';
import { questionnaireAnalytics } from '../services/questionnaireAnalytics';
import { offlineQuestionnaireManager } from '../services/offlineQuestionnaireManager';
import ProgressIndicator from '../components/questionnaire/ProgressIndicator';
import RatingScale from '../components/questionnaire/RatingScale';
import BodyMapSelector from '../components/questionnaire/BodyMapSelector';
import { Theme } from '../constants/designTokens';
import { logger } from '../utils/logger';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

type EnhancedQuestionnaireNavigationProp = StackNavigationProp<RootStackParamList, 'Questionnaire'>;

interface RouteParams {
  type?: 'onboarding' | 'periodic_update' | 'goal_reassessment' | 'health_check';
}

const EnhancedQuestionnaireScreen: React.FC = () => {
  const { t, i18n } = useTranslation();
  const navigation = useNavigation<EnhancedQuestionnaireNavigationProp>();
  const route = useRoute();
  const { user } = useAuth();
  const { showAlert } = useAlert();

  const routeParams = route.params as RouteParams;
  const sessionType = routeParams?.type || 'onboarding';

  // State management
  const [session, setSession] = useState<QuestionnaireSession | null>(null);
  const [currentQuestion, setCurrentQuestion] = useState<QuestionnaireQuestion | null>(null);
  const [currentAnswer, setCurrentAnswer] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [validationError, setValidationError] = useState<string | null>(null);
  const [questionStartTime, setQuestionStartTime] = useState<number>(Date.now());

  // Animation refs
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const progressAnim = useRef(new Animated.Value(0)).current;

  // Initialize questionnaire session
  useEffect(() => {
    initializeSession();
  }, []);

  // Handle back button
  useFocusEffect(
    React.useCallback(() => {
      const onBackPress = () => {
        handleBackNavigation();
        return true;
      };

      const subscription = BackHandler.addEventListener('hardwareBackPress', onBackPress);
      return () => subscription.remove();
    }, [session])
  );

  // Animate question transitions
  useEffect(() => {
    if (currentQuestion) {
      setQuestionStartTime(Date.now());
      
      // Reset animations
      fadeAnim.setValue(0);
      slideAnim.setValue(50);

      // Animate in
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();

      // Track engagement
      questionnaireAnalytics.trackEngagement(user?.id || '', 'question_focus', {
        questionId: currentQuestion.id,
        questionCategory: currentQuestion.category,
      });
    }
  }, [currentQuestion]);

  /**
   * Initialize questionnaire session
   */
  const initializeSession = async () => {
    try {
      if (!user) {
        // Wait for auth state to propagate
        let retryCount = 0;
        const maxRetries = 10; // 5 seconds total
        let currentUser = user;

        while (!currentUser && retryCount < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, 500));
          retryCount++;
          currentUser = user; // Re-check user state
        }

        if (!currentUser) {
          showAlert({
            title: t('common.error'),
            message: t('auth.loginRequired'),
            type: 'error',
            buttons: [{ text: t('common.ok'), onPress: () => navigation.goBack() }],
          });
          return;
        }
      }

      setIsLoading(true);

      // Start new session
      const newSession = await enhancedQuestionnaireService.startSession(user.id, sessionType);
      setSession(newSession);

      // Get first question
      const firstQuestion = enhancedQuestionnaireService.getNextQuestion([]);
      setCurrentQuestion(firstQuestion);

      // Track session start
      await questionnaireAnalytics.trackSessionStart(newSession);

      logger.info('Enhanced questionnaire session initialized', {
        sessionId: newSession.id,
        userId: user.id,
        sessionType,
        totalQuestions: newSession.totalQuestions,
      }, 'EnhancedQuestionnaireScreen');

    } catch (error) {
      logger.error('Failed to initialize questionnaire session', error, 'EnhancedQuestionnaireScreen');
      showAlert({
        title: t('common.error'),
        message: t('questionnaire.initError', 'Failed to start questionnaire. Please try again.'),
        type: 'error',
        buttons: [{ text: t('common.ok'), onPress: () => navigation.goBack() }],
      });
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Handle answer submission with validation
   */
  const handleAnswerSubmit = async () => {
    if (!session || !currentQuestion || !user) return;

    try {
      setIsSubmitting(true);
      setValidationError(null);

      // Validate answer
      if (currentQuestion.validation?.required && (currentAnswer === null || currentAnswer === undefined || currentAnswer === '')) {
        const errorMsg = i18n.language === 'hi' 
          ? currentQuestion.validation.errorMessageHindi || 'यह फ़ील्ड आवश्यक है'
          : currentQuestion.validation.errorMessage || 'This field is required';
        setValidationError(errorMsg);
        return;
      }

      // Calculate response time
      const responseTime = Date.now() - questionStartTime;

      // Submit response
      const updatedSession = await enhancedQuestionnaireService.submitResponse(
        session.id,
        currentQuestion.id,
        currentAnswer,
        5, // Default confidence level
        responseTime
      );

      setSession(updatedSession);

      // Track response
      const response: QuestionnaireResponse = {
        id: `response_${Date.now()}`,
        sessionId: session.id,
        userId: user.id,
        questionId: currentQuestion.id,
        questionCategory: currentQuestion.category,
        questionText: currentQuestion.title,
        answerValue: currentAnswer,
        answerData: {
          confidenceLevel: 5,
          responseTimeMs: responseTime,
          questionType: currentQuestion.type,
        },
        confidenceLevel: 5,
        responseTimeMs: responseTime,
        createdAt: new Date(),
      };

      await questionnaireAnalytics.trackResponse(response);

      // Check if questionnaire is complete
      if (updatedSession.completionPercentage >= 100) {
        await handleQuestionnaireComplete(updatedSession);
        return;
      }

      // Get next question
      const nextQuestion = enhancedQuestionnaireService.getNextQuestion(updatedSession.responses);
      
      if (nextQuestion) {
        // Animate out current question
        Animated.parallel([
          Animated.timing(fadeAnim, {
            toValue: 0,
            duration: 200,
            useNativeDriver: true,
          }),
          Animated.timing(slideAnim, {
            toValue: -50,
            duration: 200,
            useNativeDriver: true,
          }),
        ]).start(() => {
          setCurrentQuestion(nextQuestion);
          setCurrentAnswer(null);
        });
      } else {
        await handleQuestionnaireComplete(updatedSession);
      }

    } catch (error) {
      logger.error('Failed to submit questionnaire response', error, 'EnhancedQuestionnaireScreen');
      showAlert({
        title: t('common.error'),
        message: t('questionnaire.submitError', 'Failed to save your response. Please try again.'),
        type: 'error',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  /**
   * Handle questionnaire completion
   */
  const handleQuestionnaireComplete = async (completedSession: QuestionnaireSession) => {
    try {
      // Track completion
      await questionnaireAnalytics.trackSessionComplete(completedSession);

      // Show completion message
      showAlert({
        title: t('questionnaire.complete'),
        message: t('questionnaire.completeMessage', 'Thank you! Your personalized recommendations are ready.'),
        type: 'success',
        buttons: [
          { 
            text: t('questionnaire.viewRecommendations'), 
            onPress: () => {
              // Navigate to main screen
              navigation.replace('Main');
            }
          }
        ],
      });

    } catch (error) {
      logger.error('Failed to handle questionnaire completion', error, 'EnhancedQuestionnaireScreen');
    }
  };

  /**
   * Handle back navigation with confirmation
   */
  const handleBackNavigation = () => {
    if (!session || session.responses.length === 0) {
      navigation.goBack();
      return;
    }

    showAlert({
      title: t('questionnaire.exitConfirm'),
      message: t('questionnaire.exitMessage', 'Your progress will be saved. You can continue later.'),
      type: 'warning',
      buttons: [
        { text: t('common.cancel'), style: 'cancel' },
        { 
          text: t('questionnaire.saveAndExit'), 
          onPress: async () => {
            if (session) {
              await questionnaireAnalytics.trackSessionAbandoned(session, 'user_exit');
            }
            navigation.goBack();
          }
        },
      ],
    });
  };

  /**
   * Handle question skip
   */
  const handleSkip = async () => {
    if (!session || !currentQuestion || !user) return;

    await questionnaireAnalytics.trackQuestionSkip(
      session.id,
      user.id,
      currentQuestion.id,
      'user_skip'
    );

    // Submit 'skipped' as answer
    setCurrentAnswer('skipped');
    await handleAnswerSubmit();
  };

  /**
   * Render question based on type
   */
  const renderQuestion = () => {
    if (!currentQuestion) return null;

    const questionTitle = i18n.language === 'hi' && currentQuestion.titleHindi 
      ? currentQuestion.titleHindi 
      : currentQuestion.title;

    const questionDescription = i18n.language === 'hi' && currentQuestion.descriptionHindi 
      ? currentQuestion.descriptionHindi 
      : currentQuestion.description;

    return (
      <Animated.View
        style={[
          styles.questionContainer,
          {
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }],
          },
        ]}
      >
        <View style={styles.questionHeader}>
          <Text style={styles.questionTitle}>{questionTitle}</Text>
          {questionDescription && (
            <Text style={styles.questionDescription}>{questionDescription}</Text>
          )}
        </View>

        <View style={styles.questionContent}>
          {renderQuestionInput()}
        </View>

        {validationError && (
          <View style={styles.errorContainer}>
            <Ionicons name="alert-circle" size={16} color={Theme.colors.error[500]} />
            <Text style={styles.errorText}>{validationError}</Text>
          </View>
        )}
      </Animated.View>
    );
  };

  /**
   * Render question input based on type
   */
  const renderQuestionInput = () => {
    if (!currentQuestion) return null;

    switch (currentQuestion.type) {
      case 'info_display':
        return (
          <View style={styles.infoDisplay}>
            <Ionicons name="information-circle" size={48} color={Theme.colors.primary[500]} />
            <Text style={styles.infoText}>
              {i18n.language === 'hi' && currentQuestion.descriptionHindi 
                ? currentQuestion.descriptionHindi 
                : currentQuestion.description}
            </Text>
          </View>
        );

      case 'single_choice':
        return (
          <View style={styles.optionsContainer}>
            {currentQuestion.options?.map((option, index) => (
              <TouchableOpacity
                key={option.value}
                style={[
                  styles.optionButton,
                  currentAnswer === option.value && styles.selectedOption,
                ]}
                onPress={() => setCurrentAnswer(option.value)}
                activeOpacity={0.7}
              >
                {option.icon && (
                  <Text style={styles.optionIcon}>{option.icon}</Text>
                )}
                <View style={styles.optionTextContainer}>
                  <Text style={[
                    styles.optionText,
                    currentAnswer === option.value && styles.selectedOptionText,
                  ]}>
                    {i18n.language === 'hi' && option.labelHindi ? option.labelHindi : option.label}
                  </Text>
                </View>
                {currentAnswer === option.value && (
                  <Ionicons name="checkmark-circle" size={24} color="#4F46E5" />
                )}
              </TouchableOpacity>
            ))}
          </View>
        );

      case 'multiple_choice':
        return (
          <View style={styles.optionsContainer}>
            {currentQuestion.options?.map((option, index) => {
              const isSelected = Array.isArray(currentAnswer) && currentAnswer.includes(option.value);
              
              return (
                <TouchableOpacity
                  key={option.value}
                  style={[
                    styles.optionButton,
                    isSelected && styles.selectedOption,
                  ]}
                  onPress={() => {
                    const currentSelections = Array.isArray(currentAnswer) ? currentAnswer : [];
                    let newSelections;
                    
                    if (isSelected) {
                      newSelections = currentSelections.filter(val => val !== option.value);
                    } else {
                      // Check max selections
                      const maxSelections = currentQuestion.validation?.maxSelections;
                      if (maxSelections && currentSelections.length >= maxSelections) {
                        setValidationError(
                          i18n.language === 'hi' 
                            ? `अधिकतम ${maxSelections} विकल्प चुनें`
                            : `Select at most ${maxSelections} options`
                        );
                        return;
                      }
                      newSelections = [...currentSelections, option.value];
                    }
                    
                    setCurrentAnswer(newSelections);
                    setValidationError(null);
                  }}
                  activeOpacity={0.7}
                >
                  {option.icon && (
                    <Text style={styles.optionIcon}>{option.icon}</Text>
                  )}
                  <View style={styles.optionTextContainer}>
                    <Text style={[
                      styles.optionText,
                      isSelected && styles.selectedOptionText,
                    ]}>
                      {i18n.language === 'hi' && option.labelHindi ? option.labelHindi : option.label}
                    </Text>
                  </View>
                  <View style={[
                    styles.checkbox,
                    isSelected && styles.checkedBox,
                  ]}>
                    {isSelected && (
                      <Ionicons name="checkmark" size={16} color="#FFFFFF" />
                    )}
                  </View>
                </TouchableOpacity>
              );
            })}
          </View>
        );

      case 'rating_scale':
        return (
          <RatingScale
            min={currentQuestion.scaleMin || 1}
            max={currentQuestion.scaleMax || 5}
            step={currentQuestion.scaleStep || 1}
            value={currentAnswer}
            onValueChange={setCurrentAnswer}
            labels={i18n.language === 'hi' ? currentQuestion.scaleLabelsHindi : currentQuestion.scaleLabels}
            language={i18n.language}
          />
        );

      case 'body_map':
        return (
          <BodyMapSelector
            bodyAreas={currentQuestion.bodyAreas || []}
            selectedAreas={Array.isArray(currentAnswer) ? currentAnswer : []}
            onSelectionChange={setCurrentAnswer}
            language={i18n.language}
          />
        );

      default:
        return (
          <Text style={styles.errorText}>
            {t('questionnaire.unsupportedQuestionType', 'Unsupported question type')}
          </Text>
        );
    }
  };

  /**
   * Render action buttons
   */
  const renderActionButtons = () => {
    const canSkip = currentQuestion && !currentQuestion.required;
    const canContinue = currentQuestion?.type === 'info_display' || currentAnswer !== null;

    return (
      <View style={styles.actionButtonsContainer}>
        {canSkip && (
          <TouchableOpacity
            style={styles.skipButton}
            onPress={handleSkip}
            activeOpacity={0.7}
          >
            <Text style={styles.skipButtonText}>
              {t('questionnaire.skip', 'Skip')}
            </Text>
          </TouchableOpacity>
        )}

        <TouchableOpacity
          style={[
            styles.continueButton,
            !canContinue && styles.disabledButton,
          ]}
          onPress={handleAnswerSubmit}
          disabled={!canContinue || isSubmitting}
          activeOpacity={0.7}
        >
          <Text style={[
            styles.continueButtonText,
            !canContinue && styles.disabledButtonText,
          ]}>
            {isSubmitting 
              ? t('common.saving', 'Saving...')
              : session?.completionPercentage === 100
              ? t('questionnaire.finish', 'Finish')
              : t('common.continue', 'Continue')
            }
          </Text>
          {!isSubmitting && (
            <Ionicons 
              name="arrow-forward" 
              size={20} 
              color={canContinue ? Theme.colors.primary[600] : Theme.colors.neutral[400]} 
            />
          )}
        </TouchableOpacity>
      </View>
    );
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <Animated.View style={styles.loadingContent}>
          <Ionicons name="clipboard-outline" size={48} color={Theme.colors.primary[500]} />
          <Text style={styles.loadingText}>
            {t('questionnaire.loading', 'Preparing your questionnaire...')}
          </Text>
        </Animated.View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={Theme.colors.primary[600]} />
      
      <LinearGradient
        colors={[Theme.colors.primary[600], Theme.colors.primary[700]]}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={handleBackNavigation}
            activeOpacity={0.7}
          >
            <Ionicons name="arrow-back" size={24} color="white" />
          </TouchableOpacity>
          
          <View style={styles.headerTitleContainer}>
            <Text style={styles.headerTitle}>
              {sessionType === 'onboarding' 
                ? (i18n.language === 'hi' ? 'स्वास्थ्य मूल्यांकन' : 'Health Assessment')
                : (i18n.language === 'hi' ? 'प्राथमिकताएं अपडेट करें' : 'Update Preferences')
              }
            </Text>
            <Text style={styles.headerSubtitle}>
              {i18n.language === 'hi' 
                ? 'व्यक्तिगत सिफारिशों के लिए'
                : 'For personalized recommendations'
              }
            </Text>
          </View>
        </View>

        {session && (
          <ProgressIndicator
            currentStep={session.currentQuestionIndex + 1}
            totalSteps={session.totalQuestions}
            completionPercentage={session.completionPercentage}
            estimatedTimeRemaining={session.estimatedTimeRemaining}
            language={i18n.language}
          />
        )}
      </LinearGradient>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderQuestion()}
      </ScrollView>

      <View style={styles.footer}>
        {renderActionButtons()}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Theme.colors.surface.primary,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Theme.colors.surface.primary,
  },
  loadingContent: {
    alignItems: 'center',
    padding: 40,
  },
  loadingText: {
    fontSize: 16,
    color: Theme.colors.text.secondary,
    marginTop: 16,
    textAlign: 'center',
  },
  header: {
    paddingTop: 50,
    paddingBottom: 0,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  backButton: {
    padding: 8,
    marginRight: 12,
  },
  headerTitleContainer: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
  },
  headerSubtitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 4,
  },
  content: {
    flex: 1,
  },
  questionContainer: {
    padding: 20,
    minHeight: screenHeight * 0.5,
  },
  questionHeader: {
    marginBottom: 24,
  },
  questionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: Theme.colors.text.primary,
    lineHeight: 28,
    marginBottom: 8,
  },
  questionDescription: {
    fontSize: 16,
    color: Theme.colors.text.secondary,
    lineHeight: 24,
  },
  questionContent: {
    flex: 1,
  },
  infoDisplay: {
    alignItems: 'center',
    padding: 40,
  },
  infoText: {
    fontSize: 16,
    color: Theme.colors.text.secondary,
    textAlign: 'center',
    lineHeight: 24,
    marginTop: 16,
  },
  optionsContainer: {
    gap: 12,
  },
  optionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#E5E7EB',
    marginVertical: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  selectedOption: {
    backgroundColor: '#EEF2FF',
    borderColor: '#4F46E5',
    shadowColor: '#4F46E5',
    shadowOpacity: 0.2,
    elevation: 4,
  },
  optionIcon: {
    fontSize: 24,
    marginRight: 12,
  },
  optionTextContainer: {
    flex: 1,
  },
  optionText: {
    fontSize: 16,
    color: '#1F2937',
    fontWeight: '500',
    lineHeight: 22,
  },
  selectedOptionText: {
    color: '#4F46E5',
    fontWeight: '600',
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: '#D1D5DB',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
  },
  checkedBox: {
    backgroundColor: '#4F46E5',
    borderColor: '#4F46E5',
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 12,
    padding: 12,
    backgroundColor: Theme.colors.error[50],
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: Theme.colors.error[500],
  },
  errorText: {
    fontSize: 14,
    color: Theme.colors.error[600],
    marginLeft: 8,
    flex: 1,
  },
  footer: {
    padding: 20,
    backgroundColor: Theme.colors.surface.primary,
    borderTopWidth: 1,
    borderTopColor: Theme.colors.neutral[200],
  },
  actionButtonsContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  skipButton: {
    flex: 1,
    padding: 16,
    backgroundColor: Theme.colors.neutral[100],
    borderRadius: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Theme.colors.neutral[300],
  },
  skipButtonText: {
    fontSize: 16,
    color: Theme.colors.text.secondary,
    fontWeight: '500',
  },
  continueButton: {
    flex: 2,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    backgroundColor: Theme.colors.primary[500],
    borderRadius: 12,
    gap: 8,
  },
  disabledButton: {
    backgroundColor: Theme.colors.neutral[300],
  },
  continueButtonText: {
    fontSize: 16,
    color: 'white',
    fontWeight: '600',
  },
  disabledButtonText: {
    color: Theme.colors.neutral[500],
  },
});

export default EnhancedQuestionnaireScreen;
