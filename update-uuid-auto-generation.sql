-- Update database functions to use auto-generated UUIDs
-- Run this in your Supabase SQL Editor

-- Drop the old function first
DROP FUNCTION IF EXISTS create_questionnaire_session(uuid, text);
DROP FUNCTION IF EXISTS create_questionnaire_session(text);

-- Recreate the function with auto-generated UUIDs (no ID parameter needed)
CREATE OR REPLACE FUNCTION create_questionnaire_session(
    session_type text DEFAULT 'onboarding'
)
RETURNS uuid
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    current_user_id uuid;
    new_session_id uuid;
BEGIN
    -- Get current authenticated user
    current_user_id := auth.uid();
    
    -- Check if user is authenticated
    IF current_user_id IS NULL THEN
        RAISE EXCEPTION 'User not authenticated';
    END IF;
    
    -- Insert the session (let database auto-generate ID)
    INSERT INTO public.questionnaire_sessions (
        user_id,
        session_type,
        started_at,
        completion_percentage,
        created_at,
        updated_at
    ) VALUES (
        current_user_id,
        session_type,
        NOW(),
        0,
        NOW(),
        NOW()
    ) RETURNING id INTO new_session_id;
    
    RETURN new_session_id;
END;
$$;

-- <PERSON> execute permission on the function
GRANT EXECUTE ON FUNCTION create_questionnaire_session(text) TO authenticated;

-- Verify the tables have auto-generated UUIDs (should already be set)
-- These are just for verification - they should already exist

-- Verify questionnaire_sessions table
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'questionnaire_sessions' 
        AND column_name = 'id' 
        AND column_default LIKE '%gen_random_uuid%'
    ) THEN
        RAISE NOTICE 'questionnaire_sessions.id does not have auto-generated UUID default';
    ELSE
        RAISE NOTICE 'questionnaire_sessions.id has auto-generated UUID ✓';
    END IF;
END $$;

-- Verify questionnaire_responses table
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'questionnaire_responses' 
        AND column_name = 'id' 
        AND column_default LIKE '%gen_random_uuid%'
    ) THEN
        RAISE NOTICE 'questionnaire_responses.id does not have auto-generated UUID default';
    ELSE
        RAISE NOTICE 'questionnaire_responses.id has auto-generated UUID ✓';
    END IF;
END $$;

-- Test the function (optional - you can run this to verify it works)
-- SELECT create_questionnaire_session('onboarding');
