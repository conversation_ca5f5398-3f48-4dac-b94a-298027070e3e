<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="com.google.mlkit:object-detection-common:18.0.0" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.13\transforms\6ece73bb9fe8844fd8790e23f3b2cc28\transformed\object-detection-common-18.0.0\assets"><file name="mlkit_odt_localizer/localizer_with_validation.tflite" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\6ece73bb9fe8844fd8790e23f3b2cc28\transformed\object-detection-common-18.0.0\assets\mlkit_odt_localizer\localizer_with_validation.tflite"/><file name="mlkit_odt_localizer/mobile_object_localizer_3_1_anchors.pb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\6ece73bb9fe8844fd8790e23f3b2cc28\transformed\object-detection-common-18.0.0\assets\mlkit_odt_localizer\mobile_object_localizer_3_1_anchors.pb"/><file name="mlkit_odt_localizer/mobile_object_localizer_labelmap" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\6ece73bb9fe8844fd8790e23f3b2cc28\transformed\object-detection-common-18.0.0\assets\mlkit_odt_localizer\mobile_object_localizer_labelmap"/></source></dataSet><dataSet config="com.google.mlkit:text-recognition-korean:16.0.0" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.13\transforms\8fe3bffbf1b2e7c378176589ac1ff364\transformed\text-recognition-korean-16.0.0\assets"><file name="mlkit-google-ocr-models/aksara/aksara_page_layout_analysis_rpn_gcn.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\8fe3bffbf1b2e7c378176589ac1ff364\transformed\text-recognition-korean-16.0.0\assets\mlkit-google-ocr-models\aksara\aksara_page_layout_analysis_rpn_gcn.binarypb"/><file name="mlkit-google-ocr-models/aksara/aksara_page_layout_analysis_ti_rpn_gcn.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\8fe3bffbf1b2e7c378176589ac1ff364\transformed\text-recognition-korean-16.0.0\assets\mlkit-google-ocr-models\aksara\aksara_page_layout_analysis_ti_rpn_gcn.binarypb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Kore_ctc/optical/assets.extra/LabelMap.pb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\8fe3bffbf1b2e7c378176589ac1ff364\transformed\text-recognition-korean-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Kore_ctc\optical\assets.extra\LabelMap.pb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Kore_ctc/optical/conv_model.fb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\8fe3bffbf1b2e7c378176589ac1ff364\transformed\text-recognition-korean-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Kore_ctc\optical\conv_model.fb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Kore_ctc/optical/lstm_model.fb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\8fe3bffbf1b2e7c378176589ac1ff364\transformed\text-recognition-korean-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Kore_ctc\optical\lstm_model.fb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Kore_ctc_cpu.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\8fe3bffbf1b2e7c378176589ac1ff364\transformed\text-recognition-korean-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Kore_ctc_cpu.binarypb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Latn_ctc/optical/assets.extra/LabelMap.pb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\8fe3bffbf1b2e7c378176589ac1ff364\transformed\text-recognition-korean-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Latn_ctc\optical\assets.extra\LabelMap.pb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Latn_ctc/optical/conv_model.fb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\8fe3bffbf1b2e7c378176589ac1ff364\transformed\text-recognition-korean-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Latn_ctc\optical\conv_model.fb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Latn_ctc/optical/lstm_model.fb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\8fe3bffbf1b2e7c378176589ac1ff364\transformed\text-recognition-korean-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Latn_ctc\optical\lstm_model.fb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Latn_ctc_cpu.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\8fe3bffbf1b2e7c378176589ac1ff364\transformed\text-recognition-korean-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Latn_ctc_cpu.binarypb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/tflite_langid.tflite" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\8fe3bffbf1b2e7c378176589ac1ff364\transformed\text-recognition-korean-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\tflite_langid.tflite"/><file name="mlkit-google-ocr-models/gocr/layout/line_clustering_custom_ops/model.tflite" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\8fe3bffbf1b2e7c378176589ac1ff364\transformed\text-recognition-korean-16.0.0\assets\mlkit-google-ocr-models\gocr\layout\line_clustering_custom_ops\model.tflite"/><file name="mlkit-google-ocr-models/gocr/layout/line_splitting_custom_ops/model.tflite" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\8fe3bffbf1b2e7c378176589ac1ff364\transformed\text-recognition-korean-16.0.0\assets\mlkit-google-ocr-models\gocr\layout\line_splitting_custom_ops\model.tflite"/><file name="mlkit-google-ocr-models/taser/detector/region_proposal_text_detector_tflite_vertical_mbv2_v1.bincfg" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\8fe3bffbf1b2e7c378176589ac1ff364\transformed\text-recognition-korean-16.0.0\assets\mlkit-google-ocr-models\taser\detector\region_proposal_text_detector_tflite_vertical_mbv2_v1.bincfg"/><file name="mlkit-google-ocr-models/taser/detector/rpn_text_detector_mobile_space_to_depth_quantized_mbv2_v1.tflite" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\8fe3bffbf1b2e7c378176589ac1ff364\transformed\text-recognition-korean-16.0.0\assets\mlkit-google-ocr-models\taser\detector\rpn_text_detector_mobile_space_to_depth_quantized_mbv2_v1.tflite"/><file name="mlkit-google-ocr-models/taser/rpn_text_detection_tflite_mobile_mbv2.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\8fe3bffbf1b2e7c378176589ac1ff364\transformed\text-recognition-korean-16.0.0\assets\mlkit-google-ocr-models\taser\rpn_text_detection_tflite_mobile_mbv2.binarypb"/><file name="mlkit-google-ocr-models/taser/segmenter/tflite_script_detector_0.3.bincfg" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\8fe3bffbf1b2e7c378176589ac1ff364\transformed\text-recognition-korean-16.0.0\assets\mlkit-google-ocr-models\taser\segmenter\tflite_script_detector_0.3.bincfg"/><file name="mlkit-google-ocr-models/taser/segmenter/tflite_script_detector_0.3.conv_model" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\8fe3bffbf1b2e7c378176589ac1ff364\transformed\text-recognition-korean-16.0.0\assets\mlkit-google-ocr-models\taser\segmenter\tflite_script_detector_0.3.conv_model"/><file name="mlkit-google-ocr-models/taser/segmenter/tflite_script_detector_0.3.lstm_model" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\8fe3bffbf1b2e7c378176589ac1ff364\transformed\text-recognition-korean-16.0.0\assets\mlkit-google-ocr-models\taser\segmenter\tflite_script_detector_0.3.lstm_model"/><file name="mlkit-google-ocr-models/taser/taser_script_identification_tflite_mobile.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\8fe3bffbf1b2e7c378176589ac1ff364\transformed\text-recognition-korean-16.0.0\assets\mlkit-google-ocr-models\taser\taser_script_identification_tflite_mobile.binarypb"/><file name="mlkit-google-ocr-models/taser_tflite_gocrkorean_and_latin_mbv2_aksara_layout_gcn_mobile_engine.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\8fe3bffbf1b2e7c378176589ac1ff364\transformed\text-recognition-korean-16.0.0\assets\mlkit-google-ocr-models\taser_tflite_gocrkorean_and_latin_mbv2_aksara_layout_gcn_mobile_engine.binarypb"/><file name="mlkit-google-ocr-models/taser_tflite_gocrkorean_and_latin_mbv2_aksara_layout_gcn_mobile_engine_ti.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\8fe3bffbf1b2e7c378176589ac1ff364\transformed\text-recognition-korean-16.0.0\assets\mlkit-google-ocr-models\taser_tflite_gocrkorean_and_latin_mbv2_aksara_layout_gcn_mobile_engine_ti.binarypb"/><file name="mlkit-google-ocr-models/taser_tflite_gocrkorean_and_latin_mbv2_aksara_layout_gcn_mobile_recognizer.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\8fe3bffbf1b2e7c378176589ac1ff364\transformed\text-recognition-korean-16.0.0\assets\mlkit-google-ocr-models\taser_tflite_gocrkorean_and_latin_mbv2_aksara_layout_gcn_mobile_recognizer.binarypb"/><file name="mlkit-google-ocr-models/taser_tflite_gocrkorean_and_latin_mbv2_aksara_layout_gcn_mobile_runner.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\8fe3bffbf1b2e7c378176589ac1ff364\transformed\text-recognition-korean-16.0.0\assets\mlkit-google-ocr-models\taser_tflite_gocrkorean_and_latin_mbv2_aksara_layout_gcn_mobile_runner.binarypb"/><file name="mlkit-google-ocr-models/taser_tflite_gocrkorean_and_latin_mbv2_aksara_layout_gcn_mobile_runner_ti.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\8fe3bffbf1b2e7c378176589ac1ff364\transformed\text-recognition-korean-16.0.0\assets\mlkit-google-ocr-models\taser_tflite_gocrkorean_and_latin_mbv2_aksara_layout_gcn_mobile_runner_ti.binarypb"/></source></dataSet><dataSet config="com.google.mlkit:text-recognition-japanese:16.0.0" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.13\transforms\557853535f9f860c34bac7705135fe77\transformed\text-recognition-japanese-16.0.0\assets"><file name="mlkit-google-ocr-models/aksara/aksara_page_layout_analysis_rpn_gcn.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\557853535f9f860c34bac7705135fe77\transformed\text-recognition-japanese-16.0.0\assets\mlkit-google-ocr-models\aksara\aksara_page_layout_analysis_rpn_gcn.binarypb"/><file name="mlkit-google-ocr-models/aksara/aksara_page_layout_analysis_ti_rpn_gcn.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\557853535f9f860c34bac7705135fe77\transformed\text-recognition-japanese-16.0.0\assets\mlkit-google-ocr-models\aksara\aksara_page_layout_analysis_ti_rpn_gcn.binarypb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Hani_ctc/optical/assets.extra/LabelMap.pb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\557853535f9f860c34bac7705135fe77\transformed\text-recognition-japanese-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Hani_ctc\optical\assets.extra\LabelMap.pb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Hani_ctc/optical/conv_model.fb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\557853535f9f860c34bac7705135fe77\transformed\text-recognition-japanese-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Hani_ctc\optical\conv_model.fb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Hani_ctc/optical/lstm_model.fb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\557853535f9f860c34bac7705135fe77\transformed\text-recognition-japanese-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Hani_ctc\optical\lstm_model.fb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Hani_ctc_cpu.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\557853535f9f860c34bac7705135fe77\transformed\text-recognition-japanese-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Hani_ctc_cpu.binarypb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Jpan_ctc/optical/assets.extra/LabelMap.pb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\557853535f9f860c34bac7705135fe77\transformed\text-recognition-japanese-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Jpan_ctc\optical\assets.extra\LabelMap.pb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Jpan_ctc/optical/conv_model.fb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\557853535f9f860c34bac7705135fe77\transformed\text-recognition-japanese-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Jpan_ctc\optical\conv_model.fb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Jpan_ctc/optical/lstm_model.fb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\557853535f9f860c34bac7705135fe77\transformed\text-recognition-japanese-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Jpan_ctc\optical\lstm_model.fb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Jpan_ctc_cpu.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\557853535f9f860c34bac7705135fe77\transformed\text-recognition-japanese-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Jpan_ctc_cpu.binarypb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Latn_ctc/optical/assets.extra/LabelMap.pb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\557853535f9f860c34bac7705135fe77\transformed\text-recognition-japanese-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Latn_ctc\optical\assets.extra\LabelMap.pb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Latn_ctc/optical/conv_model.fb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\557853535f9f860c34bac7705135fe77\transformed\text-recognition-japanese-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Latn_ctc\optical\conv_model.fb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Latn_ctc/optical/lstm_model.fb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\557853535f9f860c34bac7705135fe77\transformed\text-recognition-japanese-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Latn_ctc\optical\lstm_model.fb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Latn_ctc_cpu.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\557853535f9f860c34bac7705135fe77\transformed\text-recognition-japanese-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Latn_ctc_cpu.binarypb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/tflite_langid.tflite" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\557853535f9f860c34bac7705135fe77\transformed\text-recognition-japanese-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\tflite_langid.tflite"/><file name="mlkit-google-ocr-models/gocr/layout/line_clustering_custom_ops/model.tflite" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\557853535f9f860c34bac7705135fe77\transformed\text-recognition-japanese-16.0.0\assets\mlkit-google-ocr-models\gocr\layout\line_clustering_custom_ops\model.tflite"/><file name="mlkit-google-ocr-models/gocr/layout/line_splitting_custom_ops/model.tflite" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\557853535f9f860c34bac7705135fe77\transformed\text-recognition-japanese-16.0.0\assets\mlkit-google-ocr-models\gocr\layout\line_splitting_custom_ops\model.tflite"/><file name="mlkit-google-ocr-models/taser/detector/region_proposal_text_detector_tflite_vertical_mbv2_v1.bincfg" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\557853535f9f860c34bac7705135fe77\transformed\text-recognition-japanese-16.0.0\assets\mlkit-google-ocr-models\taser\detector\region_proposal_text_detector_tflite_vertical_mbv2_v1.bincfg"/><file name="mlkit-google-ocr-models/taser/detector/rpn_text_detector_mobile_space_to_depth_quantized_mbv2_v1.tflite" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\557853535f9f860c34bac7705135fe77\transformed\text-recognition-japanese-16.0.0\assets\mlkit-google-ocr-models\taser\detector\rpn_text_detector_mobile_space_to_depth_quantized_mbv2_v1.tflite"/><file name="mlkit-google-ocr-models/taser/rpn_text_detection_tflite_mobile_mbv2.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\557853535f9f860c34bac7705135fe77\transformed\text-recognition-japanese-16.0.0\assets\mlkit-google-ocr-models\taser\rpn_text_detection_tflite_mobile_mbv2.binarypb"/><file name="mlkit-google-ocr-models/taser/segmenter/tflite_script_detector_0.3.bincfg" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\557853535f9f860c34bac7705135fe77\transformed\text-recognition-japanese-16.0.0\assets\mlkit-google-ocr-models\taser\segmenter\tflite_script_detector_0.3.bincfg"/><file name="mlkit-google-ocr-models/taser/segmenter/tflite_script_detector_0.3.conv_model" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\557853535f9f860c34bac7705135fe77\transformed\text-recognition-japanese-16.0.0\assets\mlkit-google-ocr-models\taser\segmenter\tflite_script_detector_0.3.conv_model"/><file name="mlkit-google-ocr-models/taser/segmenter/tflite_script_detector_0.3.lstm_model" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\557853535f9f860c34bac7705135fe77\transformed\text-recognition-japanese-16.0.0\assets\mlkit-google-ocr-models\taser\segmenter\tflite_script_detector_0.3.lstm_model"/><file name="mlkit-google-ocr-models/taser/taser_script_identification_tflite_mobile.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\557853535f9f860c34bac7705135fe77\transformed\text-recognition-japanese-16.0.0\assets\mlkit-google-ocr-models\taser\taser_script_identification_tflite_mobile.binarypb"/><file name="mlkit-google-ocr-models/taser_tflite_gocrjapanese_and_latin_mbv2_aksara_layout_gcn_mobile_engine.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\557853535f9f860c34bac7705135fe77\transformed\text-recognition-japanese-16.0.0\assets\mlkit-google-ocr-models\taser_tflite_gocrjapanese_and_latin_mbv2_aksara_layout_gcn_mobile_engine.binarypb"/><file name="mlkit-google-ocr-models/taser_tflite_gocrjapanese_and_latin_mbv2_aksara_layout_gcn_mobile_engine_ti.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\557853535f9f860c34bac7705135fe77\transformed\text-recognition-japanese-16.0.0\assets\mlkit-google-ocr-models\taser_tflite_gocrjapanese_and_latin_mbv2_aksara_layout_gcn_mobile_engine_ti.binarypb"/><file name="mlkit-google-ocr-models/taser_tflite_gocrjapanese_and_latin_mbv2_aksara_layout_gcn_mobile_recognizer.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\557853535f9f860c34bac7705135fe77\transformed\text-recognition-japanese-16.0.0\assets\mlkit-google-ocr-models\taser_tflite_gocrjapanese_and_latin_mbv2_aksara_layout_gcn_mobile_recognizer.binarypb"/><file name="mlkit-google-ocr-models/taser_tflite_gocrjapanese_and_latin_mbv2_aksara_layout_gcn_mobile_runner.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\557853535f9f860c34bac7705135fe77\transformed\text-recognition-japanese-16.0.0\assets\mlkit-google-ocr-models\taser_tflite_gocrjapanese_and_latin_mbv2_aksara_layout_gcn_mobile_runner.binarypb"/><file name="mlkit-google-ocr-models/taser_tflite_gocrjapanese_and_latin_mbv2_aksara_layout_gcn_mobile_runner_ti.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\557853535f9f860c34bac7705135fe77\transformed\text-recognition-japanese-16.0.0\assets\mlkit-google-ocr-models\taser_tflite_gocrjapanese_and_latin_mbv2_aksara_layout_gcn_mobile_runner_ti.binarypb"/></source></dataSet><dataSet config="com.google.mlkit:text-recognition-devanagari:16.0.0" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.13\transforms\860e4262622aba7728c92912ca69ecf3\transformed\text-recognition-devanagari-16.0.0\assets"><file name="mlkit-google-ocr-models/aksara/aksara_page_layout_analysis_rpn_gcn.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\860e4262622aba7728c92912ca69ecf3\transformed\text-recognition-devanagari-16.0.0\assets\mlkit-google-ocr-models\aksara\aksara_page_layout_analysis_rpn_gcn.binarypb"/><file name="mlkit-google-ocr-models/aksara/aksara_page_layout_analysis_ti_rpn_gcn.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\860e4262622aba7728c92912ca69ecf3\transformed\text-recognition-devanagari-16.0.0\assets\mlkit-google-ocr-models\aksara\aksara_page_layout_analysis_ti_rpn_gcn.binarypb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Beng_ctc/optical/assets.extra/LabelMap.pb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\860e4262622aba7728c92912ca69ecf3\transformed\text-recognition-devanagari-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Beng_ctc\optical\assets.extra\LabelMap.pb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Beng_ctc/optical/conv_model.fb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\860e4262622aba7728c92912ca69ecf3\transformed\text-recognition-devanagari-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Beng_ctc\optical\conv_model.fb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Beng_ctc/optical/lstm_model.fb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\860e4262622aba7728c92912ca69ecf3\transformed\text-recognition-devanagari-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Beng_ctc\optical\lstm_model.fb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Beng_ctc_cpu.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\860e4262622aba7728c92912ca69ecf3\transformed\text-recognition-devanagari-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Beng_ctc_cpu.binarypb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Deva_ctc/optical/assets.extra/LabelMap.pb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\860e4262622aba7728c92912ca69ecf3\transformed\text-recognition-devanagari-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Deva_ctc\optical\assets.extra\LabelMap.pb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Deva_ctc/optical/conv_model.fb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\860e4262622aba7728c92912ca69ecf3\transformed\text-recognition-devanagari-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Deva_ctc\optical\conv_model.fb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Deva_ctc/optical/lstm_model.fb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\860e4262622aba7728c92912ca69ecf3\transformed\text-recognition-devanagari-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Deva_ctc\optical\lstm_model.fb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Deva_ctc_cpu.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\860e4262622aba7728c92912ca69ecf3\transformed\text-recognition-devanagari-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Deva_ctc_cpu.binarypb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Latn_ctc/optical/assets.extra/LabelMap.pb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\860e4262622aba7728c92912ca69ecf3\transformed\text-recognition-devanagari-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Latn_ctc\optical\assets.extra\LabelMap.pb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Latn_ctc/optical/conv_model.fb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\860e4262622aba7728c92912ca69ecf3\transformed\text-recognition-devanagari-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Latn_ctc\optical\conv_model.fb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Latn_ctc/optical/lstm_model.fb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\860e4262622aba7728c92912ca69ecf3\transformed\text-recognition-devanagari-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Latn_ctc\optical\lstm_model.fb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Latn_ctc_cpu.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\860e4262622aba7728c92912ca69ecf3\transformed\text-recognition-devanagari-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Latn_ctc_cpu.binarypb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/tflite_langid.tflite" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\860e4262622aba7728c92912ca69ecf3\transformed\text-recognition-devanagari-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\tflite_langid.tflite"/><file name="mlkit-google-ocr-models/gocr/layout/line_clustering_custom_ops/model.tflite" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\860e4262622aba7728c92912ca69ecf3\transformed\text-recognition-devanagari-16.0.0\assets\mlkit-google-ocr-models\gocr\layout\line_clustering_custom_ops\model.tflite"/><file name="mlkit-google-ocr-models/gocr/layout/line_splitting_custom_ops/model.tflite" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\860e4262622aba7728c92912ca69ecf3\transformed\text-recognition-devanagari-16.0.0\assets\mlkit-google-ocr-models\gocr\layout\line_splitting_custom_ops\model.tflite"/><file name="mlkit-google-ocr-models/taser/detector/region_proposal_text_detector_tflite_vertical_mbv2_v1.bincfg" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\860e4262622aba7728c92912ca69ecf3\transformed\text-recognition-devanagari-16.0.0\assets\mlkit-google-ocr-models\taser\detector\region_proposal_text_detector_tflite_vertical_mbv2_v1.bincfg"/><file name="mlkit-google-ocr-models/taser/detector/rpn_text_detector_mobile_space_to_depth_quantized_mbv2_v1.tflite" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\860e4262622aba7728c92912ca69ecf3\transformed\text-recognition-devanagari-16.0.0\assets\mlkit-google-ocr-models\taser\detector\rpn_text_detector_mobile_space_to_depth_quantized_mbv2_v1.tflite"/><file name="mlkit-google-ocr-models/taser/rpn_text_detection_tflite_mobile_mbv2.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\860e4262622aba7728c92912ca69ecf3\transformed\text-recognition-devanagari-16.0.0\assets\mlkit-google-ocr-models\taser\rpn_text_detection_tflite_mobile_mbv2.binarypb"/><file name="mlkit-google-ocr-models/taser/segmenter/tflite_script_detector_0.3.bincfg" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\860e4262622aba7728c92912ca69ecf3\transformed\text-recognition-devanagari-16.0.0\assets\mlkit-google-ocr-models\taser\segmenter\tflite_script_detector_0.3.bincfg"/><file name="mlkit-google-ocr-models/taser/segmenter/tflite_script_detector_0.3.conv_model" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\860e4262622aba7728c92912ca69ecf3\transformed\text-recognition-devanagari-16.0.0\assets\mlkit-google-ocr-models\taser\segmenter\tflite_script_detector_0.3.conv_model"/><file name="mlkit-google-ocr-models/taser/segmenter/tflite_script_detector_0.3.lstm_model" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\860e4262622aba7728c92912ca69ecf3\transformed\text-recognition-devanagari-16.0.0\assets\mlkit-google-ocr-models\taser\segmenter\tflite_script_detector_0.3.lstm_model"/><file name="mlkit-google-ocr-models/taser/taser_script_identification_tflite_mobile.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\860e4262622aba7728c92912ca69ecf3\transformed\text-recognition-devanagari-16.0.0\assets\mlkit-google-ocr-models\taser\taser_script_identification_tflite_mobile.binarypb"/><file name="mlkit-google-ocr-models/taser_tflite_gocrdevanagari_and_latin_mbv2_aksara_layout_gcn_mobile_engine.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\860e4262622aba7728c92912ca69ecf3\transformed\text-recognition-devanagari-16.0.0\assets\mlkit-google-ocr-models\taser_tflite_gocrdevanagari_and_latin_mbv2_aksara_layout_gcn_mobile_engine.binarypb"/><file name="mlkit-google-ocr-models/taser_tflite_gocrdevanagari_and_latin_mbv2_aksara_layout_gcn_mobile_engine_ti.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\860e4262622aba7728c92912ca69ecf3\transformed\text-recognition-devanagari-16.0.0\assets\mlkit-google-ocr-models\taser_tflite_gocrdevanagari_and_latin_mbv2_aksara_layout_gcn_mobile_engine_ti.binarypb"/><file name="mlkit-google-ocr-models/taser_tflite_gocrdevanagari_and_latin_mbv2_aksara_layout_gcn_mobile_recognizer.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\860e4262622aba7728c92912ca69ecf3\transformed\text-recognition-devanagari-16.0.0\assets\mlkit-google-ocr-models\taser_tflite_gocrdevanagari_and_latin_mbv2_aksara_layout_gcn_mobile_recognizer.binarypb"/><file name="mlkit-google-ocr-models/taser_tflite_gocrdevanagari_and_latin_mbv2_aksara_layout_gcn_mobile_runner.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\860e4262622aba7728c92912ca69ecf3\transformed\text-recognition-devanagari-16.0.0\assets\mlkit-google-ocr-models\taser_tflite_gocrdevanagari_and_latin_mbv2_aksara_layout_gcn_mobile_runner.binarypb"/><file name="mlkit-google-ocr-models/taser_tflite_gocrdevanagari_and_latin_mbv2_aksara_layout_gcn_mobile_runner_ti.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\860e4262622aba7728c92912ca69ecf3\transformed\text-recognition-devanagari-16.0.0\assets\mlkit-google-ocr-models\taser_tflite_gocrdevanagari_and_latin_mbv2_aksara_layout_gcn_mobile_runner_ti.binarypb"/></source></dataSet><dataSet config="com.google.mlkit:text-recognition-chinese:16.0.0" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.13\transforms\75a59c21320c47a756fdba669af99ccf\transformed\text-recognition-chinese-16.0.0\assets"><file name="mlkit-google-ocr-models/aksara/aksara_page_layout_analysis_rpn_gcn.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\75a59c21320c47a756fdba669af99ccf\transformed\text-recognition-chinese-16.0.0\assets\mlkit-google-ocr-models\aksara\aksara_page_layout_analysis_rpn_gcn.binarypb"/><file name="mlkit-google-ocr-models/aksara/aksara_page_layout_analysis_ti_rpn_gcn.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\75a59c21320c47a756fdba669af99ccf\transformed\text-recognition-chinese-16.0.0\assets\mlkit-google-ocr-models\aksara\aksara_page_layout_analysis_ti_rpn_gcn.binarypb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Hani_ctc/optical/assets.extra/LabelMap.pb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\75a59c21320c47a756fdba669af99ccf\transformed\text-recognition-chinese-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Hani_ctc\optical\assets.extra\LabelMap.pb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Hani_ctc/optical/conv_model.fb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\75a59c21320c47a756fdba669af99ccf\transformed\text-recognition-chinese-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Hani_ctc\optical\conv_model.fb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Hani_ctc/optical/lstm_model.fb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\75a59c21320c47a756fdba669af99ccf\transformed\text-recognition-chinese-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Hani_ctc\optical\lstm_model.fb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Hani_ctc_cpu.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\75a59c21320c47a756fdba669af99ccf\transformed\text-recognition-chinese-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Hani_ctc_cpu.binarypb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Latn_ctc/optical/assets.extra/LabelMap.pb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\75a59c21320c47a756fdba669af99ccf\transformed\text-recognition-chinese-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Latn_ctc\optical\assets.extra\LabelMap.pb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Latn_ctc/optical/conv_model.fb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\75a59c21320c47a756fdba669af99ccf\transformed\text-recognition-chinese-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Latn_ctc\optical\conv_model.fb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Latn_ctc/optical/lstm_model.fb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\75a59c21320c47a756fdba669af99ccf\transformed\text-recognition-chinese-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Latn_ctc\optical\lstm_model.fb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Latn_ctc_cpu.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\75a59c21320c47a756fdba669af99ccf\transformed\text-recognition-chinese-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Latn_ctc_cpu.binarypb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/tflite_langid.tflite" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\75a59c21320c47a756fdba669af99ccf\transformed\text-recognition-chinese-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\tflite_langid.tflite"/><file name="mlkit-google-ocr-models/gocr/layout/line_clustering_custom_ops/model.tflite" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\75a59c21320c47a756fdba669af99ccf\transformed\text-recognition-chinese-16.0.0\assets\mlkit-google-ocr-models\gocr\layout\line_clustering_custom_ops\model.tflite"/><file name="mlkit-google-ocr-models/gocr/layout/line_splitting_custom_ops/model.tflite" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\75a59c21320c47a756fdba669af99ccf\transformed\text-recognition-chinese-16.0.0\assets\mlkit-google-ocr-models\gocr\layout\line_splitting_custom_ops\model.tflite"/><file name="mlkit-google-ocr-models/taser/detector/region_proposal_text_detector_tflite_vertical_mbv2_v1.bincfg" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\75a59c21320c47a756fdba669af99ccf\transformed\text-recognition-chinese-16.0.0\assets\mlkit-google-ocr-models\taser\detector\region_proposal_text_detector_tflite_vertical_mbv2_v1.bincfg"/><file name="mlkit-google-ocr-models/taser/detector/rpn_text_detector_mobile_space_to_depth_quantized_mbv2_v1.tflite" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\75a59c21320c47a756fdba669af99ccf\transformed\text-recognition-chinese-16.0.0\assets\mlkit-google-ocr-models\taser\detector\rpn_text_detector_mobile_space_to_depth_quantized_mbv2_v1.tflite"/><file name="mlkit-google-ocr-models/taser/rpn_text_detection_tflite_mobile_mbv2.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\75a59c21320c47a756fdba669af99ccf\transformed\text-recognition-chinese-16.0.0\assets\mlkit-google-ocr-models\taser\rpn_text_detection_tflite_mobile_mbv2.binarypb"/><file name="mlkit-google-ocr-models/taser/segmenter/tflite_script_detector_0.3.bincfg" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\75a59c21320c47a756fdba669af99ccf\transformed\text-recognition-chinese-16.0.0\assets\mlkit-google-ocr-models\taser\segmenter\tflite_script_detector_0.3.bincfg"/><file name="mlkit-google-ocr-models/taser/segmenter/tflite_script_detector_0.3.conv_model" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\75a59c21320c47a756fdba669af99ccf\transformed\text-recognition-chinese-16.0.0\assets\mlkit-google-ocr-models\taser\segmenter\tflite_script_detector_0.3.conv_model"/><file name="mlkit-google-ocr-models/taser/segmenter/tflite_script_detector_0.3.lstm_model" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\75a59c21320c47a756fdba669af99ccf\transformed\text-recognition-chinese-16.0.0\assets\mlkit-google-ocr-models\taser\segmenter\tflite_script_detector_0.3.lstm_model"/><file name="mlkit-google-ocr-models/taser/taser_script_identification_tflite_mobile.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\75a59c21320c47a756fdba669af99ccf\transformed\text-recognition-chinese-16.0.0\assets\mlkit-google-ocr-models\taser\taser_script_identification_tflite_mobile.binarypb"/><file name="mlkit-google-ocr-models/taser_tflite_gocrchinese_and_latin_mbv2_aksara_layout_gcn_mobile_engine.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\75a59c21320c47a756fdba669af99ccf\transformed\text-recognition-chinese-16.0.0\assets\mlkit-google-ocr-models\taser_tflite_gocrchinese_and_latin_mbv2_aksara_layout_gcn_mobile_engine.binarypb"/><file name="mlkit-google-ocr-models/taser_tflite_gocrchinese_and_latin_mbv2_aksara_layout_gcn_mobile_engine_ti.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\75a59c21320c47a756fdba669af99ccf\transformed\text-recognition-chinese-16.0.0\assets\mlkit-google-ocr-models\taser_tflite_gocrchinese_and_latin_mbv2_aksara_layout_gcn_mobile_engine_ti.binarypb"/><file name="mlkit-google-ocr-models/taser_tflite_gocrchinese_and_latin_mbv2_aksara_layout_gcn_mobile_recognizer.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\75a59c21320c47a756fdba669af99ccf\transformed\text-recognition-chinese-16.0.0\assets\mlkit-google-ocr-models\taser_tflite_gocrchinese_and_latin_mbv2_aksara_layout_gcn_mobile_recognizer.binarypb"/><file name="mlkit-google-ocr-models/taser_tflite_gocrchinese_and_latin_mbv2_aksara_layout_gcn_mobile_runner.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\75a59c21320c47a756fdba669af99ccf\transformed\text-recognition-chinese-16.0.0\assets\mlkit-google-ocr-models\taser_tflite_gocrchinese_and_latin_mbv2_aksara_layout_gcn_mobile_runner.binarypb"/><file name="mlkit-google-ocr-models/taser_tflite_gocrchinese_and_latin_mbv2_aksara_layout_gcn_mobile_runner_ti.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\75a59c21320c47a756fdba669af99ccf\transformed\text-recognition-chinese-16.0.0\assets\mlkit-google-ocr-models\taser_tflite_gocrchinese_and_latin_mbv2_aksara_layout_gcn_mobile_runner_ti.binarypb"/></source></dataSet><dataSet config="com.google.mlkit:text-recognition:16.0.0" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.13\transforms\e649289029d07290cdbf1c54774eb4ef\transformed\text-recognition-16.0.0\assets"><file name="mlkit-google-ocr-models/aksara/aksara_page_layout_analysis_rpn_gcn.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\e649289029d07290cdbf1c54774eb4ef\transformed\text-recognition-16.0.0\assets\mlkit-google-ocr-models\aksara\aksara_page_layout_analysis_rpn_gcn.binarypb"/><file name="mlkit-google-ocr-models/aksara/aksara_page_layout_analysis_ti_rpn_gcn.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\e649289029d07290cdbf1c54774eb4ef\transformed\text-recognition-16.0.0\assets\mlkit-google-ocr-models\aksara\aksara_page_layout_analysis_ti_rpn_gcn.binarypb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Latn_ctc/optical/assets.extra/LabelMap.pb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\e649289029d07290cdbf1c54774eb4ef\transformed\text-recognition-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Latn_ctc\optical\assets.extra\LabelMap.pb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Latn_ctc/optical/conv_model.fb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\e649289029d07290cdbf1c54774eb4ef\transformed\text-recognition-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Latn_ctc\optical\conv_model.fb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Latn_ctc/optical/lstm_model.fb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\e649289029d07290cdbf1c54774eb4ef\transformed\text-recognition-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Latn_ctc\optical\lstm_model.fb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Latn_ctc_cpu.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\e649289029d07290cdbf1c54774eb4ef\transformed\text-recognition-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Latn_ctc_cpu.binarypb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/tflite_langid.tflite" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\e649289029d07290cdbf1c54774eb4ef\transformed\text-recognition-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\tflite_langid.tflite"/><file name="mlkit-google-ocr-models/gocr/layout/line_clustering_custom_ops/model.tflite" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\e649289029d07290cdbf1c54774eb4ef\transformed\text-recognition-16.0.0\assets\mlkit-google-ocr-models\gocr\layout\line_clustering_custom_ops\model.tflite"/><file name="mlkit-google-ocr-models/gocr/layout/line_splitting_custom_ops/model.tflite" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\e649289029d07290cdbf1c54774eb4ef\transformed\text-recognition-16.0.0\assets\mlkit-google-ocr-models\gocr\layout\line_splitting_custom_ops\model.tflite"/><file name="mlkit-google-ocr-models/taser/detector/region_proposal_text_detector_tflite_vertical_mbv2_v1.bincfg" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\e649289029d07290cdbf1c54774eb4ef\transformed\text-recognition-16.0.0\assets\mlkit-google-ocr-models\taser\detector\region_proposal_text_detector_tflite_vertical_mbv2_v1.bincfg"/><file name="mlkit-google-ocr-models/taser/detector/rpn_text_detector_mobile_space_to_depth_quantized_mbv2_v1.tflite" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\e649289029d07290cdbf1c54774eb4ef\transformed\text-recognition-16.0.0\assets\mlkit-google-ocr-models\taser\detector\rpn_text_detector_mobile_space_to_depth_quantized_mbv2_v1.tflite"/><file name="mlkit-google-ocr-models/taser/rpn_text_detection_tflite_mobile_mbv2.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\e649289029d07290cdbf1c54774eb4ef\transformed\text-recognition-16.0.0\assets\mlkit-google-ocr-models\taser\rpn_text_detection_tflite_mobile_mbv2.binarypb"/><file name="mlkit-google-ocr-models/taser/segmenter/tflite_script_detector_0.3.bincfg" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\e649289029d07290cdbf1c54774eb4ef\transformed\text-recognition-16.0.0\assets\mlkit-google-ocr-models\taser\segmenter\tflite_script_detector_0.3.bincfg"/><file name="mlkit-google-ocr-models/taser/segmenter/tflite_script_detector_0.3.conv_model" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\e649289029d07290cdbf1c54774eb4ef\transformed\text-recognition-16.0.0\assets\mlkit-google-ocr-models\taser\segmenter\tflite_script_detector_0.3.conv_model"/><file name="mlkit-google-ocr-models/taser/segmenter/tflite_script_detector_0.3.lstm_model" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\e649289029d07290cdbf1c54774eb4ef\transformed\text-recognition-16.0.0\assets\mlkit-google-ocr-models\taser\segmenter\tflite_script_detector_0.3.lstm_model"/><file name="mlkit-google-ocr-models/taser/taser_script_identification_tflite_mobile.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\e649289029d07290cdbf1c54774eb4ef\transformed\text-recognition-16.0.0\assets\mlkit-google-ocr-models\taser\taser_script_identification_tflite_mobile.binarypb"/><file name="mlkit-google-ocr-models/taser_tflite_gocrlatin_mbv2_scriptid_aksara_layout_gcn_mobile_engine.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\e649289029d07290cdbf1c54774eb4ef\transformed\text-recognition-16.0.0\assets\mlkit-google-ocr-models\taser_tflite_gocrlatin_mbv2_scriptid_aksara_layout_gcn_mobile_engine.binarypb"/><file name="mlkit-google-ocr-models/taser_tflite_gocrlatin_mbv2_scriptid_aksara_layout_gcn_mobile_engine_ti.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\e649289029d07290cdbf1c54774eb4ef\transformed\text-recognition-16.0.0\assets\mlkit-google-ocr-models\taser_tflite_gocrlatin_mbv2_scriptid_aksara_layout_gcn_mobile_engine_ti.binarypb"/><file name="mlkit-google-ocr-models/taser_tflite_gocrlatin_mbv2_scriptid_aksara_layout_gcn_mobile_recognizer.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\e649289029d07290cdbf1c54774eb4ef\transformed\text-recognition-16.0.0\assets\mlkit-google-ocr-models\taser_tflite_gocrlatin_mbv2_scriptid_aksara_layout_gcn_mobile_recognizer.binarypb"/><file name="mlkit-google-ocr-models/taser_tflite_gocrlatin_mbv2_scriptid_aksara_layout_gcn_mobile_runner.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\e649289029d07290cdbf1c54774eb4ef\transformed\text-recognition-16.0.0\assets\mlkit-google-ocr-models\taser_tflite_gocrlatin_mbv2_scriptid_aksara_layout_gcn_mobile_runner.binarypb"/><file name="mlkit-google-ocr-models/taser_tflite_gocrlatin_mbv2_scriptid_aksara_layout_gcn_mobile_runner_ti.binarypb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\e649289029d07290cdbf1c54774eb4ef\transformed\text-recognition-16.0.0\assets\mlkit-google-ocr-models\taser_tflite_gocrlatin_mbv2_scriptid_aksara_layout_gcn_mobile_runner_ti.binarypb"/></source></dataSet><dataSet config="com.google.mlkit:image-labeling:17.0.8" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.13\transforms\6a7ff03472faa32cabd273cf0a7f1e6d\transformed\image-labeling-17.0.8\assets"><file name="mlkit_label_default_model/mobile_ica_8bit_with_metadata_tflite" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\6a7ff03472faa32cabd273cf0a7f1e6d\transformed\image-labeling-17.0.8\assets\mlkit_label_default_model\mobile_ica_8bit_with_metadata_tflite"/></source></dataSet><dataSet config="com.google.mlkit:object-detection:17.0.1" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.13\transforms\aec36f618bce635507132453e2ad4d1e\transformed\object-detection-17.0.1\assets"><file name="mlkit_odt_default_classifier/labeler_with_validation.tflite" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\aec36f618bce635507132453e2ad4d1e\transformed\object-detection-17.0.1\assets\mlkit_odt_default_classifier\labeler_with_validation.tflite"/></source></dataSet><dataSet config="com.google.mlkit:barcode-scanning:17.3.0" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.13\transforms\e2bb4dec275a16b4bfc458248856b0b8\transformed\barcode-scanning-17.3.0\assets"><file name="mlkit_barcode_models/barcode_ssd_mobilenet_v1_dmp25_quant.tflite" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\e2bb4dec275a16b4bfc458248856b0b8\transformed\barcode-scanning-17.3.0\assets\mlkit_barcode_models\barcode_ssd_mobilenet_v1_dmp25_quant.tflite"/><file name="mlkit_barcode_models/oned_auto_regressor_mobile.tflite" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\e2bb4dec275a16b4bfc458248856b0b8\transformed\barcode-scanning-17.3.0\assets\mlkit_barcode_models\oned_auto_regressor_mobile.tflite"/><file name="mlkit_barcode_models/oned_feature_extractor_mobile.tflite" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\e2bb4dec275a16b4bfc458248856b0b8\transformed\barcode-scanning-17.3.0\assets\mlkit_barcode_models\oned_feature_extractor_mobile.tflite"/></source></dataSet><dataSet config="com.google.mlkit:face-detection:16.1.5" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.13\transforms\ed1d4cbf20f73f15f47c294c7a90b540\transformed\face-detection-16.1.5\assets"><file name="models_bundled/BCLjoy_200.emd" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\ed1d4cbf20f73f15f47c294c7a90b540\transformed\face-detection-16.1.5\assets\models_bundled\BCLjoy_200.emd"/><file name="models_bundled/BCLlefteyeclosed_200.emd" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\ed1d4cbf20f73f15f47c294c7a90b540\transformed\face-detection-16.1.5\assets\models_bundled\BCLlefteyeclosed_200.emd"/><file name="models_bundled/BCLrighteyeclosed_200.emd" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\ed1d4cbf20f73f15f47c294c7a90b540\transformed\face-detection-16.1.5\assets\models_bundled\BCLrighteyeclosed_200.emd"/><file name="models_bundled/blazeface.tfl" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\ed1d4cbf20f73f15f47c294c7a90b540\transformed\face-detection-16.1.5\assets\models_bundled\blazeface.tfl"/><file name="models_bundled/contours.tfl" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\ed1d4cbf20f73f15f47c294c7a90b540\transformed\face-detection-16.1.5\assets\models_bundled\contours.tfl"/><file name="models_bundled/fssd_25_8bit_gray_v2.tflite" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\ed1d4cbf20f73f15f47c294c7a90b540\transformed\face-detection-16.1.5\assets\models_bundled\fssd_25_8bit_gray_v2.tflite"/><file name="models_bundled/fssd_25_8bit_v2.tflite" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\ed1d4cbf20f73f15f47c294c7a90b540\transformed\face-detection-16.1.5\assets\models_bundled\fssd_25_8bit_v2.tflite"/><file name="models_bundled/fssd_anchors_v2.pb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\ed1d4cbf20f73f15f47c294c7a90b540\transformed\face-detection-16.1.5\assets\models_bundled\fssd_anchors_v2.pb"/><file name="models_bundled/fssd_anchors_v5.pb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\ed1d4cbf20f73f15f47c294c7a90b540\transformed\face-detection-16.1.5\assets\models_bundled\fssd_anchors_v5.pb"/><file name="models_bundled/fssd_medium_8bit_gray_v5.tflite" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\ed1d4cbf20f73f15f47c294c7a90b540\transformed\face-detection-16.1.5\assets\models_bundled\fssd_medium_8bit_gray_v5.tflite"/><file name="models_bundled/fssd_medium_8bit_v5.tflite" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\ed1d4cbf20f73f15f47c294c7a90b540\transformed\face-detection-16.1.5\assets\models_bundled\fssd_medium_8bit_v5.tflite"/><file name="models_bundled/LMprec_600.emd" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\ed1d4cbf20f73f15f47c294c7a90b540\transformed\face-detection-16.1.5\assets\models_bundled\LMprec_600.emd"/><file name="models_bundled/MFT_fssd_accgray.pb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\ed1d4cbf20f73f15f47c294c7a90b540\transformed\face-detection-16.1.5\assets\models_bundled\MFT_fssd_accgray.pb"/><file name="models_bundled/MFT_fssd_fastgray.pb" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\ed1d4cbf20f73f15f47c294c7a90b540\transformed\face-detection-16.1.5\assets\models_bundled\MFT_fssd_fastgray.pb"/></source></dataSet><dataSet config=":expo-constants" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PostureApp\node_modules\expo-constants\android\build\intermediates\library_assets\debug\packageDebugAssets\out"><file name="app.config" path="D:\PostureApp\node_modules\expo-constants\android\build\intermediates\library_assets\debug\packageDebugAssets\out\app.config"/></source></dataSet><dataSet config=":expo-modules-core" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PostureApp\node_modules\expo-modules-core\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-worklets-core" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PostureApp\node_modules\react-native-worklets-core\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-svg" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PostureApp\node_modules\react-native-svg\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-reanimated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PostureApp\node_modules\react-native-reanimated\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-razorpay" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PostureApp\node_modules\react-native-razorpay\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-fs" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PostureApp\node_modules\react-native-fs\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PostureApp\node_modules\expo\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":shopify_react-native-skia" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PostureApp\node_modules\@shopify\react-native-skia\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-ml-kit_face-detection" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PostureApp\node_modules\@react-native-ml-kit\face-detection\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-community_netinfo" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PostureApp\node_modules\@react-native-community\netinfo\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-async-storage_async-storage" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PostureApp\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-edge-to-edge" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PostureApp\node_modules\react-native-edge-to-edge\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-vision-camera" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PostureApp\node_modules\react-native-vision-camera\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-vision-camera-mlkit" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PostureApp\node_modules\react-native-vision-camera-mlkit\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-screens" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PostureApp\node_modules\react-native-screens\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-safe-area-context" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PostureApp\node_modules\react-native-safe-area-context\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-gesture-handler" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PostureApp\node_modules\react-native-gesture-handler\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PostureApp\android\app\src\main\assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PostureApp\android\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PostureApp\android\app\build\intermediates\shader_assets\debug\compileDebugShaders\out"/></dataSet></merger>