{"logs": [{"outputFile": "com.postureapp.android.app-mergeDebugResources-68:/values-ne/values-ne.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7becb12098085a58be85c10a694bf84d\\transformed\\material-1.12.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,262,351,439,521,616,705,807,917,1004,1064,1130,1226,1292,1353,1458,1522,1594,1652,1726,1788,1842,1955,2015,2076,2135,2213,2337,2418,2500,2600,2685,2770,2906,2987,3070,3201,3284,3370,3432,3486,3552,3629,3708,3779,3862,3931,4007,4088,4156,4260,4351,4429,4522,4619,4693,4772,4870,4930,5018,5084,5172,5260,5322,5390,5453,5519,5624,5730,5825,5930,5996,6054,6138,6227,6303", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,88,87,81,94,88,101,109,86,59,65,95,65,60,104,63,71,57,73,61,53,112,59,60,58,77,123,80,81,99,84,84,135,80,82,130,82,85,61,53,65,76,78,70,82,68,75,80,67,103,90,77,92,96,73,78,97,59,87,65,87,87,61,67,62,65,104,105,94,104,65,57,83,88,75,72", "endOffsets": "257,346,434,516,611,700,802,912,999,1059,1125,1221,1287,1348,1453,1517,1589,1647,1721,1783,1837,1950,2010,2071,2130,2208,2332,2413,2495,2595,2680,2765,2901,2982,3065,3196,3279,3365,3427,3481,3547,3624,3703,3774,3857,3926,4002,4083,4151,4255,4346,4424,4517,4614,4688,4767,4865,4925,5013,5079,5167,5255,5317,5385,5448,5514,5619,5725,5820,5925,5991,6049,6133,6222,6298,6371"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,86,87,139,150,153,155,156,157,158,159,160,161,162,163,164,165,166,167,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,216,217,218", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "826,3755,3844,3932,4014,4109,4918,5020,5130,7829,7889,11846,13255,13468,13597,13702,13766,13838,13896,13970,14032,14086,14199,14259,14320,14379,14457,14648,14729,14811,14911,14996,15081,15217,15298,15381,15512,15595,15681,15743,15797,15863,15940,16019,16090,16173,16242,16318,16399,16467,16571,16662,16740,16833,16930,17004,17083,17181,17241,17329,17395,17483,17571,17633,17701,17764,17830,17935,18041,18136,18241,18307,18365,18529,18618,18694", "endLines": "22,50,51,52,53,54,62,63,64,86,87,139,150,153,155,156,157,158,159,160,161,162,163,164,165,166,167,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,216,217,218", "endColumns": "12,88,87,81,94,88,101,109,86,59,65,95,65,60,104,63,71,57,73,61,53,112,59,60,58,77,123,80,81,99,84,84,135,80,82,130,82,85,61,53,65,76,78,70,82,68,75,80,67,103,90,77,92,96,73,78,97,59,87,65,87,87,61,67,62,65,104,105,94,104,65,57,83,88,75,72", "endOffsets": "983,3839,3927,4009,4104,4193,5015,5125,5212,7884,7950,11937,13316,13524,13697,13761,13833,13891,13965,14027,14081,14194,14254,14315,14374,14452,14576,14724,14806,14906,14991,15076,15212,15293,15376,15507,15590,15676,15738,15792,15858,15935,16014,16085,16168,16237,16313,16394,16462,16566,16657,16735,16828,16925,16999,17078,17176,17236,17324,17390,17478,17566,17628,17696,17759,17825,17930,18036,18131,18236,18302,18360,18444,18613,18689,18762"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bf4e5700f050532bbd59ead7b0c07184\\transformed\\play-services-base-18.5.0\\res\\values-ne\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,454,584,697,864,996,1102,1203,1379,1489,1649,1778,1922,2070,2132,2200", "endColumns": "106,153,129,112,166,131,105,100,175,109,159,128,143,147,61,67,87", "endOffsets": "299,453,583,696,863,995,1101,1202,1378,1488,1648,1777,1921,2069,2131,2199,2287"}, "to": {"startLines": "66,67,68,69,70,71,72,73,75,76,77,78,79,80,81,82,83", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5294,5405,5563,5697,5814,5985,6121,6231,6500,6680,6794,6958,7091,7239,7391,7457,7529", "endColumns": "110,157,133,116,170,135,109,104,179,113,163,132,147,151,65,71,91", "endOffsets": "5400,5558,5692,5809,5980,6116,6226,6331,6675,6789,6953,7086,7234,7386,7452,7524,7616"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ecf58a1d5a3b5cc4faabc13e873181f2\\transformed\\react-android-0.79.5-debug\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,132,200,279,347,414,484", "endColumns": "76,67,78,67,66,69,68", "endOffsets": "127,195,274,342,409,479,548"}, "to": {"startLines": "65,151,152,154,168,219,220", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5217,13321,13389,13529,14581,18767,18837", "endColumns": "76,67,78,67,66,69,68", "endOffsets": "5289,13384,13463,13592,14643,18832,18901"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7bb9f3beb2453d006dfcb077349e135e\\transformed\\exoplayer-core-2.18.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,197,267,334,412,489,589,683", "endColumns": "70,70,69,66,77,76,99,93,68", "endOffsets": "121,192,262,329,407,484,584,678,747"}, "to": {"startLines": "112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10029,10100,10171,10241,10308,10386,10463,10563,10657", "endColumns": "70,70,69,66,77,76,99,93,68", "endOffsets": "10095,10166,10236,10303,10381,10458,10558,10652,10721"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\88b9f6ad7ed87d7d30486cdd2fcbb5f7\\transformed\\biometric-1.1.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,263,385,513,645,790,922,1070,1166,1306,1445", "endColumns": "117,89,121,127,131,144,131,147,95,139,138,130", "endOffsets": "168,258,380,508,640,785,917,1065,1161,1301,1440,1571"}, "to": {"startLines": "84,85,140,141,142,143,144,145,146,147,148,149", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7621,7739,11942,12064,12192,12324,12469,12601,12749,12845,12985,13124", "endColumns": "117,89,121,127,131,144,131,147,95,139,138,130", "endOffsets": "7734,7824,12059,12187,12319,12464,12596,12744,12840,12980,13119,13250"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\df4af9fc07c95635f45d375fd55e05f1\\transformed\\play-services-wallet-18.1.3\\res\\values-ne\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "79", "endOffsets": "281"}, "to": {"startLines": "222", "startColumns": "4", "startOffsets": "19007", "endColumns": "83", "endOffsets": "19086"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af9a9d7ace362c65c6063a55648731b1\\transformed\\appcompat-1.7.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,325,433,524,631,751,835,914,1005,1098,1193,1287,1387,1480,1575,1669,1760,1851,1937,2050,2151,2254,2367,2477,2594,2761,2872", "endColumns": "108,110,107,90,106,119,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,102,112,109,116,166,110,79", "endOffsets": "209,320,428,519,626,746,830,909,1000,1093,1188,1282,1382,1475,1570,1664,1755,1846,1932,2045,2146,2249,2362,2472,2589,2756,2867,2947"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,215", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "988,1097,1208,1316,1407,1514,1634,1718,1797,1888,1981,2076,2170,2270,2363,2458,2552,2643,2734,2820,2933,3034,3137,3250,3360,3477,3644,18449", "endColumns": "108,110,107,90,106,119,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,102,112,109,116,166,110,79", "endOffsets": "1092,1203,1311,1402,1509,1629,1713,1792,1883,1976,2071,2165,2265,2358,2453,2547,2638,2729,2815,2928,3029,3132,3245,3355,3472,3639,3750,18524"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6c6e309b72c7c7f21fbfce9b95a8faf6\\transformed\\core-1.13.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,261,363,469,567,667,775", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "153,256,358,464,562,662,770,871"}, "to": {"startLines": "55,56,57,58,59,60,61,221", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4198,4301,4404,4506,4612,4710,4810,18906", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "4296,4399,4501,4607,4705,4805,4913,19002"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f3b883529050a580787eb1fe00d61c7b\\transformed\\exoplayer-ui-2.18.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,310,529,731,824,917,1007,1108,1211,1297,1361,1456,1551,1623,1696,1756,1826,1943,2057,2177,2256,2348,2416,2502,2588,2673,2742,2805,2858,2916,2964,3025,3087,3158,3220,3282,3341,3408,3474,3537,3604,3658,3720,3796,3872", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,92,92,89,100,102,85,63,94,94,71,72,59,69,116,113,119,78,91,67,85,85,84,68,62,52,57,47,60,61,70,61,61,58,66,65,62,66,53,61,75,75,52", "endOffsets": "305,524,726,819,912,1002,1103,1206,1292,1356,1451,1546,1618,1691,1751,1821,1938,2052,2172,2251,2343,2411,2497,2583,2668,2737,2800,2853,2911,2959,3020,3082,3153,3215,3277,3336,3403,3469,3532,3599,3653,3715,3791,3867,3920"}, "to": {"startLines": "2,11,15,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,405,624,7955,8048,8141,8231,8332,8435,8521,8585,8680,8775,8847,8920,8980,9050,9167,9281,9401,9480,9572,9640,9726,9812,9897,9966,10726,10779,10837,10885,10946,11008,11079,11141,11203,11262,11329,11395,11458,11525,11579,11641,11717,11793", "endLines": "10,14,18,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "endColumns": "17,12,12,92,92,89,100,102,85,63,94,94,71,72,59,69,116,113,119,78,91,67,85,85,84,68,62,52,57,47,60,61,70,61,61,58,66,65,62,66,53,61,75,75,52", "endOffsets": "400,619,821,8043,8136,8226,8327,8430,8516,8580,8675,8770,8842,8915,8975,9045,9162,9276,9396,9475,9567,9635,9721,9807,9892,9961,10024,10774,10832,10880,10941,11003,11074,11136,11198,11257,11324,11390,11453,11520,11574,11636,11712,11788,11841"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8c47125c9a335e989accf2286b6eb952\\transformed\\play-services-basement-18.4.0\\res\\values-ne\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "159", "endOffsets": "354"}, "to": {"startLines": "74", "startColumns": "4", "startOffsets": "6336", "endColumns": "163", "endOffsets": "6495"}}]}]}