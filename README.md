# PostureApp 🧘‍♀️

[![Version](https://img.shields.io/badge/version-1.0.0-blue.svg)](https://github.com/postureapp/mobile)
[![Platform](https://img.shields.io/badge/platform-iOS%20%7C%20Android%20%7C%20Web-lightgrey.svg)](https://expo.dev)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Build Status](https://img.shields.io/badge/build-passing-brightgreen.svg)](https://github.com/postureapp/mobile/actions)

AI-powered posture improvement application with real-time pose detection, personalized wellness recommendations, and intelligent user preference analysis.

## 🌟 Features

### 🤖 AI-Powered Analysis
- **Real-time Pose Detection**: Advanced MediaPipe integration for accurate posture analysis
- **Intelligent Insights**: AI-generated recommendations based on user behavior and preferences
- **Personalized Questionnaire**: Comprehensive user profiling for tailored experiences
- **Smart Analytics**: Real-time progress tracking with Firebase Firestore integration

### 🎯 Core Functionality
- **Posture Monitoring**: Live posture analysis with instant feedback
- **Yoga & Exercise Library**: Curated exercises for posture improvement
- **Progress Tracking**: Detailed analytics and improvement metrics
- **Multi-language Support**: English and Hindi localization
- **Offline Capability**: Core features work without internet connection

### 🔒 Enterprise Features
- **Secure Authentication**: Firebase Auth with multi-factor support
- **Data Privacy**: GDPR-compliant data handling with user control
- **Performance Optimization**: Multi-layer caching and real-time sync
- **Cross-platform**: iOS, Android, and Web support

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and npm
- Expo CLI (`npm install -g @expo/cli`)
- EAS CLI (`npm install -g eas-cli`)
- iOS Simulator (macOS) or Android Studio

### Installation

```bash
# Clone the repository
git clone https://github.com/postureapp/mobile.git
cd PostureApp

# Install dependencies
npm install

# Start development server
npm start
```

### Development

```bash
# Start with cache clearing
npm run start:clear

# Platform-specific development
npm run ios      # iOS Simulator
npm run android  # Android Emulator
npm run web      # Web browser

# Type checking
npm run type-check

# Linting
npm run lint
```

## 🏗️ Production Builds

### Prebuild Setup

```bash
# Generate native code
npm run prebuild

# Clean prebuild (removes existing native code)
npm run prebuild:clean
```

### EAS Build (Recommended)

```bash
# Build for specific platforms
npm run build:android    # Android APK/AAB
npm run build:ios        # iOS IPA
npm run build:all        # Both platforms

# Build profiles
npm run build:preview     # Preview build
npm run build:production  # Production build

# Submit to app stores
npm run submit:android    # Google Play Store
npm run submit:ios        # Apple App Store
```

### Local Builds

```bash
# Android
cd android && ./gradlew assembleRelease

# iOS (macOS only)
cd ios && xcodebuild -workspace PostureApp.xcworkspace -scheme PostureApp archive
```

## 🎨 Branding & Assets

### Logo Usage

The PostureApp brand includes professionally designed SVG assets:

- **Main Logo** (`assets/logo.svg`): Use for headers, splash screens, marketing
- **App Icon** (`assets/app-icon.svg`): Use for navigation, small spaces, app stores

### Brand Colors

```css
/* Primary Brand Colors */
--primary: #FF6B35;           /* Main brand color */
--primary-light: #FF8A65;     /* Light variant */
--primary-dark: #E55A2B;      /* Dark variant */

/* Secondary Colors */
--wellness-green: #4ADE80;    /* Wellness accent */
--wellness-green-light: #34D399;

/* Background Colors */
--brand-background: #FF6B35;
--brand-background-light: #FFF7F5;
```

### Usage Guidelines

- **Minimum Logo Size**: 120px width
- **Minimum Icon Size**: 24px
- **Clear Space**: 20px around logo, 8px around icon
- **Backgrounds**: Logo works best on white/light backgrounds
- **Icon**: Designed to work on any background

### Asset Sizes

| Context | Logo Size | Icon Size |
|---------|-----------|----------|
| Splash Screen | 200x200px | - |
| Header | 150x150px | - |
| Navigation | - | 32x32px |
| Button | - | 24x24px |
| Large Display | 300x300px | - |

## 📱 App Store Assets

### Required Sizes

**iOS App Store**
- App Icon: 1024x1024px
- Screenshots: 1290x2796px (iPhone 14 Pro Max)
- Preview Video: 1200x675px

**Google Play Store**
- App Icon: 512x512px
- Screenshots: 1080x1920px
- Feature Graphic: 1024x500px

### Marketing Materials

All marketing assets should maintain brand consistency:
- Use official brand colors
- Include proper logo placement with clear space
- Follow typography guidelines
- Maintain professional, wellness-focused aesthetic

## 🏗️ Architecture

### Tech Stack
- **Framework**: React Native with Expo
- **Navigation**: React Navigation 7
- **State Management**: React Context + Hooks
- **Backend**: Firebase (Auth, Firestore, Analytics)
- **AI/ML**: MediaPipe for pose detection
- **Styling**: Custom design system with TypeScript
- **Internationalization**: react-i18next
- **Caching**: Multi-layer caching strategy

### Project Structure

```
src/
├── assets/           # Images, icons, yoga poses
├── components/       # Reusable UI components
├── constants/        # Design tokens, branding
├── contexts/         # React contexts (Auth, etc.)
├── hooks/           # Custom React hooks
├── locales/         # i18n translations
├── navigation/      # Navigation configuration
├── screens/         # Screen components
├── services/        # API services, Firebase
├── types/           # TypeScript type definitions
└── utils/           # Utility functions
```

### Key Services

- **Authentication**: Firebase Auth with secure token management
- **Data Storage**: Firestore with real-time sync and offline support
- **Pose Detection**: MediaPipe integration with performance optimization
- **Analytics**: User behavior tracking and insights generation
- **Caching**: Smart caching for offline functionality
- **Questionnaire**: Intelligent user preference analysis

## 🔧 Configuration

### Environment Variables

Create `.env` file in project root:

```env
# Firebase Configuration
FIREBASE_API_KEY=your_api_key
FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
FIREBASE_PROJECT_ID=your_project_id
FIREBASE_STORAGE_BUCKET=your_project.appspot.com
FIREBASE_MESSAGING_SENDER_ID=123456789
FIREBASE_APP_ID=1:123456789:web:abcdef

# API Configuration
API_BASE_URL=https://api.postureapp.com
API_VERSION=v1

# Feature Flags
ENABLE_ANALYTICS=true
ENABLE_POSE_DETECTION=true
ENABLE_OFFLINE_MODE=true
```

### Build Configuration

The app uses EAS Build with three profiles:

- **Development**: Debug builds with development client
- **Preview**: Release builds for internal testing
- **Production**: Optimized builds for app stores

## 📊 Performance

### Optimization Features

- **Bundle Splitting**: Lazy loading for better startup time
- **Image Optimization**: WebP format with fallbacks
- **Caching Strategy**: Multi-layer caching (memory, disk, network)
- **Code Splitting**: Dynamic imports for large features
- **Tree Shaking**: Unused code elimination

### Performance Metrics

- **App Startup**: <2 seconds on mid-range devices
- **Pose Detection**: 30 FPS real-time analysis
- **Cache Hit Rate**: 85%+ for frequently accessed data
- **Bundle Size**: <50MB total app size

## 🧪 Testing

```bash
# Run tests
npm test

# Run tests with coverage
npm run test:coverage

# E2E testing
npm run test:e2e

# Performance testing
npm run test:performance
```

## 🚀 Deployment

### Automated Deployment

```bash
# Complete release preparation
npm run prepare-release

# Deploy to staging
eas update --branch staging

# Deploy to production
eas update --branch production
```

### Manual Deployment

1. **Code Quality**: Run `npm run type-check` and `npm run lint`
2. **Build**: Create production builds with `npm run build:production`
3. **Test**: Verify builds on physical devices
4. **Submit**: Upload to app stores with `npm run submit:android` / `npm run submit:ios`

## 📈 Analytics & Monitoring

### Integrated Analytics

- **User Behavior**: Firebase Analytics
- **Performance**: Custom performance monitoring
- **Error Tracking**: Comprehensive error logging
- **Usage Metrics**: Feature adoption and user engagement

### Key Metrics

- Daily/Monthly Active Users
- Session Duration
- Feature Usage
- Pose Detection Accuracy
- User Satisfaction Scores

## 🤝 Contributing

### Development Workflow

1. **Fork** the repository
2. **Create** a feature branch (`git checkout -b feature/amazing-feature`)
3. **Commit** changes (`git commit -m 'Add amazing feature'`)
4. **Push** to branch (`git push origin feature/amazing-feature`)
5. **Open** a Pull Request

### Code Standards

- **TypeScript**: Strict mode enabled
- **ESLint**: Airbnb configuration
- **Prettier**: Automatic code formatting
- **Conventional Commits**: Standardized commit messages

### Pull Request Guidelines

- Include comprehensive tests
- Update documentation
- Follow existing code patterns
- Ensure all checks pass
- Add screenshots for UI changes

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **MediaPipe**: Google's ML framework for pose detection
- **Firebase**: Backend infrastructure and analytics
- **Expo**: Development platform and build tools
- **React Native Community**: Open source components and tools

## 📞 Support

- **Documentation**: [docs.postureapp.com](https://docs.postureapp.com)
- **Issues**: [GitHub Issues](https://github.com/postureapp/mobile/issues)
- **Email**: <EMAIL>
- **Community**: [Discord Server](https://discord.gg/postureapp)

---

**Built with ❤️ by the PostureApp Team**

*Improving posture, one user at a time* 🧘‍♀️

**गलत posture से confidence कम? → रोज़ाना yoga-guided सुधार → सीधे खड़ें, बेहतर महसूस करें → Free शुरुआत!**

A React Native app designed specifically for urban Indians aged 22-45, combining AR-guided yoga with AI-powered posture analysis to improve posture and overall well-being.

## 🌟 Features

### Core Features
- **AR Posture Detection**: Real-time posture analysis using MediaPipe and device camera
- **AI-Guided Yoga**: 15+ yoga poses specifically designed for posture improvement
- **Multilingual Support**: Hindi, English, and regional language support
- **Progress Tracking**: Detailed analytics and improvement tracking
- **Family Health**: Track family members' posture improvement journey

### Subscription Tiers
- **Free (₹0)**: 5 posture checks/month, basic exercises, ad-supported
- **Plus (₹99/month)**: Unlimited checks, ad-free experience
- **Pro (₹199/month)**: AI coaching, family tracking, advanced analytics
- **Premium (₹299/month)**: Personalized programs, wearables integration

### Payment Integration
- UPI, Paytm, Razorpay integration
- EMI options available
- Secure Indian payment gateways

## 🚀 Technology Stack

- **Frontend**: React Native with Expo
- **Language**: TypeScript
- **Navigation**: React Navigation v6
- **State Management**: React Context API
- **Backend**: Firebase (Auth, Firestore, Storage)
- **AR/Pose Detection**: MediaPipe, Expo Camera
- **Payments**: Razorpay, UPI integration
- **Internationalization**: i18next
- **UI Components**: Custom components with Expo Vector Icons
- **Animations**: React Native Reanimated

## 📱 Screenshots

*Screenshots will be added after UI implementation*

## 🛠️ Installation & Setup

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn
- Expo CLI
- Android Studio (for Android development)
- Xcode (for iOS development, macOS only)

### 1. Clone the Repository
```bash
git clone https://github.com/your-username/PostureApp.git
cd PostureApp
```

### 2. Install Dependencies
```bash
npm install
# or
yarn install
```

### 3. Environment Configuration
1. Copy the example environment file:
```bash
cp .env.example .env
```

2. Configure your environment variables in `.env`:
```env
# Firebase Configuration
EXPO_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
EXPO_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
# ... other Firebase config

# Payment Configuration
EXPO_PUBLIC_RAZORPAY_KEY_ID=your_razorpay_key

# Other configurations...
```

### 4. Firebase Setup
1. Create a new Firebase project at [Firebase Console](https://console.firebase.google.com/)
2. Enable Authentication (Email/Password, Google)
3. Create Firestore database
4. Enable Storage
5. Download and configure Firebase config

### 5. Razorpay Setup
1. Create account at [Razorpay](https://razorpay.com/)
2. Get API keys from dashboard
3. Configure webhook endpoints

### 6. Run the Application
```bash
# Start Expo development server
npx expo start

# Run on Android
npx expo run:android

# Run on iOS (macOS only)
npx expo run:ios

# Run on web
npx expo start --web
```

## 📁 Project Structure

```
PostureApp/
├── src/
│   ├── components/          # Reusable UI components
│   ├── screens/            # Screen components
│   │   ├── SplashScreen.tsx
│   │   ├── AuthScreen.tsx
│   │   ├── HomeScreen.tsx
│   │   ├── PostureCheckScreen.tsx
│   │   ├── YogaExercisesScreen.tsx
│   │   ├── ProgressScreen.tsx
│   │   └── ...
│   ├── navigation/         # Navigation configuration
│   │   └── AppNavigator.tsx
│   ├── services/          # API and business logic
│   │   ├── authService.ts
│   │   ├── poseDetectionService.ts
│   │   ├── paymentService.ts
│   │   └── firebase.ts
│   ├── types/             # TypeScript type definitions
│   │   └── index.ts
│   ├── hooks/             # Custom React hooks
│   ├── utils/             # Utility functions
│   ├── constants/         # App constants
│   ├── assets/            # Images, icons, etc.
│   └── locales/           # Internationalization
│       ├── en/
│       ├── hi/
│       └── i18n.ts
├── assets/                # Expo assets
├── App.tsx               # Main app component
├── package.json
├── tsconfig.json
├── .env.example
└── README.md
```

## 🎯 Key Features Implementation

### AR Posture Detection
- Uses MediaPipe Pose for real-time pose estimation
- Analyzes key body landmarks for posture assessment
- Provides instant feedback and scoring
- Supports both front and back camera

### Yoga Exercise Library
- 15+ carefully selected poses for posture improvement
- Step-by-step instructions in Hindi and English
- Duration tracking and accuracy measurement
- Progressive difficulty levels

### Subscription Management
- Tiered subscription model
- Indian payment gateway integration
- Feature-based access control
- Subscription analytics

### Multilingual Support
- Hindi and English translations
- RTL support for regional languages
- Cultural adaptation of content
- Localized date/time formats

## 🔧 Development

### Code Style
- ESLint and Prettier configured
- TypeScript strict mode enabled
- Consistent naming conventions
- Component-based architecture

### Testing
```bash
# Run tests
npm test

# Run tests with coverage
npm run test:coverage
```

### Building for Production
```bash
# Build for Android
npx expo build:android

# Build for iOS
npx expo build:ios

# Create production bundle
npx expo export
```

## 📊 Business Model

### Revenue Streams
- **Freemium Subscriptions (50%)**: Plus, Pro, Premium tiers
- **In-App Advertising (40%)**: Targeted health and wellness ads
- **Corporate Partnerships (10%)**: B2B wellness programs

### Target Market
- **IT/Corporate Workers (45%)**: Bangalore, Hyderabad, Pune
- **Students (25%)**: Study posture improvement
- **Urban Professionals (30%)**: Delhi, Mumbai, Chennai

### Growth Strategy
- Influencer marketing on Instagram/YouTube
- Corporate wellness partnerships
- Tier 2 city expansion
- Regional language content

## 🚀 Deployment

### App Store Deployment
1. Configure app.json for production
2. Generate production builds
3. Submit to Google Play Store and Apple App Store
4. Configure Firebase for production

### Backend Deployment
- Firebase hosting for web version
- Cloud Functions for serverless backend
- Firestore for database
- Firebase Storage for media files

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines
- Follow TypeScript best practices
- Write tests for new features
- Update documentation
- Follow the existing code style
- Test on both Android and iOS

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 📞 Support

- **Email**: <EMAIL>
- **Website**: https://postureapp.in
- **Documentation**: https://docs.postureapp.in

## 🙏 Acknowledgments

- MediaPipe team for pose detection technology
- React Native community
- Expo team for development tools
- Firebase for backend services
- Indian payment gateway providers

## 📈 Roadmap

### Phase 1 (Current)
- ✅ Core posture detection
- ✅ Basic yoga exercises
- ✅ User authentication
- ✅ Subscription system

### Phase 2 (Next 3 months)
- [ ] Advanced AI coaching
- [ ] Wearable device integration
- [ ] Social features
- [ ] Corporate dashboard

### Phase 3 (6 months)
- [ ] Machine learning personalization
- [ ] Telemedicine integration
- [ ] Advanced analytics
- [ ] International expansion

---

**Made with ❤️ in India for better posture and healthier lives**

*गलत posture से confidence कम? PostureApp के साथ अपना posture सुधारें!*