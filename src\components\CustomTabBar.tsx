import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Platform,
  Animated,
} from 'react-native';
import { BottomTabBarProps } from '@react-navigation/bottom-tabs';
import { LinearGradient } from 'expo-linear-gradient';
import {
  Home,
  Camera,
  Dumbbell,
  TrendingUp,
  User
} from 'lucide-react-native';

const CustomTabBar: React.FC<BottomTabBarProps> = ({ state, descriptors, navigation }) => {
  // Check if current screen should hide tab bar
  const currentRoute = state.routes[state.index];
  const currentOptions = descriptors[currentRoute.key].options;
  
  if (currentOptions.tabBarStyle && typeof currentOptions.tabBarStyle === 'object' && 'display' in currentOptions.tabBarStyle && currentOptions.tabBarStyle.display === 'none') {
    return null;
  }
  const getIcon = (routeName: string, focused: boolean) => {
    const size = 24;
    const color = focused ? '#FFFFFF' : 'rgba(255, 255, 255, 0.5)';
    const strokeWidth = focused ? 2.5 : 2;

    switch (routeName) {
      case 'Home':
        return <Home size={size} color={color} strokeWidth={strokeWidth} fill={focused ? color : 'none'} />;
      case 'PostureCheck':
        return <Camera size={size} color={color} strokeWidth={strokeWidth} fill={focused ? color : 'none'} />;
      case 'Exercises':
        return <Dumbbell size={size} color={color} strokeWidth={strokeWidth} fill={focused ? color : 'none'} />;
      case 'Progress':
        return <TrendingUp size={size} color={color} strokeWidth={strokeWidth} fill={focused ? color : 'none'} />;
      case 'Profile':
        return <User size={size} color={color} strokeWidth={strokeWidth} fill={focused ? color : 'none'} />;
      default:
        return <Home size={size} color={color} strokeWidth={strokeWidth} fill={focused ? color : 'none'} />;
    }
  };

  const getTabLabel = (routeName: string) => {
    switch (routeName) {
      case 'Home':
        return 'Home';
      case 'PostureCheck':
        return 'Check';
      case 'Exercises':
        return 'Exercises';
      case 'Progress':
        return 'Progress';
      case 'Profile':
        return 'Profile';
      default:
        return routeName;
    }
  };

  return (
    <View style={styles.container}>
      {/* Background with blur effect */}
      <LinearGradient
        colors={['rgba(0, 0, 0, 0.98)', 'rgba(0, 0, 0, 0.95)']}
        style={styles.background}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      />

      {/* Tab items */}
      <View style={styles.tabContainer}>
        {state.routes.map((route, index) => {
          const { options } = descriptors[route.key];
          const isFocused = state.index === index;

          const onPress = () => {
            const event = navigation.emit({
              type: 'tabPress',
              target: route.key,
              canPreventDefault: true,
            });

            if (!isFocused && !event.defaultPrevented) {
              navigation.navigate(route.name);
            }
          };

          const onLongPress = () => {
            navigation.emit({
              type: 'tabLongPress',
              target: route.key,
            });
          };

          return (
            <TouchableOpacity
              key={route.key}
              accessibilityRole="button"
              accessibilityState={isFocused ? { selected: true } : {}}
              accessibilityLabel={options.tabBarAccessibilityLabel}
              testID={`tab-${route.name}`}
              onPress={onPress}
              onLongPress={onLongPress}
              style={styles.tabItem}
              activeOpacity={0.6}
            >
              {/* Icon */}
              <View style={styles.iconContainer}>
                {getIcon(route.name, isFocused)}
              </View>

              {/* Label */}
              <Text style={[
                styles.label,
                isFocused && styles.labelActive
              ]}>
                {getTabLabel(route.name)}
              </Text>
            </TouchableOpacity>
          );
        })}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: Platform.OS === 'ios' ? 90 : 70,
  },
  background: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  tabContainer: {
    flexDirection: 'row',
    paddingTop: 8,
    paddingBottom: Platform.OS === 'ios' ? 28 : 12,
    paddingHorizontal: 0,
    justifyContent: 'space-around',
    alignItems: 'center',
    height: '100%',
  },
  tabItem: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 6,
    paddingHorizontal: 4,
  },
  iconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 2,
    height: 28,
  },
  label: {
    fontSize: 10,
    fontWeight: '500',
    color: 'rgba(255, 255, 255, 0.5)',
    letterSpacing: 0.2,
    textAlign: 'center',
    marginTop: 2,
  },
  labelActive: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
});

export default CustomTabBar;