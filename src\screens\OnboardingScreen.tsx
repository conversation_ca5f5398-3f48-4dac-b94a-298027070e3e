import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  Animated,
  StatusBar,
  Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList } from '../types';
import { Theme } from '../constants/designTokens';

type OnboardingNavigationProp = StackNavigationProp<RootStackParamList>;

const { width, height } = Dimensions.get('window');

interface OnboardingStep {
  id: string;
  title: string;
  subtitle: string;
  description: string;
  icon: string;
  gradient: [string, string];
}

const OnboardingScreen: React.FC = () => {
  const { t } = useTranslation();
  const navigation = useNavigation<OnboardingNavigationProp>();
  
  const [currentStep, setCurrentStep] = useState(0);
  const scrollViewRef = useRef<ScrollView>(null);
  const fadeAnim = useRef(new Animated.Value(1)).current;
  const slideAnim = useRef(new Animated.Value(0)).current;

  const onboardingSteps: OnboardingStep[] = [
    {
      id: '1',
      title: 'AI-Powered Analysis',
      subtitle: 'Precision Meets Intelligence',
      description: 'Advanced computer vision technology analyzes your posture in real-time, providing instant feedback and personalized recommendations.',
      icon: 'scan-outline',
      gradient: ['#000000', '#1a1a1a'] as [string, string],
    },
    {
      id: '2',
      title: 'Personalized Guidance',
      subtitle: 'Tailored to Your Needs',
      description: 'Receive customized exercise routines and posture corrections based on your unique body mechanics and lifestyle.',
      icon: 'person-outline',
      gradient: ['#1a1a1a', '#2d2d2d'] as [string, string],
    },
    {
      id: '3',
      title: 'Track Your Progress',
      subtitle: 'Measure Your Success',
      description: 'Monitor your improvement over time with detailed analytics and celebrate your journey to better posture.',
      icon: 'trending-up-outline',
      gradient: ['#2d2d2d', '#000000'] as [string, string],
    },
  ];

  const animateTransition = (direction: 'next' | 'prev') => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: direction === 'next' ? -50 : 50,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start(() => {
      // Reset and animate in
      slideAnim.setValue(direction === 'next' ? 50 : -50);
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.spring(slideAnim, {
          toValue: 0,
          tension: 100,
          friction: 8,
          useNativeDriver: true,
        }),
      ]).start();
    });
  };

  const handleNext = async () => {
    if (currentStep < onboardingSteps.length - 1) {
      animateTransition('next');
      setCurrentStep(currentStep + 1);
    } else {
      try {
        const { AppStorage, STORAGE_KEYS } = await import('../utils/storage');
        await AppStorage.setItem(STORAGE_KEYS.ONBOARDING_COMPLETE, 'true');
      } catch (error) {
        // Silently handle error - onboarding completion will be checked again on next app launch
      }
      navigation.navigate('Main');
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      animateTransition('prev');
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSkip = async () => {
    try {
      console.log('Skipping onboarding - saving completion status...');
      const { AppStorage, STORAGE_KEYS } = await import('../utils/storage');
      await AppStorage.setItem(STORAGE_KEYS.ONBOARDING_COMPLETE, 'true');
      console.log('Onboarding skip - completion saved successfully');
      
      // Verify it was saved
      const saved = await AppStorage.getItem(STORAGE_KEYS.ONBOARDING_COMPLETE);
      console.log('Verification - onboarding status after skip:', saved);
    } catch (error) {
      console.error('Error saving onboarding status:', error);
    }
    navigation.navigate('Main');
  };

  const currentStepData = onboardingSteps[currentStep];

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#000000" />
      
      {/* Dynamic background gradient */}
      <LinearGradient
        colors={currentStepData.gradient}
        style={StyleSheet.absoluteFillObject}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.skipButton}
          onPress={handleSkip}
          activeOpacity={0.7}
        >
          <Text style={styles.skipText}>Skip</Text>
        </TouchableOpacity>
      </View>
      
      {/* Progress indicator */}
      <View style={styles.progressContainer}>
        {onboardingSteps.map((_, index) => (
          <View
            key={index}
            style={[
              styles.progressDot,
              index === currentStep && styles.progressDotActive,
              index < currentStep && styles.progressDotCompleted,
            ]}
          />
        ))}
      </View>
      
      {/* Main content */}
      <Animated.View
        style={[
          styles.content,
          {
            opacity: fadeAnim,
            transform: [{ translateX: slideAnim }],
          },
        ]}
      >
        {/* Icon */}
        <View style={styles.iconContainer}>
          <View style={styles.iconWrapper}>
            <Ionicons 
              name={currentStepData.icon as any} 
              size={60} 
              color="#FFFFFF" 
            />
          </View>
        </View>
        
        {/* Text content */}
        <View style={styles.textContainer}>
          <Text style={styles.subtitle}>{currentStepData.subtitle}</Text>
          <Text style={styles.title}>{currentStepData.title}</Text>
          <Text style={styles.description}>{currentStepData.description}</Text>
        </View>
      </Animated.View>
      
      {/* Navigation */}
      <View style={styles.navigation}>
        <TouchableOpacity
          style={[
            styles.navButton,
            styles.prevButton,
            currentStep === 0 && styles.navButtonDisabled,
          ]}
          onPress={handlePrevious}
          disabled={currentStep === 0}
          activeOpacity={0.7}
        >
          <Ionicons 
            name="chevron-back" 
            size={24} 
            color={currentStep === 0 ? 'rgba(255, 255, 255, 0.3)' : '#FFFFFF'} 
          />
        </TouchableOpacity>
        
        <View style={styles.stepIndicator}>
          <Text style={styles.stepText}>
            {currentStep + 1} of {onboardingSteps.length}
          </Text>
        </View>
        
        <TouchableOpacity
          style={[
            styles.navButton, 
            currentStep === onboardingSteps.length - 1 ? styles.getStartedButton : styles.nextButton
          ]}
          onPress={handleNext}
          activeOpacity={0.8}
        >
          {currentStep === onboardingSteps.length - 1 ? (
            <View style={styles.getStartedContent}>
              <Text style={styles.getStartedText}>Get Started</Text>
              <Ionicons name="arrow-forward" size={18} color="#000000" style={styles.getStartedIcon} />
            </View>
          ) : (
            <Ionicons name="chevron-forward" size={24} color="#000000" />
          )}
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    paddingHorizontal: 24,
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
    paddingBottom: 20,
  },
  skipButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  skipText: {
    fontSize: 16,
    fontWeight: '400',
    color: 'rgba(255, 255, 255, 0.7)',
    letterSpacing: 0.5,
  },
  progressContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 24,
    marginBottom: 60,
    gap: 12,
  },
  progressDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
  },
  progressDotActive: {
    backgroundColor: '#FFFFFF',
    width: 24,
  },
  progressDotCompleted: {
    backgroundColor: 'rgba(255, 255, 255, 0.6)',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  iconContainer: {
    marginBottom: 60,
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconWrapper: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  textContainer: {
    alignItems: 'center',
    maxWidth: width - 80,
  },
  subtitle: {
    fontSize: 14,
    fontWeight: '400',
    color: 'rgba(255, 255, 255, 0.7)',
    letterSpacing: 1,
    marginBottom: 8,
    textAlign: 'center',
    textTransform: 'uppercase',
  },
  title: {
    fontSize: 32,
    fontWeight: '300',
    color: '#FFFFFF',
    letterSpacing: 1,
    marginBottom: 24,
    textAlign: 'center',
    lineHeight: 40,
  },
  description: {
    fontSize: 16,
    fontWeight: '400',
    color: 'rgba(255, 255, 255, 0.8)',
    lineHeight: 24,
    textAlign: 'center',
    letterSpacing: 0.3,
  },
  navigation: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 24,
    paddingBottom: Platform.OS === 'ios' ? 40 : 24,
    paddingTop: 20,
  },
  navButton: {
    width: 56,
    height: 56,
    borderRadius: 28,
    alignItems: 'center',
    justifyContent: 'center',
  },
  prevButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  nextButton: {
    backgroundColor: '#FFFFFF',
  },
  getStartedButton: {
    backgroundColor: '#FFFFFF',
    width: 140,
    height: 56,
    borderRadius: 28,
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
  },
  getStartedContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  navButtonDisabled: {
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  stepIndicator: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  stepText: {
    fontSize: 14,
    fontWeight: '400',
    color: 'rgba(255, 255, 255, 0.6)',
    letterSpacing: 0.5,
  },
  getStartedText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    letterSpacing: 0.3,
  },
  getStartedIcon: {
    marginLeft: 8,
  },
});

export default OnboardingScreen;