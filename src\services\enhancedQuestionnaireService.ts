import {
  QuestionnaireQuestion,
  QuestionnaireSession,
  QuestionnaireResponse,
  UserInsight,
  PersonalizedRecommendation,
  UserPreferences,
} from '../types';
import { dataService } from './dataService';
import { recommendationEngine } from './recommendationEngine';
import { logger } from '../utils/logger';
import { cacheManager } from '../utils/cache';
import { supabase } from '../lib/supabase';
import { AuthUtils } from '../utils/authUtils';
import { generateUUID, generateResponseId, generateSessionId } from '../utils/uuid';

/**
 * Enterprise-level questionnaire service with advanced features
 * Implements adaptive questioning, real-time validation, and intelligent branching
 */
export class EnhancedQuestionnaireService {
  private static instance: EnhancedQuestionnaireService;
  private questionBank: QuestionnaireQuestion[] = [];
  private currentSession: QuestionnaireSession | null = null;

  private constructor() {
    this.initializeEnterpriseQuestionBank();
  }

  public static getInstance(): EnhancedQuestionnaireService {
    if (!EnhancedQuestionnaireService.instance) {
      EnhancedQuestionnaireService.instance = new EnhancedQuestionnaireService();
    }
    return EnhancedQuestionnaireService.instance;
  }

  /**
   * Initialize comprehensive enterprise-level question bank
   */
  private initializeEnterpriseQuestionBank(): void {
    this.questionBank = [
      // Welcome and Introduction
      {
        id: 'welcome',
        type: 'info_display',
        category: 'introduction',
        title: 'Welcome to Your Personalized Health Journey',
        titleHindi: 'आपकी व्यक्तिगत स्वास्थ्य यात्रा में आपका स्वागत है',
        description: 'This assessment will help us create a personalized plan tailored specifically for you. It takes about 5-7 minutes to complete.',
        descriptionHindi: 'यह मूल्यांकन हमें आपके लिए विशेष रूप से तैयार की गई व्यक्तिगत योजना बनाने में मदद करेगा। इसे पूरा करने में लगभग 5-7 मिनट लगते हैं।',
        required: false,
        order: 0,
        validation: {
          required: false,
        },
      },

      // Demographics
      {
        id: 'age_group',
        type: 'single_choice',
        category: 'demographics',
        title: 'What is your age group?',
        titleHindi: 'आपकी आयु क्या है?',
        description: 'This helps us customize recommendations for your life stage',
        descriptionHindi: 'यह हमें आपके जीवन चरण के लिए सिफारिशों को अनुकूलित करने में मदद करता है',
        options: [
          { value: '18-25', label: '18-25 years', labelHindi: '18-25 वर्ष', icon: '🧑' },
          { value: '26-35', label: '26-35 years', labelHindi: '26-35 वर्ष', icon: '👨‍💼' },
          { value: '36-45', label: '36-45 years', labelHindi: '36-45 वर्ष', icon: '👩‍💼' },
          { value: '46-55', label: '46-55 years', labelHindi: '46-55 वर्ष', icon: '👨‍🦳' },
          { value: '55+', label: '55+ years', labelHindi: '55+ वर्ष', icon: '👴' },
        ],
        required: true,
        order: 1,
        validation: {
          required: true,
          errorMessage: 'Please select your age group',
          errorMessageHindi: 'कृपया अपना आयु समूह चुनें',
        },
      },

      // Work Environment Assessment
      {
        id: 'work_setup',
        type: 'single_choice',
        category: 'work_environment',
        title: 'How would you describe your primary work setup?',
        titleHindi: 'आप अपने मुख्य कार्य सेटअप का वर्णन कैसे करेंगे?',
        description: 'Your workspace significantly impacts your posture throughout the day',
        descriptionHindi: 'आपका कार्यक्षेत्र दिन भर आपकी मुद्रा को महत्वपूर्ण रूप से प्रभावित करता है',
        options: [
          { value: 'excellent', label: 'Ergonomic desk with adjustable chair', labelHindi: 'समायोज्य कुर्सी के साथ एर्गोनॉमिक डेस्क', icon: '🪑' },
          { value: 'good', label: 'Standard desk and office chair', labelHindi: 'मानक डेस्क और ऑफिस चेयर', icon: '🏢' },
          { value: 'average', label: 'Basic desk setup', labelHindi: 'बुनियादी डेस्क सेटअप', icon: '📋' },
          { value: 'poor', label: 'Couch, bed, or non-ergonomic setup', labelHindi: 'सोफा, बिस्तर, या गैर-एर्गोनॉमिक सेटअप', icon: '🛋️' },
        ],
        required: true,
        order: 2,
        validation: {
          required: true,
          errorMessage: 'Please describe your work setup',
          errorMessageHindi: 'कृपया अपने कार्य सेटअप का वर्णन करें',
        },
      },

      // Screen Time Assessment
      {
        id: 'daily_screen_time',
        type: 'rating_scale',
        category: 'digital_wellness',
        title: 'How many hours do you spend looking at screens daily?',
        titleHindi: 'आप दैनिक कितने घंटे स्क्रीन देखने में बिताते हैं?',
        description: 'Include work, entertainment, and mobile device usage',
        descriptionHindi: 'कार्य, मनोरंजन और मोबाइल डिवाइस उपयोग शामिल करें',
        scaleMin: 2,
        scaleMax: 16,
        scaleStep: 1,
        scaleLabels: {
          2: '2 hours',
          8: '8 hours',
          16: '16+ hours',
        },
        scaleLabelsHindi: {
          2: '2 घंटे',
          8: '8 घंटे',
          16: '16+ घंटे',
        },
        required: true,
        order: 3,
        validation: {
          required: true,
          min: 2,
          max: 16,
          errorMessage: 'Please select your daily screen time',
          errorMessageHindi: 'कृपया अपना दैनिक स्क्रीन समय चुनें',
        },
      },

      // Pain Assessment with Body Mapping
      {
        id: 'pain_mapping',
        type: 'body_map',
        category: 'health_assessment',
        title: 'Mark any areas where you experience pain or discomfort',
        titleHindi: 'उन क्षेत्रों को चिह्नित करें जहाँ आप दर्द या असुविधा महसूस करते हैं',
        description: 'Tap on the body diagram to indicate problem areas',
        descriptionHindi: 'समस्या वाले क्षेत्रों को इंगित करने के लिए शरीर के आरेख पर टैप करें',
        bodyAreas: [
          { id: 'head', label: 'Head', labelHindi: 'सिर', coordinates: { x: 50, y: 10 } },
          { id: 'neck', label: 'Neck', labelHindi: 'गर्दन', coordinates: { x: 50, y: 20 } },
          { id: 'left_shoulder', label: 'Left Shoulder', labelHindi: 'बायाँ कंधा', coordinates: { x: 30, y: 30 } },
          { id: 'right_shoulder', label: 'Right Shoulder', labelHindi: 'दायाँ कंधा', coordinates: { x: 70, y: 30 } },
          { id: 'upper_back', label: 'Upper Back', labelHindi: 'ऊपरी पीठ', coordinates: { x: 50, y: 40 } },
          { id: 'lower_back', label: 'Lower Back', labelHindi: 'निचली पीठ', coordinates: { x: 50, y: 60 } },
          { id: 'left_hip', label: 'Left Hip', labelHindi: 'बायाँ कूल्हा', coordinates: { x: 40, y: 70 } },
          { id: 'right_hip', label: 'Right Hip', labelHindi: 'दायाँ कूल्हा', coordinates: { x: 60, y: 70 } },
        ],
        required: false,
        order: 4,
        validation: {
          required: false,
        },
      },

      // Work Breaks Assessment
      {
        id: 'work_breaks',
        type: 'single_choice',
        category: 'work_environment',
        title: 'How often do you take breaks from your work/screen?',
        titleHindi: 'आप अपने काम/स्क्रीन से कितनी बार ब्रेक लेते हैं?',
        description: 'Regular breaks are crucial for maintaining good posture',
        descriptionHindi: 'अच्छी मुद्रा बनाए रखने के लिए नियमित ब्रेक महत्वपूर्ण हैं',
        options: [
          { value: 'every_30min', label: 'Every 30 minutes', labelHindi: 'हर 30 मिनट में', icon: '⏰' },
          { value: 'hourly', label: 'Every hour', labelHindi: 'हर घंटे', icon: '🕐' },
          { value: 'few_hours', label: 'Every 2-3 hours', labelHindi: 'हर 2-3 घंटे में', icon: '⏳' },
          { value: 'rarely', label: 'Rarely during work', labelHindi: 'काम के दौरान शायद ही कभी', icon: '😓' },
          { value: 'never', label: 'Almost never', labelHindi: 'लगभग कभी नहीं', icon: '🚫' },
        ],
        required: true,
        order: 5,
        validation: {
          required: true,
          errorMessage: 'Please select your break frequency',
          errorMessageHindi: 'कृपया अपनी ब्रेक आवृत्ति चुनें',
        },
      },

      // Fitness Level Assessment
      {
        id: 'fitness_level',
        type: 'single_choice',
        category: 'fitness_assessment',
        title: 'How would you describe your current fitness level?',
        titleHindi: 'आप अपने वर्तमान फिटनेस स्तर का वर्णन कैसे करेंगे?',
        description: 'This helps us recommend appropriate exercises',
        descriptionHindi: 'यह हमें उपयुक्त व्यायाम की सिफारिश करने में मदद करता है',
        options: [
          { value: 'beginner', label: 'Beginner - New to exercise', labelHindi: 'शुरुआती - व्यायाम में नया', icon: '🌱' },
          { value: 'intermediate', label: 'Intermediate - Regular exercise', labelHindi: 'मध्यम - नियमित व्यायाम', icon: '💪' },
          { value: 'advanced', label: 'Advanced - Very active', labelHindi: 'उन्नत - बहुत सक्रिय', icon: '🏃‍♂️' },
        ],
        required: true,
        order: 6,
        validation: {
          required: true,
          errorMessage: 'Please select your fitness level',
          errorMessageHindi: 'कृपया अपना फिटनेस स्तर चुनें',
        },
      },

      // Available Time Assessment
      {
        id: 'available_time',
        type: 'rating_scale',
        category: 'preferences',
        title: 'How many minutes per day can you dedicate to posture exercises?',
        titleHindi: 'आप दिन में कितने मिनट आसन व्यायाम के लिए समर्पित कर सकते हैं?',
        description: 'Be realistic about your schedule for sustainable habits',
        descriptionHindi: 'स्थायी आदतों के लिए अपने कार्यक्रम के बारे में यथार्थवादी बनें',
        scaleMin: 5,
        scaleMax: 60,
        scaleStep: 5,
        scaleLabels: {
          5: '5 min',
          15: '15 min',
          30: '30 min',
          60: '60+ min',
        },
        scaleLabelsHindi: {
          5: '5 मिनट',
          15: '15 मिनट',
          30: '30 मिनट',
          60: '60+ मिनट',
        },
        required: true,
        order: 7,
        validation: {
          required: true,
          min: 5,
          max: 60,
          errorMessage: 'Please select your available time',
          errorMessageHindi: 'कृपया अपना उपलब्ध समय चुनें',
        },
      },

      // Pain Frequency Assessment
      {
        id: 'neck_pain_frequency',
        type: 'rating_scale',
        category: 'health_assessment',
        title: 'How often do you experience neck pain or stiffness?',
        titleHindi: 'आप कितनी बार गर्दन में दर्द या अकड़न महसूस करते हैं?',
        description: 'Rate from 1 (never) to 5 (daily)',
        descriptionHindi: '1 (कभी नहीं) से 5 (दैनिक) तक रेट करें',
        scaleMin: 1,
        scaleMax: 5,
        scaleStep: 1,
        scaleLabels: {
          1: 'Never',
          2: 'Rarely',
          3: 'Sometimes',
          4: 'Often',
          5: 'Daily',
        },
        scaleLabelsHindi: {
          1: 'कभी नहीं',
          2: 'शायद ही कभी',
          3: 'कभी कभी',
          4: 'अक्सर',
          5: 'दैनिक',
        },
        required: true,
        order: 8,
        validation: {
          required: true,
          min: 1,
          max: 5,
          errorMessage: 'Please rate your neck pain frequency',
          errorMessageHindi: 'कृपया अपनी गर्दन के दर्द की आवृत्ति को रेट करें',
        },
      },

      // Exercise Preferences
      {
        id: 'exercise_preferences',
        type: 'multiple_choice',
        category: 'preferences',
        title: 'What types of exercises do you prefer?',
        titleHindi: 'आप किस प्रकार के व्यायाम पसंद करते हैं?',
        description: 'Select all that apply - we\'ll personalize your routine',
        descriptionHindi: 'सभी लागू का चयन करें - हम आपकी दिनचर्या को व्यक्तिगत बनाएंगे',
        options: [
          { value: 'yoga', label: 'Yoga & Stretching', labelHindi: 'योग और स्ट्रेचिंग', icon: '🧘‍♀️' },
          { value: 'strength', label: 'Strength Training', labelHindi: 'शक्ति प्रशिक्षण', icon: '💪' },
          { value: 'cardio', label: 'Cardio & Walking', labelHindi: 'कार्डियो और चलना', icon: '🏃‍♂️' },
          { value: 'pilates', label: 'Pilates', labelHindi: 'पिलेट्स', icon: '🤸‍♀️' },
          { value: 'breathing', label: 'Breathing Exercises', labelHindi: 'श्वास व्यायाम', icon: '🫁' },
          { value: 'meditation', label: 'Meditation & Mindfulness', labelHindi: 'ध्यान और माइंडफुलनेस', icon: '🧠' },
        ],
        required: true,
        order: 9,
        validation: {
          required: true,
          minSelections: 1,
          maxSelections: 4,
          errorMessage: 'Please select at least one exercise type',
          errorMessageHindi: 'कृपया कम से कम एक व्यायाम प्रकार चुनें',
        },
      },

      // Stress Level Assessment
      {
        id: 'stress_level',
        type: 'rating_scale',
        category: 'lifestyle',
        title: 'How would you rate your current stress level?',
        titleHindi: 'आप अपने वर्तमान तनाव स्तर को कैसे रेट करेंगे?',
        description: 'Stress can significantly impact muscle tension and posture',
        descriptionHindi: 'तनाव मांसपेशियों के तनाव और मुद्रा को महत्वपूर्ण रूप से प्रभावित कर सकता है',
        scaleMin: 1,
        scaleMax: 5,
        scaleStep: 1,
        scaleLabels: {
          1: 'Very Low',
          2: 'Low',
          3: 'Moderate',
          4: 'High',
          5: 'Very High',
        },
        scaleLabelsHindi: {
          1: 'बहुत कम',
          2: 'कम',
          3: 'मध्यम',
          4: 'उच्च',
          5: 'बहुत उच्च',
        },
        required: true,
        order: 10,
        validation: {
          required: true,
          min: 1,
          max: 5,
          errorMessage: 'Please rate your stress level',
          errorMessageHindi: 'कृपया अपने तनाव स्तर को रेट करें',
        },
      },

      // Sleep Quality Assessment
      {
        id: 'sleep_quality',
        type: 'rating_scale',
        category: 'lifestyle',
        title: 'How would you rate your sleep quality?',
        titleHindi: 'आप अपनी नींद की गुणवत्ता को कैसे रेट करेंगे?',
        description: 'Poor sleep affects muscle recovery and posture',
        descriptionHindi: 'खराब नींद मांसपेशियों की रिकवरी और मुद्रा को प्रभावित करती है',
        scaleMin: 1,
        scaleMax: 5,
        scaleStep: 1,
        scaleLabels: {
          1: 'Very Poor',
          2: 'Poor',
          3: 'Average',
          4: 'Good',
          5: 'Excellent',
        },
        scaleLabelsHindi: {
          1: 'बहुत खराब',
          2: 'खराब',
          3: 'औसत',
          4: 'अच्छा',
          5: 'उत्कृष्ट',
        },
        required: true,
        order: 11,
        validation: {
          required: true,
          min: 1,
          max: 5,
          errorMessage: 'Please rate your sleep quality',
          errorMessageHindi: 'कृपया अपनी नींद की गुणवत्ता को रेट करें',
        },
      },

      // Exercise Frequency
      {
        id: 'exercise_frequency',
        type: 'single_choice',
        category: 'fitness_assessment',
        title: 'How often do you currently exercise?',
        titleHindi: 'आप वर्तमान में कितनी बार व्यायाम करते हैं?',
        description: 'Regular exercise helps maintain good posture',
        descriptionHindi: 'नियमित व्यायाम अच्छी मुद्रा बनाए रखने में मदद करता है',
        options: [
          { value: 'daily', label: 'Daily (7 days/week)', labelHindi: 'दैनिक (7 दिन/सप्ताह)', icon: '🏃‍♂️' },
          { value: 'frequent', label: 'Frequently (4-6 days/week)', labelHindi: 'अक्सर (4-6 दिन/सप्ताह)', icon: '💪' },
          { value: 'moderate', label: 'Moderately (2-3 days/week)', labelHindi: 'मध्यम (2-3 दिन/सप्ताह)', icon: '🚶‍♂️' },
          { value: 'occasional', label: 'Occasionally (1 day/week)', labelHindi: 'कभी-कभार (1 दिन/सप्ताह)', icon: '🧘‍♀️' },
          { value: 'rarely', label: 'Rarely (few times/month)', labelHindi: 'शायद ही कभी (महीने में कुछ बार)', icon: '😴' },
          { value: 'never', label: 'Never', labelHindi: 'कभी नहीं', icon: '🛋️' },
        ],
        required: true,
        order: 12,
        validation: {
          required: true,
          errorMessage: 'Please select your exercise frequency',
          errorMessageHindi: 'कृपया अपनी व्यायाम आवृत्ति चुनें',
        },
      },

      // Device Usage Pattern
      {
        id: 'device_usage_pattern',
        type: 'multiple_choice',
        category: 'digital_wellness',
        title: 'Which devices do you use most frequently?',
        titleHindi: 'आप किन उपकरणों का सबसे अधिक उपयोग करते हैं?',
        description: 'Different devices affect posture in different ways',
        descriptionHindi: 'विभिन्न उपकरण अलग-अलग तरीकों से मुद्रा को प्रभावित करते हैं',
        options: [
          { value: 'desktop', label: 'Desktop Computer', labelHindi: 'डेस्कटॉप कंप्यूटर', icon: '🖥️' },
          { value: 'laptop', label: 'Laptop', labelHindi: 'लैपटॉप', icon: '💻' },
          { value: 'tablet', label: 'Tablet', labelHindi: 'टैबलेट', icon: '📱' },
          { value: 'smartphone', label: 'Smartphone', labelHindi: 'स्मार्टफोन', icon: '📱' },
          { value: 'tv', label: 'Television', labelHindi: 'टेलीविजन', icon: '📺' },
        ],
        required: true,
        order: 13,
        validation: {
          required: true,
          minSelections: 1,
          maxSelections: 5,
          errorMessage: 'Please select at least one device',
          errorMessageHindi: 'कृपया कम से कम एक उपकरण चुनें',
        },
      },

      // Eye Strain Assessment
      {
        id: 'eye_strain_frequency',
        type: 'rating_scale',
        category: 'digital_wellness',
        title: 'How often do you experience eye strain or fatigue?',
        titleHindi: 'आप कितनी बार आंखों में तनाव या थकान महसूस करते हैं?',
        description: 'Eye strain often indicates poor screen ergonomics',
        descriptionHindi: 'आंखों का तनाव अक्सर खराब स्क्रीन एर्गोनॉमिक्स को दर्शाता है',
        scaleMin: 1,
        scaleMax: 5,
        scaleStep: 1,
        scaleLabels: {
          1: 'Never',
          2: 'Rarely',
          3: 'Sometimes',
          4: 'Often',
          5: 'Daily',
        },
        scaleLabelsHindi: {
          1: 'कभी नहीं',
          2: 'शायद ही कभी',
          3: 'कभी कभी',
          4: 'अक्सर',
          5: 'दैनिक',
        },
        required: true,
        order: 14,
        validation: {
          required: true,
          min: 1,
          max: 5,
          errorMessage: 'Please rate your eye strain frequency',
          errorMessageHindi: 'कृपया अपनी आंखों के तनाव की आवृत्ति को रेट करें',
        },
      },

      // Goals and Motivation
      {
        id: 'primary_goals',
        type: 'multiple_choice',
        category: 'goals',
        title: 'What are your primary health and wellness goals?',
        titleHindi: 'आपके मुख्य स्वास्थ्य और कल्याण लक्ष्य क्या हैं?',
        description: 'Select up to 3 goals that matter most to you',
        descriptionHindi: 'उन 3 लक्ष्यों का चयन करें जो आपके लिए सबसे महत्वपूर्ण हैं',
        options: [
          { value: 'pain_relief', label: 'Reduce pain and discomfort', labelHindi: 'दर्द और असुविधा कम करें', icon: '🩹' },
          { value: 'posture_improvement', label: 'Improve posture', labelHindi: 'मुद्रा में सुधार', icon: '🧍‍♂️' },
          { value: 'energy_boost', label: 'Increase energy levels', labelHindi: 'ऊर्जा का स्तर बढ़ाएं', icon: '⚡' },
          { value: 'stress_reduction', label: 'Reduce stress and tension', labelHindi: 'तनाव और चिंता कम करें', icon: '😌' },
          { value: 'sleep_improvement', label: 'Better sleep quality', labelHindi: 'बेहतर नींद की गुणवत्ता', icon: '😴' },
          { value: 'productivity', label: 'Enhance work productivity', labelHindi: 'कार्य उत्पादकता बढ़ाएं', icon: '📈' },
          { value: 'flexibility', label: 'Improve flexibility', labelHindi: 'लचीलेपन में सुधार', icon: '🤸‍♀️' },
          { value: 'strength', label: 'Build core strength', labelHindi: 'मुख्य शक्ति का निर्माण', icon: '💪' },
        ],
        required: true,
        order: 15,
        validation: {
          required: true,
          minSelections: 1,
          maxSelections: 3,
          errorMessage: 'Please select 1-3 primary goals',
          errorMessageHindi: 'कृपया 1-3 मुख्य लक्ष्य चुनें',
        },
      },

      // Preferred Exercise Time
      {
        id: 'preferred_exercise_time',
        type: 'single_choice',
        category: 'preferences',
        title: 'When do you prefer to exercise or do wellness activities?',
        titleHindi: 'आप कब व्यायाम या कल्याण गतिविधियां करना पसंद करते हैं?',
        description: 'We\'ll schedule reminders at your preferred time',
        descriptionHindi: 'हम आपके पसंदीदा समय पर रिमाइंडर शेड्यूल करेंगे',
        options: [
          { value: 'early_morning', label: 'Early Morning (5-7 AM)', labelHindi: 'सुबह जल्दी (5-7 बजे)', icon: '🌅' },
          { value: 'morning', label: 'Morning (7-10 AM)', labelHindi: 'सुबह (7-10 बजे)', icon: '☀️' },
          { value: 'midday', label: 'Midday (10 AM-2 PM)', labelHindi: 'दोपहर (10 बजे-2 बजे)', icon: '🌞' },
          { value: 'afternoon', label: 'Afternoon (2-6 PM)', labelHindi: 'दोपहर (2-6 बजे)', icon: '🌤️' },
          { value: 'evening', label: 'Evening (6-9 PM)', labelHindi: 'शाम (6-9 बजे)', icon: '🌆' },
          { value: 'night', label: 'Night (9 PM+)', labelHindi: 'रात (9 बजे+)', icon: '🌙' },
          { value: 'flexible', label: 'Flexible/No preference', labelHindi: 'लचीला/कोई प्राथमिकता नहीं', icon: '🔄' },
        ],
        required: true,
        order: 16,
        validation: {
          required: true,
          errorMessage: 'Please select your preferred exercise time',
          errorMessageHindi: 'कृपया अपना पसंदीदा व्यायाम समय चुनें',
        },
      },
    ];
  }

  /**
   * Start a new questionnaire session with enhanced tracking
   */
  async startSession(
    userId: string,
    type: 'onboarding' | 'periodic_update' | 'goal_reassessment' | 'health_check' = 'onboarding'
  ): Promise<QuestionnaireSession> {
    try {
      // Verify user is authenticated with robust retry logic
      const authenticatedUser = await AuthUtils.getCurrentUser(3);

      if (!authenticatedUser) {
        throw new Error('User not authenticated');
      }

      if (authenticatedUser.id !== userId) {
        throw new Error('User ID mismatch - authentication required');
      }

      // Use database function to create session and get auto-generated ID
      let sessionId: string;
      try {
        const { data, error } = await supabase.rpc('create_questionnaire_session', {
          session_type: type
        });

        if (error) {
          logger.error('Failed to create enhanced questionnaire session in database', error, 'EnhancedQuestionnaireService');
          throw new Error('Failed to create session');
        }

        sessionId = data;
        logger.info('Enhanced questionnaire session created in database', { sessionId }, 'EnhancedQuestionnaireService');
      } catch (dbError) {
        logger.error('Enhanced questionnaire database function call failed', dbError, 'EnhancedQuestionnaireService');
        throw new Error('Failed to create session');
      }

      const session: QuestionnaireSession = {
        id: sessionId,
        userId: authenticatedUser.id, // Use the authenticated user ID
        type,
        startedAt: new Date(),
        responses: [],
        completionPercentage: 0,
        insights: [],
        recommendations: [],
        totalQuestions: this.questionBank.length,
        currentQuestionIndex: 0,
        estimatedTimeRemaining: this.calculateEstimatedTime(),
      };

      this.currentSession = session;

      // Cache session for offline support
      await cacheManager.set(`questionnaire_session_${authenticatedUser.id}`, session, 24 * 60 * 60);

      logger.info('Enhanced questionnaire session started', {
        sessionId: session.id,
        userId: authenticatedUser.id,
        type,
        totalQuestions: session.totalQuestions,
      }, 'EnhancedQuestionnaireService');

      return session;
    } catch (error) {
      logger.error('Failed to start enhanced questionnaire session', error, 'EnhancedQuestionnaireService');
      throw error;
    }
  }

  /**
   * Calculate estimated time to complete questionnaire
   */
  private calculateEstimatedTime(): number {
    // Average 30 seconds per question
    return this.questionBank.length * 30;
  }

  /**
   * Get next question with intelligent branching
   */
  getNextQuestion(responses: QuestionnaireResponse[]): QuestionnaireQuestion | null {
    const answeredQuestionIds = responses.map(r => r.questionId);
    
    // Apply conditional logic and branching
    const availableQuestions = this.questionBank.filter(question => {
      if (answeredQuestionIds.includes(question.id)) {
        return false;
      }

      // Check conditional logic
      if (question.conditional) {
        const dependentResponse = responses.find(r => r.questionId === question.conditional!.dependsOn);
        if (!dependentResponse) {
          return false;
        }

        return this.evaluateCondition(dependentResponse, question.conditional);
      }

      return true;
    });

    // Return next question by order
    return availableQuestions.sort((a, b) => a.order - b.order)[0] || null;
  }

  /**
   * Evaluate conditional logic for question branching
   */
  private evaluateCondition(
    response: QuestionnaireResponse,
    conditional: { dependsOn: string; condition: string; value: any }
  ): boolean {
    const { condition, value } = conditional;
    const responseValue = response.answerValue;

    switch (condition) {
      case 'equals':
        return responseValue === value;
      case 'not_equals':
        return responseValue !== value;
      case 'contains':
        return Array.isArray(responseValue) && responseValue.includes(value);
      case 'not_contains':
        return Array.isArray(responseValue) && !responseValue.includes(value);
      case 'greater_than':
        return Number(responseValue) > Number(value);
      case 'less_than':
        return Number(responseValue) < Number(value);
      default:
        return true;
    }
  }

  /**
   * Submit response with real-time validation and processing
   */
  async submitResponse(
    sessionId: string,
    questionId: string,
    answerValue: any,
    confidenceLevel: number = 5,
    responseTimeMs: number = 0
  ): Promise<QuestionnaireSession> {
    try {
      if (!this.currentSession || this.currentSession.id !== sessionId) {
        throw new Error('Invalid session');
      }

      // Validate response
      const question = this.questionBank.find(q => q.id === questionId);
      if (!question) {
        throw new Error('Question not found');
      }

      const validationResult = this.validateResponse(question, answerValue);
      if (!validationResult.isValid) {
        throw new Error(validationResult.errorMessage);
      }

      // Create response object
      const response: QuestionnaireResponse = {
        id: '', // Will be set by database after insert
        sessionId,
        userId: this.currentSession.userId,
        questionId,
        questionCategory: question.category,
        questionText: question.title,
        answerValue,
        answerData: {
          confidenceLevel,
          responseTimeMs,
          questionType: question.type,
        },
        confidenceLevel,
        responseTimeMs,
        createdAt: new Date(),
      };

      // Save response to database and get the generated ID
      const generatedId = await dataService.saveQuestionnaireResponse(response);

      // Update response with generated ID
      response.id = generatedId;

      // Add response to session
      this.currentSession.responses.push(response);
      this.currentSession.currentQuestionIndex = this.currentSession.responses.length;
      this.currentSession.completionPercentage = Math.round(
        (this.currentSession.responses.length / this.currentSession.totalQuestions) * 100
      );
      this.currentSession.estimatedTimeRemaining = this.calculateRemainingTime();
      
      // Update session in database
      await dataService.updateQuestionnaireSession(this.currentSession);

      // Generate real-time insights if session is complete
      if (this.currentSession.completionPercentage >= 100) {
        await this.completeSession();
      }

      logger.info('Questionnaire response submitted', {
        sessionId,
        questionId,
        completionPercentage: this.currentSession.completionPercentage,
      }, 'EnhancedQuestionnaireService');

      return this.currentSession;
    } catch (error) {
      logger.error('Failed to submit questionnaire response', error, 'EnhancedQuestionnaireService');
      throw error;
    }
  }

  /**
   * Calculate remaining time based on unanswered questions
   */
  private calculateRemainingTime(): number {
    if (!this.currentSession) return 0;
    
    const remainingQuestions = this.currentSession.totalQuestions - this.currentSession.responses.length;
    return remainingQuestions * 30; // 30 seconds per question
  }

  /**
   * Validate response based on question requirements
   */
  private validateResponse(question: QuestionnaireQuestion, answerValue: any): {
    isValid: boolean;
    errorMessage?: string;
  } {
    const validation = question.validation;
    if (!validation) {
      return { isValid: true };
    }

    // Required validation
    if (validation.required && (answerValue === null || answerValue === undefined || answerValue === '')) {
      return {
        isValid: false,
        errorMessage: validation.errorMessage || 'This field is required',
      };
    }

    // Scale validation
    if (question.type === 'rating_scale') {
      const numValue = Number(answerValue);
      if (validation.min !== undefined && numValue < validation.min) {
        return {
          isValid: false,
          errorMessage: `Value must be at least ${validation.min}`,
        };
      }
      if (validation.max !== undefined && numValue > validation.max) {
        return {
          isValid: false,
          errorMessage: `Value must be at most ${validation.max}`,
        };
      }
    }

    // Multiple choice validation
    if (question.type === 'multiple_choice' && Array.isArray(answerValue)) {
      if (validation.minSelections && answerValue.length < validation.minSelections) {
        return {
          isValid: false,
          errorMessage: `Please select at least ${validation.minSelections} option(s)`,
        };
      }
      if (validation.maxSelections && answerValue.length > validation.maxSelections) {
        return {
          isValid: false,
          errorMessage: `Please select at most ${validation.maxSelections} option(s)`,
        };
      }
    }

    return { isValid: true };
  }

  /**
   * Complete session and generate insights/recommendations
   */
  private async completeSession(): Promise<void> {
    if (!this.currentSession) return;

    try {
      // Mark session as completed
      this.currentSession.completedAt = new Date();
      this.currentSession.completionPercentage = 100;

      // Generate insights using recommendation engine
      const insights = await recommendationEngine.generateInsights(
        this.currentSession.userId,
        this.currentSession.responses
      );

      // Get user preferences for recommendation generation
      const preferences = await dataService.getUserPreferences(this.currentSession.userId);
      
      // Generate personalized recommendations
      const recommendations = await recommendationEngine.generateRecommendations(
        this.currentSession.userId,
        insights,
        preferences
      );

      // Update session with results
      this.currentSession.insights = insights;
      this.currentSession.recommendations = recommendations;

      // Save completed session
      await dataService.updateQuestionnaireSession(this.currentSession);

      // Mark user as having completed questionnaire
      await dataService.updateUserProfile(this.currentSession.userId, {
        questionnaireCompleted: true,
        lastQuestionnaireAt: new Date(),
      });

      logger.info('Questionnaire session completed', {
        sessionId: this.currentSession.id,
        userId: this.currentSession.userId,
        insightCount: insights.length,
        recommendationCount: recommendations.length,
      }, 'EnhancedQuestionnaireService');

    } catch (error) {
      logger.error('Failed to complete questionnaire session', error, 'EnhancedQuestionnaireService');
      throw error;
    }
  }
}

export const enhancedQuestionnaireService = EnhancedQuestionnaireService.getInstance();
