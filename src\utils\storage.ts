/**
 * Storage utilities using Expo's storage solutions
 * - SecureStore for sensitive data (tokens, credentials)
 * - AsyncStorage for general app data (settings, cache, logs)
 */

import * as SecureStore from 'expo-secure-store';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { logger } from './logger';

// Keys for secure storage (sensitive data)
export const SECURE_KEYS = {
  AUTH_TOKEN: 'auth_token',
  REFRESH_TOKEN: 'refresh_token',
  USER_CREDENTIALS: 'user_credentials',
  PUSH_TOKEN: 'push_token',
} as const;

// Keys for regular storage (non-sensitive data)
export const STORAGE_KEYS = {
  USER_LANGUAGE: 'user_language',
  NOTIFICATION_SETTINGS: 'notification_settings',
  APP_LOGS: 'app_logs',
  CACHE_METADATA: 'cache_metadata',
  SYNC_QUEUE: 'sync_queue',
  CACHED_DATA: 'cached_data',
  LAST_SYNC: 'last_sync',
  ONBOARDING_COMPLETE: 'onboarding_complete',
} as const;

/**
 * Secure Storage - for sensitive data like tokens
 */
export class SecureStorage {
  static async setItem(key: string, value: string): Promise<void> {
    try {
      await SecureStore.setItemAsync(key, value);
    } catch (error) {
      logger.error(`Failed to store secure item: ${key}`, error, 'SecureStorage');
      throw error;
    }
  }

  static async getItem(key: string): Promise<string | null> {
    try {
      return await SecureStore.getItemAsync(key);
    } catch (error) {
      logger.error(`Failed to get secure item: ${key}`, error, 'SecureStorage');
      return null;
    }
  }

  static async removeItem(key: string): Promise<void> {
    try {
      await SecureStore.deleteItemAsync(key);
    } catch (error) {
      logger.error(`Failed to remove secure item: ${key}`, error, 'SecureStorage');
      throw error;
    }
  }

  static async setObject<T>(key: string, value: T): Promise<void> {
    try {
      const jsonValue = JSON.stringify(value);
      await SecureStore.setItemAsync(key, jsonValue);
    } catch (error) {
      logger.error(`Failed to store secure object: ${key}`, error, 'SecureStorage');
      throw error;
    }
  }

  static async getObject<T>(key: string): Promise<T | null> {
    try {
      const jsonValue = await SecureStore.getItemAsync(key);
      return jsonValue ? JSON.parse(jsonValue) : null;
    } catch (error) {
      logger.error(`Failed to get secure object: ${key}`, error, 'SecureStorage');
      return null;
    }
  }
}

/**
 * Regular Storage - for non-sensitive app data
 */
export class AppStorage {
  static async setItem(key: string, value: string): Promise<void> {
    try {
      await AsyncStorage.setItem(key, value);
    } catch (error) {
      logger.error(`Failed to store item: ${key}`, error, 'AppStorage');
      throw error;
    }
  }

  static async getItem(key: string): Promise<string | null> {
    try {
      return await AsyncStorage.getItem(key);
    } catch (error) {
      logger.error(`Failed to get item: ${key}`, error, 'AppStorage');
      return null;
    }
  }

  static async removeItem(key: string): Promise<void> {
    try {
      await AsyncStorage.removeItem(key);
    } catch (error) {
      logger.error(`Failed to remove item: ${key}`, error, 'AppStorage');
      throw error;
    }
  }

  static async setObject<T>(key: string, value: T): Promise<void> {
    try {
      const jsonValue = JSON.stringify(value);
      await AsyncStorage.setItem(key, jsonValue);
    } catch (error) {
      logger.error(`Failed to store object: ${key}`, error, 'AppStorage');
      throw error;
    }
  }

  static async getObject<T>(key: string): Promise<T | null> {
    try {
      const jsonValue = await AsyncStorage.getItem(key);
      return jsonValue ? JSON.parse(jsonValue) : null;
    } catch (error) {
      logger.error(`Failed to get object: ${key}`, error, 'AppStorage');
      return null;
    }
  }

  static async getAllKeys(): Promise<string[]> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      return [...keys]; // Convert readonly array to mutable array
    } catch (error) {
      logger.error('Failed to get all keys', error, 'AppStorage');
      return [];
    }
  }

  static async multiRemove(keys: string[]): Promise<void> {
    try {
      await AsyncStorage.multiRemove(keys);
    } catch (error) {
      logger.error('Failed to remove multiple items', error, 'AppStorage');
      throw error;
    }
  }

  static async clear(): Promise<void> {
    try {
      await AsyncStorage.clear();
    } catch (error) {
      logger.error('Failed to clear storage', error, 'AppStorage');
      throw error;
    }
  }
}