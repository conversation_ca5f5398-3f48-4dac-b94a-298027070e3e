import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  Animated,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Svg, { Path, Circle, G, Defs, LinearGradient, Stop } from 'react-native-svg';
import { Theme } from '../../constants/designTokens';

const { width: screenWidth } = Dimensions.get('window');

interface BodyArea {
  id: string;
  label: string;
  labelHindi: string;
  coordinates: { x: number; y: number };
  layer: 'skeletal' | 'muscular' | 'nervous' | 'surface';
}

interface AnatomicalLayer {
  id: string;
  name: string;
  nameHindi: string;
  icon: string;
  color: string;
  opacity: number;
}

interface AnatomicalBodyModelProps {
  bodyAreas: BodyArea[];
  selectedAreas: string[];
  onSelectionChange: (selectedAreas: string[]) => void;
  language: string;
  showLayerControls?: boolean;
  defaultView?: 'anterior' | 'posterior' | 'lateral';
}

const anatomicalLayers: AnatomicalLayer[] = [
  {
    id: 'surface',
    name: 'Surface',
    nameHindi: 'सतह',
    icon: '👤',
    color: '#E5E7EB',
    opacity: 0.8,
  },
  {
    id: 'muscular',
    name: 'Muscular',
    nameHindi: 'मांसपेशी',
    icon: '💪',
    color: '#DC2626',
    opacity: 0.7,
  },
  {
    id: 'skeletal',
    name: 'Skeletal',
    nameHindi: 'कंकाल',
    icon: '🦴',
    color: '#F3F4F6',
    opacity: 0.9,
  },
  {
    id: 'nervous',
    name: 'Nervous',
    nameHindi: 'तंत्रिका',
    icon: '⚡',
    color: '#3B82F6',
    opacity: 0.6,
  },
];

const AnatomicalBodyModel: React.FC<AnatomicalBodyModelProps> = ({
  bodyAreas,
  selectedAreas,
  onSelectionChange,
  language,
  showLayerControls = true,
  defaultView = 'anterior',
}) => {
  const [activeView, setActiveView] = useState<'anterior' | 'posterior' | 'lateral'>(defaultView);
  const [visibleLayers, setVisibleLayers] = useState<string[]>(['surface', 'muscular']);
  const [animatedValues] = useState(
    bodyAreas.reduce((acc, area) => {
      acc[area.id] = new Animated.Value(0);
      return acc;
    }, {} as { [key: string]: Animated.Value })
  );

  const handleAreaPress = (areaId: string) => {
    const isSelected = selectedAreas.includes(areaId);
    let newSelection: string[];

    if (isSelected) {
      newSelection = selectedAreas.filter(id => id !== areaId);
      // Animate out
      Animated.timing(animatedValues[areaId], {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } else {
      newSelection = [...selectedAreas, areaId];
      // Animate in
      Animated.timing(animatedValues[areaId], {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }

    onSelectionChange(newSelection);
  };

  const toggleLayer = (layerId: string) => {
    setVisibleLayers(prev => 
      prev.includes(layerId) 
        ? prev.filter(id => id !== layerId)
        : [...prev, layerId]
    );
  };

  const renderAnatomicalBody = () => {
    const svgWidth = screenWidth * 0.7;
    const svgHeight = svgWidth * 1.4;

    return (
      <View style={styles.bodyContainer}>
        <Svg width={svgWidth} height={svgHeight} viewBox="0 0 120 168">
          <Defs>
            {/* Gradients for different layers */}
            <LinearGradient id="skeletalGrad" x1="0%" y1="0%" x2="100%" y2="100%">
              <Stop offset="0%" stopColor="#F9FAFB" stopOpacity="1" />
              <Stop offset="100%" stopColor="#E5E7EB" stopOpacity="1" />
            </LinearGradient>
            <LinearGradient id="muscularGrad" x1="0%" y1="0%" x2="100%" y2="100%">
              <Stop offset="0%" stopColor="#FCA5A5" stopOpacity="0.8" />
              <Stop offset="100%" stopColor="#DC2626" stopOpacity="0.8" />
            </LinearGradient>
            <LinearGradient id="nervousGrad" x1="0%" y1="0%" x2="100%" y2="100%">
              <Stop offset="0%" stopColor="#93C5FD" stopOpacity="0.6" />
              <Stop offset="100%" stopColor="#3B82F6" stopOpacity="0.6" />
            </LinearGradient>
          </Defs>

          {/* Render layers based on visibility */}
          {visibleLayers.includes('skeletal') && (
            <G opacity={anatomicalLayers.find(l => l.id === 'skeletal')?.opacity}>
              {/* Skeletal system - simplified spine and major bones */}
              <Path
                d="M60 20 L60 140"
                stroke="url(#skeletalGrad)"
                strokeWidth="4"
                fill="none"
              />
              {/* Skull */}
              <Circle cx="60" cy="15" r="12" fill="url(#skeletalGrad)" />
              {/* Ribcage */}
              <Path
                d="M45 35 Q60 30 75 35 L75 65 Q60 70 45 65 Z"
                fill="url(#skeletalGrad)"
                opacity="0.7"
              />
              {/* Pelvis */}
              <Path
                d="M45 85 Q60 80 75 85 L75 95 Q60 100 45 95 Z"
                fill="url(#skeletalGrad)"
                opacity="0.7"
              />
            </G>
          )}

          {visibleLayers.includes('muscular') && (
            <G opacity={anatomicalLayers.find(l => l.id === 'muscular')?.opacity}>
              {/* Muscular system - major muscle groups */}
              <Path
                d="M50 25 Q60 20 70 25 L70 45 Q60 50 50 45 Z"
                fill="url(#muscularGrad)"
              />
              {/* Torso muscles */}
              <Path
                d="M40 40 Q60 35 80 40 L80 90 Q60 95 40 90 Z"
                fill="url(#muscularGrad)"
              />
            </G>
          )}

          {visibleLayers.includes('nervous') && (
            <G opacity={anatomicalLayers.find(l => l.id === 'nervous')?.opacity}>
              {/* Nervous system - spinal cord and major nerves */}
              <Path
                d="M60 20 L60 140"
                stroke="url(#nervousGrad)"
                strokeWidth="2"
                fill="none"
              />
              {/* Neural pathways */}
              <Path
                d="M60 30 L45 40 M60 30 L75 40 M60 50 L45 60 M60 50 L75 60"
                stroke="url(#nervousGrad)"
                strokeWidth="1.5"
                fill="none"
              />
            </G>
          )}

          {/* Interactive body areas */}
          {bodyAreas
            .filter(area => visibleLayers.includes(area.layer))
            .map((area) => {
              const isSelected = selectedAreas.includes(area.id);
              return (
                <Circle
                  key={area.id}
                  cx={area.coordinates.x}
                  cy={area.coordinates.y}
                  r="10"
                  fill={isSelected ? Theme.colors.primary[500] : 'rgba(59, 130, 246, 0.3)'}
                  stroke={isSelected ? Theme.colors.primary[600] : Theme.colors.primary[400]}
                  strokeWidth="2"
                  onPress={() => handleAreaPress(area.id)}
                />
              );
            })}
        </Svg>
      </View>
    );
  };

  const renderViewControls = () => (
    <View style={styles.viewControls}>
      {(['anterior', 'posterior', 'lateral'] as const).map((view) => (
        <TouchableOpacity
          key={view}
          style={[
            styles.viewButton,
            activeView === view && styles.activeViewButton,
          ]}
          onPress={() => setActiveView(view)}
        >
          <Text style={[
            styles.viewButtonText,
            activeView === view && styles.activeViewButtonText,
          ]}>
            {view === 'anterior' ? (language === 'hi' ? 'सामने' : 'Front') :
             view === 'posterior' ? (language === 'hi' ? 'पीछे' : 'Back') :
             (language === 'hi' ? 'बगल' : 'Side')}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderLayerControls = () => (
    <View style={styles.layerControls}>
      <Text style={styles.layerTitle}>
        {language === 'hi' ? 'शारीरिक परतें:' : 'Anatomical Layers:'}
      </Text>
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        {anatomicalLayers.map((layer) => (
          <TouchableOpacity
            key={layer.id}
            style={[
              styles.layerButton,
              visibleLayers.includes(layer.id) && styles.activeLayerButton,
            ]}
            onPress={() => toggleLayer(layer.id)}
          >
            <Text style={styles.layerIcon}>{layer.icon}</Text>
            <Text style={[
              styles.layerButtonText,
              visibleLayers.includes(layer.id) && styles.activeLayerButtonText,
            ]}>
              {language === 'hi' ? layer.nameHindi : layer.name}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  return (
    <View style={styles.container}>
      <Text style={styles.instructionText}>
        {language === 'hi' 
          ? 'अपनी मुद्रा संबंधी चिंताओं के क्षेत्रों का चयन करें'
          : 'Select areas of your posture concerns'
        }
      </Text>
      
      {renderViewControls()}
      
      {showLayerControls && renderLayerControls()}
      
      {renderAnatomicalBody()}
      
      {/* Selected areas display */}
      {selectedAreas.length > 0 && (
        <View style={styles.selectedAreasContainer}>
          <Text style={styles.selectedAreasTitle}>
            {language === 'hi' ? 'चयनित क्षेत्र:' : 'Selected Areas:'}
          </Text>
          <View style={styles.selectedAreasList}>
            {selectedAreas.map((areaId) => {
              const area = bodyAreas.find(a => a.id === areaId);
              if (!area) return null;

              return (
                <View key={areaId} style={styles.selectedAreaChip}>
                  <Text style={styles.selectedAreaText}>
                    {language === 'hi' ? area.labelHindi : area.label}
                  </Text>
                  <TouchableOpacity
                    onPress={() => handleAreaPress(areaId)}
                    style={styles.removeAreaButton}
                  >
                    <Ionicons name="close" size={14} color={Theme.colors.neutral[600]} />
                  </TouchableOpacity>
                </View>
              );
            })}
          </View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingVertical: 20,
  },
  instructionText: {
    fontSize: 16,
    color: Theme.colors.text.secondary,
    textAlign: 'center',
    marginBottom: 20,
    paddingHorizontal: 20,
    lineHeight: 24,
  },
  viewControls: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 16,
    paddingHorizontal: 20,
  },
  viewButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginHorizontal: 4,
    borderRadius: 20,
    backgroundColor: Theme.colors.neutral[100],
    borderWidth: 1,
    borderColor: Theme.colors.neutral[200],
  },
  activeViewButton: {
    backgroundColor: Theme.colors.primary[500],
    borderColor: Theme.colors.primary[600],
  },
  viewButtonText: {
    fontSize: 14,
    color: Theme.colors.text.secondary,
    fontWeight: '500',
  },
  activeViewButtonText: {
    color: '#FFFFFF',
  },
  layerControls: {
    marginBottom: 20,
    paddingHorizontal: 20,
  },
  layerTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Theme.colors.text.primary,
    marginBottom: 12,
  },
  layerButton: {
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginRight: 12,
    borderRadius: 12,
    backgroundColor: Theme.colors.neutral[100],
    borderWidth: 1,
    borderColor: Theme.colors.neutral[200],
    minWidth: 80,
  },
  activeLayerButton: {
    backgroundColor: Theme.colors.primary[100],
    borderColor: Theme.colors.primary[300],
  },
  layerIcon: {
    fontSize: 20,
    marginBottom: 4,
  },
  layerButtonText: {
    fontSize: 12,
    color: Theme.colors.text.secondary,
    fontWeight: '500',
    textAlign: 'center',
  },
  activeLayerButtonText: {
    color: Theme.colors.primary[700],
  },
  bodyContainer: {
    alignItems: 'center',
    backgroundColor: Theme.colors.neutral[50],
    borderRadius: 20,
    padding: 20,
    marginHorizontal: 20,
    marginBottom: 20,
  },
  selectedAreasContainer: {
    paddingHorizontal: 20,
  },
  selectedAreasTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Theme.colors.text.primary,
    marginBottom: 12,
  },
  selectedAreasList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  selectedAreaChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Theme.colors.primary[100],
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  selectedAreaText: {
    fontSize: 14,
    color: Theme.colors.primary[700],
    marginRight: 6,
  },
  removeAreaButton: {
    padding: 2,
  },
});

export default AnatomicalBodyModel;
