import { AppStorage, STORAGE_KEYS } from './storage';
import { logger } from './logger';

/**
 * Production-ready caching utility with multiple storage layers,
 * TTL support, and automatic cleanup for optimal performance
 */

export interface CacheEntry<T = any> {
  data: T;
  timestamp: number;
  ttl: number; // Time to live in milliseconds
  accessCount: number;
  lastAccessed: number;
}

export interface CacheConfig {
  maxMemoryEntries: number;
  maxStorageEntries: number;
  defaultTTL: number;
  cleanupInterval: number;
  compressionThreshold: number; // Bytes
  enableCompression: boolean;
  enablePersistence: boolean;
}

class CacheManager {
  private memoryCache: Map<string, CacheEntry> = new Map();
  private config: CacheConfig;
  private cleanupTimer?: NodeJS.Timeout;
  private storagePrefix = 'cache_';
  private metadataKey = STORAGE_KEYS.CACHE_METADATA;

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = {
      maxMemoryEntries: 100,
      maxStorageEntries: 500,
      defaultTTL: 5 * 60 * 1000, // 5 minutes
      cleanupInterval: 60 * 1000, // 1 minute
      compressionThreshold: 1024, // 1KB
      enableCompression: true,
      enablePersistence: true,
      ...config,
    };

    this.startCleanupTimer();
    this.loadMetadata();
  }

  private startCleanupTimer(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }

    this.cleanupTimer = setInterval(() => {
      this.cleanup();
    }, this.config.cleanupInterval);
  }

  private async loadMetadata(): Promise<void> {
    if (!this.config.enablePersistence) return;

    try {
      const parsedMetadata = await AppStorage.getObject<Record<string, CacheEntry>>(this.metadataKey);
      if (parsedMetadata) {
        // Load frequently accessed items into memory cache
        for (const [key, entry] of Object.entries(parsedMetadata)) {
          if ((entry as CacheEntry).accessCount > 5) {
            const data = await this.getFromStorage(key);
            if (data !== null) {
              this.memoryCache.set(key, entry as CacheEntry);
            }
          }
        }
      }
    } catch (error) {
      logger.warn('Failed to load cache metadata', error, 'CacheManager');
    }
  }

  private async saveMetadata(): Promise<void> {
    if (!this.config.enablePersistence) return;

    try {
      const metadata: { [key: string]: Omit<CacheEntry, 'data'> } = {};
      
      // Collect metadata from memory cache
      for (const [key, entry] of this.memoryCache.entries()) {
        metadata[key] = {
          timestamp: entry.timestamp,
          ttl: entry.ttl,
          accessCount: entry.accessCount,
          lastAccessed: entry.lastAccessed,
        };
      }

      // Collect metadata from storage
      const storageKeys = await AppStorage.getAllKeys();
      const cacheKeys = storageKeys.filter(key => key.startsWith(this.storagePrefix));
      
      for (const storageKey of cacheKeys) {
        const key = storageKey.replace(this.storagePrefix, '');
        if (!metadata[key]) {
          // Try to get metadata from storage entry
          const entry = await this.getFromStorage(key);
          if (entry) {
            metadata[key] = {
              timestamp: entry.timestamp || Date.now(),
              ttl: entry.ttl || this.config.defaultTTL,
              accessCount: entry.accessCount || 0,
              lastAccessed: entry.lastAccessed || Date.now(),
            };
          }
        }
      }

      await AppStorage.setObject(this.metadataKey, metadata);
    } catch (error) {
      logger.warn('Failed to save cache metadata', error, 'CacheManager');
    }
  }

  private isExpired(entry: CacheEntry): boolean {
    return Date.now() - entry.timestamp > entry.ttl;
  }

  private compress(data: any): string {
    if (!this.config.enableCompression) {
      return JSON.stringify(data);
    }

    try {
      const jsonString = JSON.stringify(data);
      if (jsonString.length < this.config.compressionThreshold) {
        return jsonString;
      }

      // Simple compression using repeated pattern replacement
      // In production, consider using a proper compression library
      let compressed = jsonString;
      const patterns = [
        [/"timestamp":/g, '"t":'],
        [/"createdAt":/g, '"c":'],
        [/"updatedAt":/g, '"u":'],
        [/"userId":/g, '"uid":'],
        [/"overallScore":/g, '"os":'],
      ];

      for (const [pattern, replacement] of patterns) {
        compressed = compressed.replace(pattern, replacement as string);
      }

      return compressed;
    } catch (error) {
      logger.warn('Compression failed, using uncompressed data', error, 'CacheManager');
      return JSON.stringify(data);
    }
  }

  private decompress(compressedData: string): any {
    if (!this.config.enableCompression) {
      return JSON.parse(compressedData);
    }

    try {
      // Reverse the compression patterns
      let decompressed = compressedData;
      const patterns = [
        [/"t":/g, '"timestamp":'],
        [/"c":/g, '"createdAt":'],
        [/"u":/g, '"updatedAt":'],
        [/"uid":/g, '"userId":'],
        [/"os":/g, '"overallScore":'],
      ];

      for (const [pattern, replacement] of patterns) {
        decompressed = decompressed.replace(pattern, replacement as string);
      }

      return JSON.parse(decompressed);
    } catch (error) {
      logger.warn('Decompression failed, trying direct parse', error, 'CacheManager');
      return JSON.parse(compressedData);
    }
  }

  private async getFromStorage(key: string): Promise<CacheEntry | null> {
    if (!this.config.enablePersistence) return null;

    try {
      const data = await AppStorage.getItem(this.storagePrefix + key);
      if (!data) return null;

      const entry = this.decompress(data) as CacheEntry;
      if (this.isExpired(entry)) {
        await this.removeFromStorage(key);
        return null;
      }

      return entry;
    } catch (error) {
      logger.warn(`Failed to get ${key} from storage`, error, 'CacheManager');
      return null;
    }
  }

  private async setToStorage(key: string, entry: CacheEntry): Promise<void> {
    if (!this.config.enablePersistence) return;

    try {
      const compressed = this.compress(entry);
      await AppStorage.setItem(this.storagePrefix + key, compressed);
    } catch (error) {
      logger.warn(`Failed to set ${key} to storage`, error, 'CacheManager');
    }
  }

  private async removeFromStorage(key: string): Promise<void> {
    if (!this.config.enablePersistence) return;

    try {
      await AppStorage.removeItem(this.storagePrefix + key);
    } catch (error) {
      logger.warn(`Failed to remove ${key} from storage`, error, 'CacheManager');
    }
  }

  private evictLeastRecentlyUsed(): void {
    if (this.memoryCache.size <= this.config.maxMemoryEntries) return;

    // Find the least recently used entry
    let lruKey: string | null = null;
    let lruTime = Date.now();

    for (const [key, entry] of this.memoryCache.entries()) {
      if (entry.lastAccessed < lruTime) {
        lruTime = entry.lastAccessed;
        lruKey = key;
      }
    }

    if (lruKey) {
      const entry = this.memoryCache.get(lruKey)!;
      this.memoryCache.delete(lruKey);
      
      // Move to storage if not expired
      if (!this.isExpired(entry)) {
        this.setToStorage(lruKey, entry);
      }
    }
  }

  private async cleanup(): Promise<void> {
    const startTime = Date.now();
    let cleanedCount = 0;

    // Cleanup memory cache
    for (const [key, entry] of this.memoryCache.entries()) {
      if (this.isExpired(entry)) {
        this.memoryCache.delete(key);
        cleanedCount++;
      }
    }

    // Cleanup storage cache
    if (this.config.enablePersistence) {
      try {
        const storageKeys = await AppStorage.getAllKeys();
        const cacheKeys = storageKeys.filter(key => key.startsWith(this.storagePrefix));
        
        for (const storageKey of cacheKeys) {
          const key = storageKey.replace(this.storagePrefix, '');
          const entry = await this.getFromStorage(key);
          if (!entry) {
            cleanedCount++;
          }
        }

        // Limit storage entries
        if (cacheKeys.length > this.config.maxStorageEntries) {
          const parsedMetadata = await AppStorage.getObject<Record<string, CacheEntry>>(this.metadataKey);
          if (parsedMetadata) {
            const sortedEntries = Object.entries(parsedMetadata)
              .sort(([, a], [, b]) => (a as CacheEntry).lastAccessed - (b as CacheEntry).lastAccessed);
            
            const toRemove = sortedEntries.slice(0, cacheKeys.length - this.config.maxStorageEntries);
            for (const [key] of toRemove) {
              await this.removeFromStorage(key);
              cleanedCount++;
            }
          }
        }
      } catch (error) {
        logger.warn('Storage cleanup failed', error, 'CacheManager');
      }
    }

    const duration = Date.now() - startTime;
    if (cleanedCount > 0) {
      logger.debug(`Cache cleanup completed: ${cleanedCount} entries removed in ${duration}ms`, 
        { cleanedCount, duration }, 'CacheManager');
    }

    // Save metadata after cleanup
    await this.saveMetadata();
  }

  // Public API

  /**
   * Get data from cache with automatic promotion to memory
   */
  async get<T = any>(key: string): Promise<T | null> {
    try {
      // Check memory cache first
      let entry = this.memoryCache.get(key);
      
      if (entry) {
        if (this.isExpired(entry)) {
          this.memoryCache.delete(key);
          await this.removeFromStorage(key);
          return null;
        }
        
        // Update access statistics
        entry.accessCount++;
        entry.lastAccessed = Date.now();
        return entry.data;
      }

      // Check storage cache
      const storageEntry = await this.getFromStorage(key);
      if (storageEntry) {
        // Promote to memory cache
        storageEntry.accessCount++;
        storageEntry.lastAccessed = Date.now();
        this.memoryCache.set(key, storageEntry);
        this.evictLeastRecentlyUsed();
        return storageEntry.data;
      }

      return null;
    } catch (error) {
      logger.warn(`Failed to get cache entry: ${key}`, error, 'CacheManager');
      return null;
    }
  }

  /**
   * Set data in cache with TTL
   */
  async set<T = any>(key: string, data: T, ttl?: number): Promise<void> {
    try {
      const entry: CacheEntry<T> = {
        data,
        timestamp: Date.now(),
        ttl: ttl || this.config.defaultTTL,
        accessCount: 1,
        lastAccessed: Date.now(),
      };

      this.memoryCache.set(key, entry);
      this.evictLeastRecentlyUsed();

      // Also save to storage for persistence
      await this.setToStorage(key, entry);
    } catch (error) {
      logger.warn(`Failed to set cache entry: ${key}`, error, 'CacheManager');
    }
  }

  /**
   * Delete data from cache
   */
  async delete(key: string): Promise<void> {
    try {
      this.memoryCache.delete(key);
      await this.removeFromStorage(key);
    } catch (error) {
      logger.warn(`Failed to delete cache entry: ${key}`, error, 'CacheManager');
    }
  }

  /**
   * Check if key exists in cache
   */
  async has(key: string): Promise<boolean> {
    const data = await this.get(key);
    return data !== null;
  }

  /**
   * Clear all cache data
   */
  async clear(): Promise<void> {
    try {
      this.memoryCache.clear();
      
      if (this.config.enablePersistence) {
        const storageKeys = await AppStorage.getAllKeys();
        const cacheKeys = storageKeys.filter(key => 
          key.startsWith(this.storagePrefix) || key === this.metadataKey
        );
        await AppStorage.multiRemove(cacheKeys);
      }
      
      logger.info('Cache cleared successfully', undefined, 'CacheManager');
    } catch (error) {
      logger.warn('Failed to clear cache', error, 'CacheManager');
    }
  }

  /**
   * Get cache statistics
   */
  async getStats(): Promise<{
    memoryEntries: number;
    storageEntries: number;
    totalSize: number;
    hitRate: number;
  }> {
    try {
      const memoryEntries = this.memoryCache.size;
      
      let storageEntries = 0;
      let totalSize = 0;
      
      if (this.config.enablePersistence) {
        const storageKeys = await AppStorage.getAllKeys();
        const cacheKeys = storageKeys.filter(key => key.startsWith(this.storagePrefix));
        storageEntries = cacheKeys.length;
        
        // Estimate total size
        for (const key of cacheKeys.slice(0, 10)) { // Sample first 10 for estimation
          const data = await AppStorage.getItem(key);
          if (data) {
            totalSize += data.length;
          }
        }
        totalSize = Math.round((totalSize / Math.min(10, cacheKeys.length)) * cacheKeys.length);
      }

      // Calculate hit rate from access counts
      let totalAccess = 0;
      let totalHits = 0;
      for (const entry of this.memoryCache.values()) {
        totalAccess += entry.accessCount;
        totalHits += entry.accessCount;
      }
      
      const hitRate = totalAccess > 0 ? (totalHits / totalAccess) * 100 : 0;

      return {
        memoryEntries,
        storageEntries,
        totalSize,
        hitRate: Math.round(hitRate * 100) / 100,
      };
    } catch (error) {
      logger.warn('Failed to get cache stats', error, 'CacheManager');
      return {
        memoryEntries: 0,
        storageEntries: 0,
        totalSize: 0,
        hitRate: 0,
      };
    }
  }

  /**
   * Preload data into cache
   */
  async preload(entries: Array<{ key: string; data: any; ttl?: number }>): Promise<void> {
    try {
      const promises = entries.map(({ key, data, ttl }) => this.set(key, data, ttl));
      await Promise.all(promises);
      logger.info(`Preloaded ${entries.length} cache entries`, undefined, 'CacheManager');
    } catch (error) {
      logger.warn('Failed to preload cache entries', error, 'CacheManager');
    }
  }

  /**
   * Cleanup and destroy cache manager
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
    this.cleanup();
    this.saveMetadata();
  }
}

// Export singleton instance
export const cacheManager = new CacheManager({
  maxMemoryEntries: 100,
  maxStorageEntries: 500,
  defaultTTL: 5 * 60 * 1000, // 5 minutes
  cleanupInterval: 60 * 1000, // 1 minute
  compressionThreshold: 1024, // 1KB
  enableCompression: true,
  enablePersistence: true,
});