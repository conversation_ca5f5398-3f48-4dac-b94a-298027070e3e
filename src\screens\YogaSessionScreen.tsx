import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,

} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useNavigation, useRoute } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';
import { LinearGradient } from 'expo-linear-gradient';
import { useAlert } from '../contexts/AlertContext';
import { RootStackParamList } from '../types';

type YogaSessionNavigationProp = StackNavigationProp<RootStackParamList, 'YogaSession'>;
type YogaSessionRouteProp = RouteProp<RootStackParamList, 'YogaSession'>;

const YogaSessionScreen: React.FC = () => {
  const { t, i18n } = useTranslation();
  const navigation = useNavigation<YogaSessionNavigationProp>();
  const route = useRoute<YogaSessionRouteProp>();
  const { showAlert } = useAlert();
  
  const [sessionStarted, setSessionStarted] = useState(false);
  const [currentPose, setCurrentPose] = useState(0);
  const [timer, setTimer] = useState(60);
  
  const poses = [
    {
      name: 'Mountain Pose',
      nameHindi: 'ताड़ासन',
      duration: 60,
      instructions: 'Stand tall with feet hip-width apart',
      instructionsHindi: 'पैरों को कूल्हे की चौड़ाई के बराबर रखकर सीधे खड़े हों',
    },
    {
      name: 'Cat-Cow Pose',
      nameHindi: 'मार्जरी आसन',
      duration: 120,
      instructions: 'Move between arching and rounding your spine',
      instructionsHindi: 'अपनी रीढ़ को मोड़ने और गोल करने के बीच चलें',
    },
  ];

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (sessionStarted && timer > 0) {
      interval = setInterval(() => {
        setTimer(timer - 1);
      }, 1000);
    } else if (timer === 0) {
      handleNextPose();
    }
    return () => clearInterval(interval);
  }, [sessionStarted, timer]);

  const startSession = () => {
    setSessionStarted(true);
    setTimer(poses[currentPose].duration);
  };

  const handleNextPose = () => {
    if (currentPose < poses.length - 1) {
      setCurrentPose(currentPose + 1);
      setTimer(poses[currentPose + 1].duration);
    } else {
      completeSession();
    }
  };

  const completeSession = () => {
    showAlert({
      title: '🧘‍♀️ Amazing Work!',
      message: i18n.language === 'hi'
        ? '🎉 बधाई हो! आपने योग सत्र पूरा किया। आप बहुत अच्छा कर रहे हैं!'
        : '🎉 Congratulations! You completed your yoga session. You\'re doing amazing on your wellness journey!',
      type: 'success',
      buttons: [
        { text: '✨ Awesome!', onPress: () => navigation.goBack() },
      ],
      userFriendly: true,
    });
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const currentPoseData = poses[currentPose];

  return (
    <View style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={['#6366F1', '#8B5CF6']}
        style={styles.header}
      >
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{t('yoga.title')}</Text>
        <View style={styles.placeholder} />
      </LinearGradient>

      {/* Session Content */}
      <View style={styles.content}>
        {!sessionStarted ? (
          <View style={styles.startContainer}>
            <View style={styles.posePreview}>
              <Ionicons name="fitness" size={64} color="#6366F1" />
            </View>
            
            <Text style={styles.sessionTitle}>
              {i18n.language === 'hi' 
                ? 'योग सत्र शुरू करने के लिए तैयार हैं?'
                : 'Ready to start your yoga session?'}
            </Text>
            
            <Text style={styles.sessionDescription}>
              {i18n.language === 'hi'
                ? `${poses.length} आसन • ${poses.reduce((total, pose) => total + pose.duration, 0) / 60} मिनट`
                : `${poses.length} poses • ${poses.reduce((total, pose) => total + pose.duration, 0) / 60} minutes`}
            </Text>
            
            <TouchableOpacity style={styles.startButton} onPress={startSession}>
              <Text style={styles.startButtonText}>
                {i18n.language === 'hi' ? 'सत्र शुरू करें' : 'Start Session'}
              </Text>
            </TouchableOpacity>
          </View>
        ) : (
          <View style={styles.sessionContainer}>
            {/* Progress */}
            <View style={styles.progressContainer}>
              <Text style={styles.progressText}>
                {currentPose + 1} / {poses.length}
              </Text>
              <View style={styles.progressBar}>
                <View 
                  style={[
                    styles.progressFill, 
                    { width: `${((currentPose + 1) / poses.length) * 100}%` }
                  ]} 
                />
              </View>
            </View>

            {/* Current Pose */}
            <View style={styles.poseContainer}>
              <View style={styles.poseImage}>
                <Ionicons name="fitness" size={80} color="#6366F1" />
              </View>
              
              <Text style={styles.poseName}>
                {i18n.language === 'hi' ? currentPoseData.nameHindi : currentPoseData.name}
              </Text>
              
              <Text style={styles.poseInstructions}>
                {i18n.language === 'hi' ? currentPoseData.instructionsHindi : currentPoseData.instructions}
              </Text>
            </View>

            {/* Timer */}
            <View style={styles.timerContainer}>
              <Text style={styles.timerText}>{formatTime(timer)}</Text>
              <View style={styles.timerProgress}>
                <View 
                  style={[
                    styles.timerProgressFill,
                    { width: `${((currentPoseData.duration - timer) / currentPoseData.duration) * 100}%` }
                  ]}
                />
              </View>
            </View>

            {/* Controls */}
            <View style={styles.controls}>
              <TouchableOpacity 
                style={styles.controlButton}
                onPress={() => setSessionStarted(false)}
              >
                <Ionicons name="pause" size={24} color="#6366F1" />
                <Text style={styles.controlButtonText}>
                  {i18n.language === 'hi' ? 'रोकें' : 'Pause'}
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.controlButton}
                onPress={handleNextPose}
              >
                <Ionicons name="play-forward" size={24} color="#6366F1" />
                <Text style={styles.controlButtonText}>
                  {i18n.language === 'hi' ? 'अगला' : 'Next'}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 50,
    paddingBottom: 16,
    paddingHorizontal: 20,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  startContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  posePreview: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: '#EEF2FF',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 32,
  },
  sessionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#111827',
    textAlign: 'center',
    marginBottom: 12,
  },
  sessionDescription: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    marginBottom: 32,
  },
  startButton: {
    backgroundColor: '#6366F1',
    paddingHorizontal: 32,
    paddingVertical: 16,
    borderRadius: 12,
  },
  startButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  sessionContainer: {
    flex: 1,
  },
  progressContainer: {
    marginBottom: 32,
  },
  progressText: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    marginBottom: 8,
  },
  progressBar: {
    height: 4,
    backgroundColor: '#E5E7EB',
    borderRadius: 2,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#6366F1',
    borderRadius: 2,
  },
  poseContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  poseImage: {
    width: 160,
    height: 160,
    borderRadius: 80,
    backgroundColor: '#EEF2FF',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  poseName: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#111827',
    textAlign: 'center',
    marginBottom: 16,
  },
  poseInstructions: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 24,
    paddingHorizontal: 20,
  },
  timerContainer: {
    alignItems: 'center',
    marginBottom: 32,
  },
  timerText: {
    fontSize: 48,
    fontWeight: 'bold',
    color: '#6366F1',
    marginBottom: 16,
  },
  timerProgress: {
    width: '100%',
    height: 8,
    backgroundColor: '#E5E7EB',
    borderRadius: 4,
  },
  timerProgressFill: {
    height: '100%',
    backgroundColor: '#6366F1',
    borderRadius: 4,
  },
  controls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  controlButton: {
    alignItems: 'center',
    padding: 16,
  },
  controlButtonText: {
    fontSize: 14,
    color: '#6366F1',
    marginTop: 8,
    fontWeight: '500',
  },
});

export default YogaSessionScreen;