import React from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  ActivityIndicator,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { Theme } from '../constants/designTokens';

interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'ghost' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  fullWidth?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
}

const Button: React.FC<ButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  fullWidth = false,
  style,
  textStyle,
  icon,
  iconPosition = 'left',
}) => {
  const getButtonStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: Theme.borderRadius.lg,
      ...Theme.shadows.sm,
    };

    // Size variations
    const sizeStyles: Record<string, ViewStyle> = {
      sm: {
        paddingHorizontal: Theme.spacing.md,
        paddingVertical: Theme.spacing.sm,
        minHeight: Theme.layout.button.height.sm,
      },
      md: {
        paddingHorizontal: Theme.spacing.xl,
        paddingVertical: Theme.spacing.md,
        minHeight: Theme.layout.button.height.md,
      },
      lg: {
        paddingHorizontal: Theme.spacing['2xl'],
        paddingVertical: Theme.spacing.lg,
        minHeight: Theme.layout.button.height.lg,
      },
    };

    // Variant styles
    const variantStyles: Record<string, ViewStyle> = {
      primary: {
        backgroundColor: disabled ? Theme.colors.neutral[300] : Theme.colors.primary[500],
        borderWidth: 0,
      },
      secondary: {
        backgroundColor: Theme.colors.surface.primary,
        borderWidth: 1,
        borderColor: disabled ? Theme.colors.neutral[300] : Theme.colors.border.primary,
        shadowOpacity: 0,
        elevation: 0,
      },
      ghost: {
        backgroundColor: 'transparent',
        borderWidth: 0,
        shadowOpacity: 0,
        elevation: 0,
      },
      danger: {
        backgroundColor: disabled ? Theme.colors.neutral[300] : Theme.colors.error[500],
        borderWidth: 0,
      },
    };

    return {
      ...baseStyle,
      ...sizeStyles[size],
      ...variantStyles[variant],
      width: fullWidth ? '100%' : 'auto',
      opacity: disabled ? 0.6 : 1,
    };
  };

  const getTextStyle = (): TextStyle => {
    const baseStyle: TextStyle = {
      fontWeight: Theme.typography.fontWeight.semibold,
      textAlign: 'center',
    };

    // Size text styles
    const sizeTextStyles: Record<string, TextStyle> = {
      sm: {
        fontSize: Theme.typography.fontSize.sm,
        lineHeight: Theme.typography.fontSize.sm * Theme.typography.lineHeight.tight,
      },
      md: {
        fontSize: Theme.typography.fontSize.base,
        lineHeight: Theme.typography.fontSize.base * Theme.typography.lineHeight.tight,
      },
      lg: {
        fontSize: Theme.typography.fontSize.lg,
        lineHeight: Theme.typography.fontSize.lg * Theme.typography.lineHeight.tight,
      },
    };

    // Variant text styles
    const variantTextStyles: Record<string, TextStyle> = {
      primary: {
        color: Theme.colors.text.inverse,
      },
      secondary: {
        color: disabled ? Theme.colors.text.disabled : Theme.colors.text.primary,
      },
      ghost: {
        color: disabled ? Theme.colors.text.disabled : Theme.colors.primary[500],
      },
      danger: {
        color: Theme.colors.text.inverse,
      },
    };

    return {
      ...baseStyle,
      ...sizeTextStyles[size],
      ...variantTextStyles[variant],
    };
  };

  const renderContent = () => {
    if (loading) {
      return (
        <ActivityIndicator
          size="small"
          color={variant === 'primary' || variant === 'danger' ? Theme.colors.text.inverse : Theme.colors.primary[500]}
        />
      );
    }

    const textElement = (
      <Text style={[getTextStyle(), textStyle]}>
        {title}
      </Text>
    );

    if (!icon) {
      return textElement;
    }

    const iconElement = (
      <React.Fragment>
        {icon}
      </React.Fragment>
    );

    return (
      <React.Fragment>
        {iconPosition === 'left' && (
          <React.Fragment>
            {iconElement}
            <Text style={{ width: Theme.spacing.sm }} />
          </React.Fragment>
        )}
        {textElement}
        {iconPosition === 'right' && (
          <React.Fragment>
            <Text style={{ width: Theme.spacing.sm }} />
            {iconElement}
          </React.Fragment>
        )}
      </React.Fragment>
    );
  };

  return (
    <TouchableOpacity
      style={[getButtonStyle(), style]}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.8}
    >
      {renderContent()}
    </TouchableOpacity>
  );
};

export default Button;