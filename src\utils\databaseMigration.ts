import { supabase } from '../lib/supabase';
import { logger } from './logger';

export class DatabaseMigration {
  static async ensurePostureAnalysesTable(): Promise<boolean> {
    try {
      // Check if the table exists by trying to select from it
      const { error } = await supabase
        .from('posture_analyses')
        .select('id')
        .limit(1);

      if (error) {
        // If table doesn't exist, it will be created by the SQL schema
        logger.warn('Posture analyses table may not exist', error, 'DatabaseMigration');
        return false;
      }

      logger.info('Posture analyses table exists', {}, 'DatabaseMigration');
      return true;
    } catch (error) {
      logger.error('Error checking posture analyses table', error, 'DatabaseMigration');
      return false;
    }
  }

  static async runMigrations(): Promise<void> {
    try {
      logger.info('Running database migrations', {}, 'DatabaseMigration');
      
      // Check and create tables as needed
      await this.ensurePostureAnalysesTable();
      
      logger.info('Database migrations completed', {}, 'DatabaseMigration');
    } catch (error) {
      logger.error('Database migration failed', error, 'DatabaseMigration');
    }
  }
}

export default DatabaseMigration;