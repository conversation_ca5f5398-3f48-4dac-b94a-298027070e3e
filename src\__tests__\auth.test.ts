import { ValidationUtils } from '../utils/validation';

describe('Authentication Tests', () => {
  describe('Name Validation', () => {
    test('should allow names with spaces', () => {
      const result = ValidationUtils.validateName('<PERSON>');
      expect(result.isValid).toBe(true);
    });

    test('should allow names with multiple words', () => {
      const result = ValidationUtils.validateName('<PERSON>');
      expect(result.isValid).toBe(true);
    });

    test('should allow names with hyphens and apostrophes', () => {
      const result = ValidationUtils.validateName("<PERSON><PERSON><PERSON>");
      expect(result.isValid).toBe(true);
    });

    test('should reject names that are too short', () => {
      const result = ValidationUtils.validateName('A');
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('at least 2 characters');
    });

    test('should reject names that are too long', () => {
      const longName = 'A'.repeat(51);
      const result = ValidationUtils.validateName(longName);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('not exceed 50 characters');
    });

    test('should reject names with invalid characters', () => {
      const result = ValidationUtils.validateName('John123');
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('letters, spaces, hyphens, and apostrophes');
    });
  });

  describe('Input Sanitization', () => {
    test('should preserve single spaces', () => {
      const result = ValidationUtils.sanitizeInput('John Doe');
      expect(result).toBe('John Doe');
    });

    test('should collapse multiple spaces to single space', () => {
      const result = ValidationUtils.sanitizeInput('John    Doe');
      expect(result).toBe('John Doe');
    });

    test('should trim leading and trailing spaces', () => {
      const result = ValidationUtils.sanitizeInput('  John Doe  ');
      expect(result).toBe('John Doe');
    });
  });

  describe('Email Validation', () => {
    test('should validate correct email addresses', () => {
      expect(ValidationUtils.validateEmail('<EMAIL>')).toBe(true);
      expect(ValidationUtils.validateEmail('<EMAIL>')).toBe(true);
    });

    test('should reject invalid email addresses', () => {
      expect(ValidationUtils.validateEmail('invalid-email')).toBe(false);
      expect(ValidationUtils.validateEmail('test@')).toBe(false);
      expect(ValidationUtils.validateEmail('@domain.com')).toBe(false);
    });
  });
});
