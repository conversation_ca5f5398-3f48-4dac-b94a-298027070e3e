// User Types
export interface User {
  id: string;
  email: string;
  name: string;
  phone?: string;
  profileImage?: string;
  subscriptionTier: SubscriptionTier;
  createdAt: Date;
  lastActiveAt: Date;
  preferences: UserPreferences;
  familyMembers?: FamilyMember[];
  onboardingCompleted?: boolean;
  questionnaireCompleted?: boolean;
  lastQuestionnaireAt?: Date;
  needsOnboardingQuestionnaire?: boolean;
}

export interface UserPreferences {
  language: 'en' | 'hi' | 'ta' | 'te' | 'bn' | 'mr';
  notifications: boolean;
  reminderTime?: string;
  yogaExperience: 'beginner' | 'intermediate' | 'advanced';
  focusAreas: PostureFocusArea[];

  // Enhanced preference system (optional for backward compatibility)
  workProfile?: WorkProfile;
  healthProfile?: HealthProfile;
  goals?: UserGoals;
  lifestyle?: LifestyleProfile;
  deviceUsage?: DeviceUsageProfile;
  personalizedSettings?: PersonalizedSettings;

  // New questionnaire-based preferences
  notificationsEnabled?: boolean;
  reminderFrequency?: 'never' | 'daily' | 'weekly' | 'custom';
  preferredExerciseTime?: 'morning' | 'afternoon' | 'evening' | 'flexible';
  fitnessLevel?: 'beginner' | 'intermediate' | 'advanced';
  availableTimeMinutes?: number;
  painAreas?: string[];
  workEnvironment?: 'office' | 'home' | 'hybrid' | 'standing' | 'mobile';
  dailyScreenTimeHours?: number;
  exercisePreferences?: string[];
  healthConditions?: string[];
}

export interface WorkProfile {
  workType: 'desk_job' | 'standing_job' | 'mixed' | 'physical_labor' | 'student' | 'retired' | 'other';
  hoursPerDay: number;
  workEnvironment: 'office' | 'home' | 'hybrid' | 'outdoor' | 'mobile';
  deskSetup: {
    hasErgonomicChair: boolean;
    hasStandingDesk: boolean;
    monitorHeight: 'too_low' | 'eye_level' | 'too_high' | 'multiple_monitors';
    keyboardMouse: 'standard' | 'ergonomic' | 'laptop_only';
  };
  breakFrequency: 'every_30min' | 'every_hour' | 'every_2hours' | 'rarely' | 'never';
  stressLevel: 1 | 2 | 3 | 4 | 5;
}

export interface HealthProfile {
  currentIssues: HealthIssue[];
  pastInjuries: PastInjury[];
  painAreas: BodyPart[];
  painLevel: 1 | 2 | 3 | 4 | 5;
  exerciseFrequency: 'daily' | 'few_times_week' | 'weekly' | 'monthly' | 'rarely' | 'never';
  sleepQuality: 1 | 2 | 3 | 4 | 5;
  energyLevel: 1 | 2 | 3 | 4 | 5;
  medicalConditions: MedicalCondition[];
  medications: string[];
}

export interface UserGoals {
  primary: GoalType;
  secondary: GoalType[];
  timeCommitment: '5-10min' | '10-20min' | '20-30min' | '30-60min' | '60min+';
  preferredTime: 'morning' | 'lunch' | 'evening' | 'night' | 'flexible';
  motivationFactors: MotivationFactor[];
  targetAchievements: string[]; // Achievement IDs
}

export interface LifestyleProfile {
  activityLevel: 'sedentary' | 'lightly_active' | 'moderately_active' | 'very_active' | 'extremely_active';
  sportsActivities: SportActivity[];
  hobbies: Hobby[];
  stressFactors: StressFactor[];
  sleepSchedule: {
    bedtime: string;
    wakeTime: string;
    sleepDuration: number;
  };
  dietaryHabits: 'healthy' | 'moderate' | 'poor' | 'vegetarian' | 'vegan' | 'keto' | 'other';
}

export interface DeviceUsageProfile {
  primaryDevices: DeviceType[];
  screenTime: {
    computer: number; // hours per day
    phone: number;
    tablet: number;
    tv: number;
  };
  usagePatterns: {
    longestContinuousUse: number; // hours
    averageBreakDuration: number; // minutes
    eyeStrainFrequency: 'never' | 'rarely' | 'sometimes' | 'often' | 'always';
  };
}

export interface PersonalizedSettings {
  reminderStyle: 'gentle' | 'firm' | 'motivational' | 'educational';
  feedbackPreference: 'immediate' | 'summary' | 'weekly_report' | 'minimal';
  difficultyProgression: 'slow' | 'moderate' | 'fast' | 'adaptive';
  contentPreference: 'video' | 'audio' | 'text' | 'mixed';
  privacyLevel: 'minimal' | 'standard' | 'enhanced' | 'maximum';
  dataSharing: {
    analytics: boolean;
    research: boolean;
    marketing: boolean;
  };
}

// Supporting types
export type HealthIssue = 
  | 'chronic_back_pain'
  | 'neck_pain'
  | 'shoulder_tension'
  | 'headaches'
  | 'eye_strain'
  | 'carpal_tunnel'
  | 'sciatica'
  | 'arthritis'
  | 'fibromyalgia'
  | 'other';

export interface PastInjury {
  type: string;
  bodyPart: BodyPart;
  severity: 'mild' | 'moderate' | 'severe';
  recoveryStatus: 'fully_recovered' | 'mostly_recovered' | 'ongoing_issues';
  yearOccurred: number;
}

export type MedicalCondition = 
  | 'diabetes'
  | 'hypertension'
  | 'heart_disease'
  | 'respiratory_issues'
  | 'autoimmune_disorder'
  | 'mental_health'
  | 'pregnancy'
  | 'other';

export type GoalType = 
  | 'pain_relief'
  | 'posture_improvement'
  | 'stress_reduction'
  | 'flexibility_increase'
  | 'strength_building'
  | 'energy_boost'
  | 'sleep_improvement'
  | 'productivity_enhancement'
  | 'injury_prevention'
  | 'general_wellness';

export type MotivationFactor = 
  | 'health_improvement'
  | 'pain_reduction'
  | 'appearance'
  | 'productivity'
  | 'energy'
  | 'stress_relief'
  | 'social_support'
  | 'achievement_badges'
  | 'progress_tracking'
  | 'expert_guidance';

export type SportActivity = 
  | 'running'
  | 'cycling'
  | 'swimming'
  | 'yoga'
  | 'pilates'
  | 'weightlifting'
  | 'tennis'
  | 'basketball'
  | 'football'
  | 'martial_arts'
  | 'dancing'
  | 'hiking'
  | 'other';

export type Hobby = 
  | 'reading'
  | 'gaming'
  | 'music'
  | 'art'
  | 'cooking'
  | 'gardening'
  | 'photography'
  | 'crafts'
  | 'writing'
  | 'traveling'
  | 'other';

export type StressFactor = 
  | 'work_pressure'
  | 'family_responsibilities'
  | 'financial_concerns'
  | 'health_issues'
  | 'relationship_problems'
  | 'time_management'
  | 'technology_overload'
  | 'social_media'
  | 'news_anxiety'
  | 'other';

export type DeviceType = 
  | 'desktop_computer'
  | 'laptop'
  | 'smartphone'
  | 'tablet'
  | 'gaming_console'
  | 'smart_tv'
  | 'smartwatch'
  | 'other';

// Questionnaire system types
export interface QuestionnaireQuestion {
  id: string;
  type: 'single_choice' | 'multiple_choice' | 'scale' | 'text' | 'number' | 'time' | 'boolean' | 'rating_scale' | 'body_map' | 'info_display';
  category: QuestionCategory;
  question?: string; // Made optional for backward compatibility
  questionHindi?: string;
  title?: string; // New field for enhanced questionnaire
  titleHindi?: string; // New field for enhanced questionnaire
  description?: string;
  descriptionHindi?: string;
  options?: QuestionOption[];
  scaleMin?: number;
  scaleMax?: number;
  scaleStep?: number;
  scaleLabels?: { [key: number]: string };
  scaleLabelsHindi?: { [key: number]: string };
  bodyAreas?: BodyArea[];
  validation?: QuestionValidation;
  conditional?: ConditionalLogic;
  priority?: 'high' | 'medium' | 'low'; // Made optional for enhanced questionnaire
  required?: boolean; // Made optional for enhanced questionnaire
  order: number;
}

export interface QuestionOption {
  id?: string; // Made optional for enhanced questionnaire
  label: string;
  labelHindi?: string;
  value: any;
  icon?: string;
  description?: string;
}

export interface QuestionValidation {
  required?: boolean;
  min?: number;
  max?: number;
  pattern?: string;
  customValidator?: string;
  minSelections?: number;
  maxSelections?: number;
  errorMessage?: string;
  errorMessageHindi?: string;
}

export interface ConditionalLogic {
  dependsOn: string; // question ID
  condition: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains' | 'not_contains';
  value: any;
}

export type QuestionCategory =
  | 'basic_info'
  | 'work_profile'
  | 'health_profile'
  | 'goals'
  | 'lifestyle'
  | 'device_usage'
  | 'preferences'
  | 'demographics'
  | 'work_environment'
  | 'digital_wellness'
  | 'health_assessment'
  | 'fitness_assessment'
  | 'introduction';

export interface QuestionnaireResponse {
  id: string;
  sessionId: string;
  userId: string;
  questionId: string;
  questionCategory: string;
  questionText?: string; // Made optional for backward compatibility
  answerValue: any;
  answerData?: {
    confidenceLevel?: number;
    responseTimeMs?: number;
    questionType?: string;
  };
  confidenceLevel: number;
  responseTimeMs: number;
  createdAt: Date;
  // Legacy fields for backward compatibility
  answer?: any;
  timestamp?: Date;
  confidence?: number;
}

export interface QuestionnaireSession {
  id: string;
  userId: string;
  type: 'onboarding' | 'periodic_update' | 'goal_reassessment' | 'health_check';
  startedAt: Date;
  completedAt?: Date;
  responses: QuestionnaireResponse[];
  currentQuestionIndex: number;
  totalQuestions: number;
  completionPercentage: number;
  insights?: UserInsight[];
  recommendations?: PersonalizedRecommendation[];
  estimatedTimeRemaining?: number;
  sessionData?: any;
}

export interface UserInsight {
  id: string;
  category: InsightCategory;
  type?: string; // Made optional for backward compatibility
  title: string;
  description: string;
  severity: 'low' | 'medium' | 'high';
  priority?: number; // Made optional for backward compatibility
  confidence: number;
  actionable: boolean;
  relatedQuestions: string[];
  data?: Record<string, any>; // Made optional for backward compatibility
  createdAt?: Date; // Made optional for backward compatibility
}

export type InsightCategory = 
  | 'posture_risk'
  | 'ergonomic_issue'
  | 'lifestyle_factor'
  | 'health_concern'
  | 'goal_alignment'
  | 'motivation_opportunity'
  | 'habit_formation';

export interface PersonalizedRecommendation {
  id: string;
  type: RecommendationType;
  title: string;
  description: string;
  priority: 'high' | 'medium' | 'low' | number; // Support both string and number for compatibility
  estimatedImpact?: number; // 1-10 scale (optional for backward compatibility)
  timeToImplement?: 'immediate' | 'days' | 'weeks' | 'months'; // Optional for backward compatibility
  estimatedTimeMinutes?: number; // New field for enhanced system
  difficultyLevel?: 'beginner' | 'intermediate' | 'advanced'; // Made optional for backward compatibility
  category: string;
  tags?: string[]; // Made optional for backward compatibility
  actionSteps?: string[]; // Optional for backward compatibility
  relatedInsights?: string[]; // Optional for backward compatibility
  completionStatus?: 'pending' | 'in_progress' | 'completed' | 'skipped';
  data?: Record<string, any>; // Made optional for backward compatibility
  createdAt?: Date; // Made optional for backward compatibility
}

export type RecommendationType =
  | 'exercise_routine'
  | 'ergonomic_adjustment'
  | 'lifestyle_change'
  | 'habit_formation'
  | 'goal_modification'
  | 'app_setting'
  | 'professional_consultation'
  | 'exercise'
  | 'habit'
  | 'lifestyle'
  | 'environment'
  | 'mindfulness';

// Achievement system for questionnaire engagement
export interface QuestionnaireAchievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  criteria: AchievementCriteria;
  reward: AchievementReward;
}

export interface AchievementCriteria {
  type: 'completion' | 'consistency' | 'honesty' | 'improvement';
  threshold: number;
  timeframe?: string;
}

export interface AchievementReward {
  type: 'badge' | 'feature_unlock' | 'content_access' | 'discount';
  value: string;
  description: string;
}

export interface FamilyMember {
  id: string;
  name: string;
  relationship: string;
  age: number;
  profileImage?: string;
}

// Subscription Types
export type SubscriptionTier = 'free' | 'plus' | 'pro' | 'premium';

export interface Subscription {
  id: string;
  userId: string;
  tier: SubscriptionTier;
  status: 'active' | 'cancelled' | 'expired';
  startDate: Date;
  endDate: Date;
  paymentMethod: PaymentMethod;
  autoRenew: boolean;
}

export interface PaymentMethod {
  type: 'upi' | 'paytm' | 'card' | 'netbanking';
  details: string;
}

// Posture & Exercise Types
export interface PostureAnalysis {
  id: string;
  userId?: string;
  overallScore: number; // 0-100
  neckAngle: number;
  shoulderAlignment: number;
  spineAlignment: number;
  hipAlignment: number;
  recommendations: string[];
  landmarks: PoseKeyPoint[];
  imageUrl?: string;
  sessionDuration: number; // in seconds
  duration?: number; // alias for sessionDuration for backward compatibility
  issues?: PostureIssue[]; // detected posture issues
  deviceInfo?: {
    platform: string;
    version: string;
    model?: string;
  };
  environmentalFactors?: {
    lighting?: 'poor' | 'fair' | 'good' | 'excellent';
    cameraAngle?: 'front' | 'side' | 'back';
    distance?: number;
  };
  createdAt: Date;
  updatedAt?: Date;
}

export interface PostureIssue {
  type: PostureIssueType;
  severity: 'low' | 'medium' | 'high';
  description: string;
  affectedBodyParts: BodyPart[];
}

export type PostureIssueType = 
  | 'forward_head'
  | 'rounded_shoulders'
  | 'hunched_back'
  | 'tilted_pelvis'
  | 'uneven_shoulders'
  | 'poor_spinal_alignment';

export type BodyPart = 
  | 'head'
  | 'neck'
  | 'shoulders'
  | 'upper_back'
  | 'lower_back'
  | 'pelvis'
  | 'hips';

export type PostureFocusArea = 
  | 'desk_work'
  | 'study_posture'
  | 'general_wellness'
  | 'back_pain_relief'
  | 'neck_strain';

// Yoga & Exercise Types
export interface YogaPose {
  id: string;
  name: string;
  nameHindi: string;
  description: string;
  descriptionHindi: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  duration: number; // in seconds
  targetAreas: BodyPart[];
  benefits: string[];
  instructions: string[];
  instructionsHindi: string[];
  imageUrl: string;
  videoUrl?: string;
  keyPoints: PoseKeyPoint[];
}

export interface PoseKeyPoint {
  name?: string;
  x: number;
  y: number;
  z?: number;
  visibility?: number;
}

export interface YogaSession {
  id: string;
  userId?: string;
  exerciseId: string;
  exerciseName: string;
  duration: number; // in seconds
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  completedPoses: Array<{
    poseId: string;
    poseName: string;
    duration: number;
    accuracy: number;
    landmarks?: PoseKeyPoint[];
  }>;
  overallAccuracy: number; // 0-100
  caloriesBurned: number;
  heartRateData?: Array<{
    timestamp: number;
    bpm: number;
  }>;
  feedback?: {
    strengths: string[];
    improvements: string[];
    nextRecommendations: string[];
  };
  createdAt: Date;
  updatedAt?: Date;
}

// Legacy type for backward compatibility
export interface ExerciseSession extends YogaSession {}

// AR & Camera Types
export interface ARPoseDetection {
  landmarks: PoseKeyPoint[];
  worldLandmarks?: PoseKeyPoint[]; // 3D world coordinates
  confidence: number;
  timestamp: number;
  processingTime?: number; // Processing time in milliseconds
  frameRate?: number; // Current frame rate
  segmentationMask?: ImageData; // Optional segmentation mask
}

export interface PostureIssue {
  type: PostureIssueType;
  severity: 'low' | 'medium' | 'high';
  description: string;
  affectedBodyParts: BodyPart[];
}

export interface CameraPermissions {
  camera: boolean;
  microphone: boolean;
}

// Analytics Types
export interface UserAnalytics {
  totalSessions: number;
  averageScore: number;
  improvementRate: number;
  weeklyProgress: DailyProgress[];
  monthlyProgress?: DailyProgress[];
  streakDays: number;
  lastSessionDate: Date | null;
  totalMinutes: number;
  bestScore: number;
  problemAreas: string[];
  recommendations: string[];
}

export interface DailyProgress {
  date: string;
  score: number;
  sessionsCount: number;
}

export interface MonthlyProgress {
  month: string;
  totalSessions: number;
  totalMinutes: number;
  averageScore: number;
  improvement: number;
}

// Navigation Types
export type RootStackParamList = {
  Splash: undefined;
  Onboarding: undefined;
  Auth: undefined;
  Main: undefined;
  PostureCheck: undefined;
  PostureHistory: undefined;
  YogaSession: { poseId?: string };
  Analytics: undefined;
  Profile: undefined;
  Subscription: undefined;
  Settings: undefined;
  Family: undefined;
  TermsPrivacy: undefined;
  Questionnaire: {
    type?: 'onboarding' | 'periodic_update' | 'goal_reassessment' | 'health_check';
    resumeSession?: boolean;
  };
  QuestionnaireResults: {
    sessionId: string;
    insights: UserInsight[];
    recommendations: PersonalizedRecommendation[];
  };
};

export type MainTabParamList = {
  Home: undefined;
  PostureCheck: undefined;
  Exercises: undefined;
  Progress: undefined;
  Profile: undefined;
};

export type AllScreenParamList = RootStackParamList & MainTabParamList;

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

// Notification Types
export interface NotificationData {
  type: 'reminder' | 'achievement' | 'tip' | 'family_update';
  title: string;
  body: string;
  data?: any;
}

// Error Types
export interface AppError {
  code: string;
  message: string;
  details?: any;
}

// Constants
export const SUBSCRIPTION_FEATURES = {
  free: {
    postureChecks: 5,
    exercises: 5,
    analytics: false,
    familyTracking: false,
    ads: true,
  },
  plus: {
    postureChecks: -1, // unlimited
    exercises: -1,
    analytics: true,
    familyTracking: false,
    ads: false,
  },
  pro: {
    postureChecks: -1,
    exercises: -1,
    analytics: true,
    familyTracking: true,
    ads: false,
    aiCoaching: true,
  },
  premium: {
    postureChecks: -1,
    exercises: -1,
    analytics: true,
    familyTracking: true,
    ads: false,
    aiCoaching: true,
    wearableIntegration: true,
    personalizedPrograms: true,
  },
} as const;

export const SUBSCRIPTION_PRICES = {
  plus: { monthly: 99, yearly: 999 },
  pro: { monthly: 199, yearly: 1999 },
  premium: { monthly: 299, yearly: 2999 },
} as const;

// Additional Enhanced Questionnaire Types
export interface BodyArea {
  id: string;
  label: string;
  labelHindi: string;
  coordinates: { x: number; y: number };
  layer?: 'skeletal' | 'muscular' | 'nervous' | 'surface';
}



export interface AnalyticsEvent {
  id: string;
  type: string;
  userId: string;
  timestamp: Date;
  properties: Record<string, any>;
}

