/**
 * Validation utilities for form inputs
 */

export class ValidationUtils {
  // Email validation
  static validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email.trim());
  }

  // Password validation (industry standard)
  static validatePassword(password: string): {
    isValid: boolean;
    errors: string[];
    strength: number;
  } {
    const errors: string[] = [];
    let strength = 0;

    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long');
    } else {
      strength += 1;
    }

    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    } else {
      strength += 1;
    }

    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    } else {
      strength += 1;
    }

    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    } else {
      strength += 1;
    }

    if (!/[@$!%*?&]/.test(password)) {
      errors.push('Password must contain at least one special character (@$!%*?&)');
    } else {
      strength += 1;
    }

    return {
      isValid: errors.length === 0,
      errors,
      strength
    };
  }

  // Phone number validation
  static validatePhone(phone: string, countryCode: string = '+1'): {
    isValid: boolean;
    formatted: string;
    error?: string;
  } {
    // Remove all non-digit characters
    const cleaned = phone.replace(/\D/g, '');
    
    // Check length based on country (basic validation)
    const minLength = 7;
    const maxLength = 15;
    
    if (cleaned.length < minLength) {
      return {
        isValid: false,
        formatted: phone,
        error: `Phone number must be at least ${minLength} digits`
      };
    }
    
    if (cleaned.length > maxLength) {
      return {
        isValid: false,
        formatted: phone,
        error: `Phone number must not exceed ${maxLength} digits`
      };
    }

    // Format phone number
    const formatted = this.formatPhoneNumber(cleaned);
    
    return {
      isValid: true,
      formatted
    };
  }

  // Format phone number for display
  static formatPhoneNumber(phone: string): string {
    const cleaned = phone.replace(/\D/g, '');
    
    if (cleaned.length <= 3) {
      return cleaned;
    } else if (cleaned.length <= 6) {
      return `${cleaned.slice(0, 3)} ${cleaned.slice(3)}`;
    } else if (cleaned.length <= 10) {
      return `${cleaned.slice(0, 3)} ${cleaned.slice(3, 6)} ${cleaned.slice(6)}`;
    } else {
      return `${cleaned.slice(0, 3)} ${cleaned.slice(3, 6)} ${cleaned.slice(6, 10)} ${cleaned.slice(10)}`;
    }
  }

  // Name validation
  static validateName(name: string): {
    isValid: boolean;
    error?: string;
  } {
    const trimmed = name.trim();
    
    if (trimmed.length < 2) {
      return {
        isValid: false,
        error: 'Name must be at least 2 characters long'
      };
    }
    
    if (trimmed.length > 50) {
      return {
        isValid: false,
        error: 'Name must not exceed 50 characters'
      };
    }
    
    if (!/^[a-zA-Z\s'-]+$/.test(trimmed)) {
      return {
        isValid: false,
        error: 'Name can only contain letters, spaces, hyphens, and apostrophes'
      };
    }
    
    return { isValid: true };
  }

  // General text sanitization
  static sanitizeInput(input: string): string {
    return input.trim().replace(/\s+/g, ' ');
  }

  // Check password strength
  static getPasswordStrength(password: string): {
    score: number;
    label: string;
    color: string;
  } {
    const validation = this.validatePassword(password);
    const score = validation.strength;
    
    switch (score) {
      case 0:
      case 1:
        return { score, label: 'Weak', color: '#FF3B30' };
      case 2:
        return { score, label: 'Fair', color: '#FF9500' };
      case 3:
        return { score, label: 'Good', color: '#FFCC00' };
      case 4:
      case 5:
        return { score, label: 'Strong', color: '#34C759' };
      default:
        return { score: 0, label: 'Weak', color: '#FF3B30' };
    }
  }
}