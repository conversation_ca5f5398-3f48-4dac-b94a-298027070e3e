import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import * as Localization from 'expo-localization';
import { AppStorage, STORAGE_KEYS } from '../utils/storage';

// Import translation files
import en from './en/translation.json';
import hi from './hi/translation.json';

const resources = {
  en: {
    translation: en,
  },
  hi: {
    translation: hi,
  },
};

const initI18n = async () => {
  let savedLanguage = await AppStorage.getItem(STORAGE_KEYS.USER_LANGUAGE);
  
  if (!savedLanguage) {
    // Detect device language
    const deviceLanguage = Localization.getLocales()[0]?.languageCode || 'en';
    savedLanguage = ['hi', 'en'].includes(deviceLanguage) ? deviceLanguage : 'en';
  }

  i18n
    .use(initReactI18next)
    .init({
      resources,
      lng: savedLanguage,
      fallbackLng: 'en',
      interpolation: {
        escapeValue: false,
      },
      react: {
        useSuspense: false,
      },
    });
};

// Initialize i18n
initI18n();

export const changeLanguage = async (language: string) => {
  await AppStorage.setItem(STORAGE_KEYS.USER_LANGUAGE, language);
  i18n.changeLanguage(language);
};

export default i18n;