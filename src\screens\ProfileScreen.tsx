import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,

  StatusBar,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { LinearGradient } from 'expo-linear-gradient';
import { useAuth } from '../contexts/AuthContext';
import { useAlert } from '../contexts/AlertContext';
import { RootStackParamList } from '../types';
import { changeLanguage } from '../locales/i18n';
import { Theme } from '../constants/designTokens';

type ProfileScreenNavigationProp = StackNavigationProp<RootStackParamList>;

const ProfileScreen: React.FC = () => {
  const { t, i18n } = useTranslation();
  const navigation = useNavigation<ProfileScreenNavigationProp>();
  const { user, logout } = useAuth();
  const { showAlert } = useAlert();

  const handleLogout = () => {
    showAlert({
      title: '👋 Ready to sign out?',
      message: 'You\'ll need to sign in again next time. Is that okay?',
      type: 'info',
      buttons: [
        { text: '❌ Stay Here', style: 'cancel' },
        { text: '👋 Sign Out', style: 'destructive', onPress: logout },
      ],
      userFriendly: true,
    });
  };

  const toggleLanguage = async () => {
    const newLanguage = i18n.language === 'en' ? 'hi' : 'en';
    await changeLanguage(newLanguage);
  };

  const handleRetakeQuestionnaire = () => {
    showAlert({
      title: '🔄 Update Your Wellness Plan?',
      message: 'Taking the questionnaire again will refresh your personalized recommendations based on how you\'re feeling now. Ready to update your plan?',
      type: 'info',
      buttons: [
        { text: '❌ Not Now', style: 'cancel' },
        {
          text: '✅ Let\'s Update!',
          onPress: () => navigation.navigate('Questionnaire', { type: 'goal_reassessment' })
        },
      ],
      userFriendly: true,
    });
  };

  const handleViewHealthProfile = () => {
    // For now, show user's questionnaire status
    const lastCompleted = user?.lastQuestionnaireAt
      ? new Date(user.lastQuestionnaireAt).toLocaleDateString()
      : 'Never';

    showAlert({
      title: '📊 Your Wellness Profile',
      message: `Questionnaire Status: ${user?.questionnaireCompleted ? '✅ Completed' : '⏳ Not Completed'}\nLast Updated: ${lastCompleted}`,
      type: 'info',
      buttons: [
        { text: '👍 Got it', style: 'cancel' },
        { text: '🔄 Update Plan', onPress: handleRetakeQuestionnaire },
      ],
      userFriendly: true,
    });
  };

  const accountItems = [
    {
      icon: 'person-outline',
      title: t('profile.editProfile'),
      onPress: () => { },
    },
    {
      icon: 'fitness-outline',
      title: i18n.language === 'hi' ? 'स्वास्थ्य प्रोफ़ाइल' : 'Health Profile',
      subtitle: user?.questionnaireCompleted
        ? (i18n.language === 'hi' ? 'पूर्ण' : 'Completed')
        : (i18n.language === 'hi' ? 'अधूरा' : 'Incomplete'),
      onPress: handleViewHealthProfile,
      showBadge: !user?.questionnaireCompleted,
    },
    {
      icon: 'refresh-outline',
      title: i18n.language === 'hi' ? 'प्राथमिकताएं अपडेट करें' : 'Update Preferences',
      subtitle: i18n.language === 'hi' ? 'व्यक्तिगत सिफारिशें अपडेट करें' : 'Update personalized recommendations',
      onPress: handleRetakeQuestionnaire,
    },
    {
      icon: 'analytics-outline',
      title: i18n.language === 'hi' ? 'मुद्रा इतिहास' : 'Posture History',
      subtitle: i18n.language === 'hi' ? 'अपने विश्लेषण देखें' : 'View your analyses',
      onPress: () => navigation.navigate('PostureHistory'),
    },
    {
      icon: 'card-outline',
      title: t('profile.subscription'),
      subtitle: user?.subscriptionTier?.toUpperCase() || 'FREE',
      onPress: () => navigation.navigate('Subscription'),
      showBadge: user?.subscriptionTier === 'free',
    },
    {
      icon: 'people-outline',
      title: t('profile.family'),
      onPress: () => navigation.navigate('Family'),
    },
  ];

  const settingsItems = [
    {
      icon: 'language-outline',
      title: t('profile.language'),
      subtitle: i18n.language === 'hi' ? 'हिंदी' : 'English',
      onPress: toggleLanguage,
    },
    {
      icon: 'notifications-outline',
      title: t('profile.notifications'),
      onPress: () => { },
    },
  ];

  const supportItems = [
    {
      icon: 'help-circle-outline',
      title: t('profile.support'),
      onPress: () => { },
    },
    {
      icon: 'document-text-outline',
      title: i18n.language === 'hi' ? 'नियम और गोपनीयता' : 'Terms & Privacy',
      onPress: () => navigation.navigate('TermsPrivacy'),
    },
    {
      icon: 'information-circle-outline',
      title: t('profile.about'),
      onPress: () => { },
    },
  ];

  // Show login prompt if user is not authenticated
  if (!user) {
    return (
      <View style={styles.container}>
        <StatusBar barStyle="light-content" backgroundColor="#000000" />

        {/* Background Gradient */}
        <LinearGradient
          colors={['#000000', '#1a1a1a', '#2d2d2d']}
          style={StyleSheet.absoluteFillObject}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        />

        <ScrollView showsVerticalScrollIndicator={false}>
          <View style={styles.authPromptContainer}>
            {/* Guest Mode Banner */}
            <TouchableOpacity
              style={styles.guestBanner}
              onPress={() => {
                // Navigate to auth screen

                navigation.navigate('Auth');
              }}
              activeOpacity={0.8}
            >
              <View style={styles.guestBannerContent}>
                <View style={styles.guestBannerIcon}>
                  <Ionicons name="person-add" size={20} color="#FFD700" />
                </View>
                <View style={styles.guestBannerText}>
                  <Text style={styles.guestBannerTitle}>
                    {i18n.language === 'hi' ? 'अपनी प्रोफाइल बनाएं' : 'Create Your Profile'}
                  </Text>
                  <Text style={styles.guestBannerSubtitle}>
                    {i18n.language === 'hi'
                      ? 'प्रगति ट्रैकिंग और व्यक्तिगत अनुभव के लिए साइन अप करें'
                      : 'Sign up for progress tracking & personalized experience'}
                  </Text>
                </View>
                <Ionicons name="arrow-forward" size={16} color="rgba(255, 255, 255, 0.8)" />
              </View>
            </TouchableOpacity>

            {/* Guest Features */}
            <View style={styles.guestFeaturesCard}>
              <Text style={styles.guestFeaturesTitle}>
                {i18n.language === 'hi' ? 'गेस्ट के रूप में उपलब्ध' : 'Available as Guest'}
              </Text>

              <View style={styles.guestFeaturesList}>
                <TouchableOpacity style={styles.guestFeatureItem} onPress={toggleLanguage}>
                  <View style={styles.guestFeatureLeft}>
                    <View style={styles.guestFeatureIcon}>
                      <Ionicons name="language-outline" size={20} color="rgba(255, 255, 255, 0.8)" />
                    </View>
                    <Text style={styles.guestFeatureText}>
                      {i18n.language === 'hi' ? 'भाषा बदलें' : 'Change Language'}
                    </Text>
                  </View>
                  <View style={styles.guestFeatureRight}>
                    <Text style={styles.guestFeatureValue}>
                      {i18n.language === 'hi' ? 'हिंदी' : 'English'}
                    </Text>
                    <Ionicons name="chevron-forward" size={16} color="rgba(255, 255, 255, 0.6)" />
                  </View>
                </TouchableOpacity>

                <TouchableOpacity style={styles.guestFeatureItem}>
                  <View style={styles.guestFeatureLeft}>
                    <View style={styles.guestFeatureIcon}>
                      <Ionicons name="help-circle-outline" size={20} color="rgba(255, 255, 255, 0.8)" />
                    </View>
                    <Text style={styles.guestFeatureText}>
                      {i18n.language === 'hi' ? 'सहायता' : 'Help & Support'}
                    </Text>
                  </View>
                  <Ionicons name="chevron-forward" size={16} color="rgba(255, 255, 255, 0.6)" />
                </TouchableOpacity>

                <TouchableOpacity style={styles.guestFeatureItem}>
                  <View style={styles.guestFeatureLeft}>
                    <View style={styles.guestFeatureIcon}>
                      <Ionicons name="information-circle-outline" size={20} color="rgba(255, 255, 255, 0.8)" />
                    </View>
                    <Text style={styles.guestFeatureText}>
                      {i18n.language === 'hi' ? 'ऐप के बारे में' : 'About App'}
                    </Text>
                  </View>
                  <Ionicons name="chevron-forward" size={16} color="rgba(255, 255, 255, 0.6)" />
                </TouchableOpacity>
              </View>
            </View>
          </View>

          <View style={styles.bottomSpacing} />
        </ScrollView>
      </View>
    );
  }

  const renderMenuItem = (item: any, index: number) => (
    <TouchableOpacity
      key={index}
      style={styles.menuItem}
      onPress={item.onPress}
      activeOpacity={0.7}
    >
      <View style={styles.menuItemLeft}>
        <View style={styles.iconContainer}>
          <Ionicons
            name={item.icon as any}
            size={20}
            color="rgba(255, 255, 255, 0.8)"
          />
        </View>
        <View style={styles.menuItemText}>
          <Text style={styles.menuItemTitle}>
            {item.title}
          </Text>
          {item.subtitle && (
            <Text style={styles.menuItemSubtitle}>{item.subtitle}</Text>
          )}
        </View>
      </View>
      <View style={styles.menuItemRight}>
        {item.showBadge && (
          <View style={styles.upgradeBadge}>
            <Text style={styles.upgradeBadgeText}>Upgrade</Text>
          </View>
        )}
        <Ionicons
          name="chevron-forward"
          size={16}
          color="rgba(255, 255, 255, 0.6)"
        />
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#000000" />

      {/* Background Gradient */}
      <LinearGradient
        colors={['#000000', '#1a1a1a', '#2d2d2d']}
        style={StyleSheet.absoluteFillObject}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      />

      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Modern Header */}
        <View style={styles.header}>
          <View style={styles.profileSection}>
            <View style={styles.avatar}>
              <Text style={styles.avatarText}>
                {user?.name?.charAt(0).toUpperCase() || 'U'}
              </Text>
            </View>
            <View style={styles.profileInfo}>
              <Text style={styles.userName}>{user?.name || 'User'}</Text>
              <Text style={styles.userEmail}>{user?.email || ''}</Text>
              <View style={styles.subscriptionContainer}>
                <View style={[
                  styles.subscriptionBadge,
                  user?.subscriptionTier === 'premium' && styles.subscriptionBadgePremium
                ]}>
                  <Ionicons
                    name={user?.subscriptionTier === 'premium' ? 'star' : 'person'}
                    size={12}
                    color={user?.subscriptionTier === 'premium' ? '#F59E0B' : Theme.colors.text.tertiary}
                  />
                  <Text style={[
                    styles.subscriptionText,
                    user?.subscriptionTier === 'premium' && styles.subscriptionTextPremium
                  ]}>
                    {user?.subscriptionTier?.toUpperCase() || 'FREE'}
                  </Text>
                </View>
              </View>
            </View>
          </View>
        </View>

        {/* Account Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Account</Text>
          <View style={styles.menuContainer}>
            {accountItems.map(renderMenuItem)}
          </View>
        </View>

        {/* Settings Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Settings</Text>
          <View style={styles.menuContainer}>
            {settingsItems.map(renderMenuItem)}
          </View>
        </View>

        {/* Support Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Support</Text>
          <View style={styles.menuContainer}>
            {supportItems.map(renderMenuItem)}
          </View>
        </View>

        {/* Logout Section */}
        <View style={styles.section}>
          <TouchableOpacity
            style={styles.logoutButton}
            onPress={handleLogout}
            activeOpacity={0.7}
          >
            <Ionicons name="log-out-outline" size={20} color={Theme.colors.error[500]} />
            <Text style={styles.logoutText}>{t('auth.logout')}</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.bottomSpacing} />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  header: {
    paddingTop: 50,
    paddingBottom: Theme.spacing['3xl'],
    paddingHorizontal: Theme.spacing.xl,
  },
  profileSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 64,
    height: 64,
    borderRadius: Theme.borderRadius.full,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Theme.spacing.lg,
  },
  avatarText: {
    fontSize: Theme.typography.fontSize['2xl'],
    fontWeight: Theme.typography.fontWeight.bold,
    color: '#FFFFFF',
  },
  profileInfo: {
    flex: 1,
  },
  userName: {
    fontSize: Theme.typography.fontSize.xl,
    fontWeight: '300',
    color: '#FFFFFF',
    marginBottom: Theme.spacing.xs,
    letterSpacing: 0.5,
  },
  userEmail: {
    fontSize: Theme.typography.fontSize.sm,
    color: 'rgba(255, 255, 255, 0.7)',
    marginBottom: Theme.spacing.sm,
  },
  subscriptionContainer: {
    alignSelf: 'flex-start',
  },
  subscriptionBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    paddingHorizontal: Theme.spacing.sm,
    paddingVertical: Theme.spacing.xs,
    borderRadius: Theme.borderRadius.lg,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  subscriptionBadgePremium: {
    backgroundColor: 'rgba(255, 215, 0, 0.2)',
    borderColor: '#FFD700',
  },
  subscriptionText: {
    fontSize: Theme.typography.fontSize.xs,
    fontWeight: Theme.typography.fontWeight.semibold,
    color: 'rgba(255, 255, 255, 0.7)',
    marginLeft: Theme.spacing.xs,
  },
  subscriptionTextPremium: {
    color: '#FFD700',
  },
  section: {
    marginBottom: Theme.spacing['2xl'],
  },
  sectionTitle: {
    fontSize: Theme.typography.fontSize.sm,
    fontWeight: Theme.typography.fontWeight.semibold,
    color: 'rgba(255, 255, 255, 0.6)',
    marginBottom: Theme.spacing.sm,
    marginHorizontal: Theme.spacing.xl,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  menuContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    marginHorizontal: Theme.spacing.xl,
    borderRadius: Theme.borderRadius['2xl'],
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    overflow: 'hidden',
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Theme.spacing.lg,
    paddingVertical: Theme.spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 32,
    height: 32,
    borderRadius: Theme.borderRadius.lg,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Theme.spacing.md,
  },
  menuItemText: {
    flex: 1,
  },
  menuItemTitle: {
    fontSize: Theme.typography.fontSize.base,
    fontWeight: Theme.typography.fontWeight.medium,
    color: '#FFFFFF',
  },
  menuItemSubtitle: {
    fontSize: Theme.typography.fontSize.sm,
    color: 'rgba(255, 255, 255, 0.6)',
    marginTop: 2,
  },
  menuItemRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  upgradeBadge: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: Theme.spacing.sm,
    paddingVertical: 2,
    borderRadius: Theme.borderRadius.sm,
    marginRight: Theme.spacing.sm,
  },
  upgradeBadgeText: {
    fontSize: Theme.typography.fontSize.xs,
    fontWeight: Theme.typography.fontWeight.semibold,
    color: '#000000',
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: Theme.spacing.xl,
    paddingVertical: Theme.spacing.lg,
    borderRadius: Theme.borderRadius['2xl'],
    borderWidth: 1,
    borderColor: 'rgba(255, 59, 48, 0.3)',
    backgroundColor: 'rgba(255, 59, 48, 0.1)',
  },
  logoutText: {
    fontSize: Theme.typography.fontSize.base,
    fontWeight: Theme.typography.fontWeight.medium,
    color: '#FF3B30',
    marginLeft: Theme.spacing.sm,
  },
  bottomSpacing: {
    height: Theme.layout.tabBar.height + Theme.spacing.xl,
  },
  authPromptContainer: {
    paddingTop: 60,
    paddingHorizontal: Theme.spacing.xl,
  },
  guestBanner: {
    backgroundColor: 'rgba(255, 215, 0, 0.1)',
    marginBottom: Theme.spacing.xl,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 215, 0, 0.3)',
    padding: 16,
  },
  guestBannerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  guestBannerIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(255, 215, 0, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  guestBannerText: {
    flex: 1,
  },
  guestBannerTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFD700',
    marginBottom: 2,
    letterSpacing: 0.2,
  },
  guestBannerSubtitle: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.8)',
    lineHeight: 16,
  },
  guestFeaturesCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: Theme.borderRadius['2xl'],
    padding: Theme.spacing.xl,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  guestFeaturesTitle: {
    fontSize: Theme.typography.fontSize.lg,
    fontWeight: Theme.typography.fontWeight.semibold,
    color: '#FFFFFF',
    marginBottom: Theme.spacing.lg,
    textAlign: 'center',
  },
  guestFeaturesList: {
    gap: Theme.spacing.xs,
  },
  guestFeatureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: Theme.spacing.md,
    paddingHorizontal: Theme.spacing.sm,
    borderRadius: Theme.borderRadius.lg,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
  },
  guestFeatureLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  guestFeatureIcon: {
    width: 32,
    height: 32,
    borderRadius: Theme.borderRadius.lg,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Theme.spacing.md,
  },
  guestFeatureText: {
    fontSize: Theme.typography.fontSize.base,
    fontWeight: Theme.typography.fontWeight.medium,
    color: '#FFFFFF',
    flex: 1,
  },
  guestFeatureRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  guestFeatureValue: {
    fontSize: Theme.typography.fontSize.sm,
    color: 'rgba(255, 255, 255, 0.6)',
    marginRight: Theme.spacing.sm,
  },
});

export default ProfileScreen;