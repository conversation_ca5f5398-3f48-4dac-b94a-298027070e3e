import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, Animated, Dimensions } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { LinearGradient } from 'expo-linear-gradient';
import { PostureAnalysis, PostureIssue } from '../types';
import { Theme } from '../constants/designTokens';

interface RealTimeFeedbackProps {
  analysis: PostureAnalysis | null;
  isRealTime: boolean;
  confidence: number;
  processingTime: number;
  frameRate: number;
}

interface ScoreIndicatorProps {
  score: number;
  size?: number;
  showText?: boolean;
}

const { width } = Dimensions.get('window');

const ScoreIndicator: React.FC<ScoreIndicatorProps> = ({ 
  score, 
  size = 80, 
  showText = true 
}) => {
  const animatedValue = new Animated.Value(0);

  useEffect(() => {
    Animated.timing(animatedValue, {
      toValue: score,
      duration: 1000,
      useNativeDriver: false,
    }).start();
  }, [score]);

  const getScoreColor = (score: number): string => {
    if (score >= 85) return '#4ADE80'; // Green - Excellent
    if (score >= 70) return '#FDE047'; // Yellow - Good
    if (score >= 50) return '#FB923C'; // Orange - Fair
    return '#EF4444'; // Red - Poor
  };

  const getScoreLabel = (score: number): string => {
    if (score >= 85) return 'Excellent';
    if (score >= 70) return 'Good';
    if (score >= 50) return 'Fair';
    return 'Poor';
  };

  return (
    <View style={[styles.scoreContainer, { width: size, height: size }]}>
      <Animated.View
        style={[
          styles.scoreCircle,
          {
            width: size,
            height: size,
            borderRadius: size / 2,
            borderColor: getScoreColor(score),
          },
        ]}
      >
        {showText && (
          <View style={styles.scoreContent}>
            <Text style={[styles.scoreNumber, { fontSize: size * 0.25 }]}>
              {Math.round(score)}
            </Text>
            <Text style={[styles.scoreLabel, { fontSize: size * 0.12 }]}>
              {getScoreLabel(score)}
            </Text>
          </View>
        )}
      </Animated.View>
    </View>
  );
};

const RealTimeFeedback: React.FC<RealTimeFeedbackProps> = ({
  analysis,
  isRealTime,
  confidence,
  processingTime,
  frameRate,
}) => {
  const { t, i18n } = useTranslation();
  const [alertVisible, setAlertVisible] = useState(false);
  const [currentAlert, setCurrentAlert] = useState<string>('');
  const alertAnimation = new Animated.Value(0);

  // Show alert when posture issues are detected
  useEffect(() => {
    if (analysis && analysis.overallScore < 60) {
      const message = i18n.language === 'hi' 
        ? 'मुद्रा सुधार की आवश्यकता है!'
        : 'Posture needs improvement!';
      
      setCurrentAlert(message);
      setAlertVisible(true);
      
      Animated.sequence([
        Animated.timing(alertAnimation, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.delay(2000),
        Animated.timing(alertAnimation, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start(() => setAlertVisible(false));
    }
  }, [analysis?.overallScore]);

  // Get issue priority color
  const getIssuePriorityColor = (severity: 'low' | 'medium' | 'high'): string => {
    switch (severity) {
      case 'high': return '#EF4444';
      case 'medium': return '#F59E0B';
      case 'low': return '#10B981';
      default: return '#6B7280';
    }
  };

  if (!analysis) {
    return (
      <View style={styles.noAnalysisContainer}>
        <Ionicons name="body-outline" size={48} color="#6B7280" />
        <Text style={styles.noAnalysisText}>
          {t('postureCheck.noAnalysis', 'Position yourself in front of the camera')}
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Real-time Score Display */}
      <View style={styles.scoreSection}>
        <ScoreIndicator score={analysis.overallScore} size={100} />
        
        <View style={styles.scoreDetails}>
          <Text style={styles.scoreTitle}>
            {t('postureCheck.postureScore', 'Posture Score')}
          </Text>
          
          {/* Individual component scores */}
          <View style={styles.componentScores}>
            <View style={styles.componentScore}>
              <Text style={styles.componentLabel}>Neck</Text>
              <View style={[styles.componentBar, { width: `${analysis.shoulderAlignment}%` }]} />
              <Text style={styles.componentValue}>{analysis.shoulderAlignment}</Text>
            </View>
            
            <View style={styles.componentScore}>
              <Text style={styles.componentLabel}>Spine</Text>
              <View style={[styles.componentBar, { width: `${analysis.spineAlignment}%` }]} />
              <Text style={styles.componentValue}>{analysis.spineAlignment}</Text>
            </View>
            
            <View style={styles.componentScore}>
              <Text style={styles.componentLabel}>Hips</Text>
              <View style={[styles.componentBar, { width: `${analysis.hipAlignment}%` }]} />
              <Text style={styles.componentValue}>{analysis.hipAlignment}</Text>
            </View>
          </View>
        </View>
      </View>

      {/* Live Issues Detection */}
      {analysis.issues && analysis.issues.length > 0 && (
        <View style={styles.issuesSection}>
          <Text style={styles.issuesTitle}>
            {t('postureCheck.detectedIssues', 'Detected Issues')}
          </Text>
          
          {analysis.issues.slice(0, 3).map((issue, index) => (
            <View key={index} style={styles.issueItem}>
              <View style={[
                styles.issuePriority, 
                { backgroundColor: getIssuePriorityColor(issue.severity) }
              ]} />
              <Text style={styles.issueText}>
                {issue.description}
              </Text>
            </View>
          ))}
        </View>
      )}

      {/* Real-time Performance Stats */}
      {isRealTime && (
        <View style={styles.performanceSection}>
          <View style={styles.performanceItem}>
            <Ionicons name="speedometer-outline" size={16} color="#4ADE80" />
            <Text style={styles.performanceText}>{frameRate.toFixed(1)} FPS</Text>
          </View>
          
          <View style={styles.performanceItem}>
            <Ionicons name="time-outline" size={16} color="#60A5FA" />
            <Text style={styles.performanceText}>{processingTime.toFixed(1)}ms</Text>
          </View>
          
          <View style={styles.performanceItem}>
            <Ionicons name="checkmark-circle-outline" size={16} color="#F59E0B" />
            <Text style={styles.performanceText}>{(confidence * 100).toFixed(0)}%</Text>
          </View>
        </View>
      )}

      {/* Alert Overlay */}
      {alertVisible && (
        <Animated.View
          style={[
            styles.alertOverlay,
            {
              opacity: alertAnimation,
              transform: [{
                translateY: alertAnimation.interpolate({
                  inputRange: [0, 1],
                  outputRange: [-50, 0],
                }),
              }],
            },
          ]}
        >
          <LinearGradient
            colors={['#EF4444', '#DC2626']}
            style={styles.alertGradient}
          >
            <Ionicons name="warning" size={20} color="#FFFFFF" />
            <Text style={styles.alertText}>{currentAlert}</Text>
          </LinearGradient>
        </Animated.View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  noAnalysisContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
  },
  noAnalysisText: {
    color: '#9CA3AF',
    fontSize: 16,
    textAlign: 'center',
    marginTop: 12,
  },
  scoreSection: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  scoreContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  scoreCircle: {
    borderWidth: 4,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  scoreContent: {
    alignItems: 'center',
  },
  scoreNumber: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  scoreLabel: {
    color: '#D1D5DB',
    fontWeight: '500',
  },
  scoreDetails: {
    flex: 1,
    marginLeft: 20,
  },
  scoreTitle: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  componentScores: {
    gap: 8,
  },
  componentScore: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  componentLabel: {
    color: '#D1D5DB',
    fontSize: 12,
    width: 40,
  },
  componentBar: {
    height: 4,
    backgroundColor: '#4ADE80',
    borderRadius: 2,
    maxWidth: 80,
  },
  componentValue: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '500',
    width: 30,
    textAlign: 'right',
  },
  issuesSection: {
    marginBottom: 16,
  },
  issuesTitle: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  issueItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  issuePriority: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  issueText: {
    color: '#D1D5DB',
    fontSize: 14,
    flex: 1,
  },
  performanceSection: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.1)',
  },
  performanceItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  performanceText: {
    color: '#D1D5DB',
    fontSize: 12,
    fontWeight: '500',
  },
  alertOverlay: {
    position: 'absolute',
    top: -60,
    left: 20,
    right: 20,
    zIndex: 1000,
  },
  alertGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    gap: 8,
  },
  alertText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
    flex: 1,
  },
});

export default RealTimeFeedback;
