/**
 * Storage Migration Utility
 * Helps migrate data from old AsyncStorage keys to new Expo storage system
 */

import { SecureStorage, AppStorage, SECURE_KEYS, STORAGE_KEYS } from './storage';
import { logger } from './logger';

interface MigrationMapping {
  oldKey: string;
  newKey: string;
  isSecure: boolean;
}

const MIGRATION_MAPPINGS: MigrationMapping[] = [
  // Auth tokens should be in secure storage
  { oldKey: 'auth_token', newKey: SECURE_KEYS.AUTH_TOKEN, isSecure: true },
  { oldKey: 'refresh_token', newKey: SECURE_KEYS.REFRESH_TOKEN, isSecure: true },
  { old<PERSON>ey: 'expo_push_token', newKey: SECURE_KEYS.PUSH_TOKEN, isSecure: true },
  
  // App settings in regular storage
  { oldKey: 'user-language', newKey: STORAGE_KEYS.USER_LANGUAGE, isSecure: false },
  { old<PERSON><PERSON>: 'notification_settings', newKey: STORAGE_KEYS.NOTIFICATION_SETTINGS, isSecure: false },
  { old<PERSON>ey: 'app_logs', newKey: STORAGE_KEYS.APP_LOGS, isSecure: false },
  { oldKey: 'cache_metadata', newKey: STORAGE_KEYS.CACHE_METADATA, isSecure: false },
  { oldKey: 'offline_sync_queue', newKey: STORAGE_KEYS.SYNC_QUEUE, isSecure: false },
  { oldKey: 'offline_cached_data', newKey: STORAGE_KEYS.CACHED_DATA, isSecure: false },
  { oldKey: 'last_sync_timestamp', newKey: STORAGE_KEYS.LAST_SYNC, isSecure: false },
  
  // Onboarding state
  { oldKey: 'onboarding_complete', newKey: STORAGE_KEYS.ONBOARDING_COMPLETE, isSecure: false },
];

export class StorageMigration {
  private static readonly MIGRATION_VERSION_KEY = 'storage_migration_version';
  private static readonly CURRENT_VERSION = '1.0.0';

  /**
   * Check if migration is needed and perform it
   */
  static async migrateIfNeeded(): Promise<void> {
    try {
      const currentVersion = await AppStorage.getItem(this.MIGRATION_VERSION_KEY);
      
      if (currentVersion === this.CURRENT_VERSION) {
        logger.debug('Storage migration not needed', {}, 'StorageMigration');
        return;
      }

      logger.info('Starting storage migration', { from: currentVersion, to: this.CURRENT_VERSION }, 'StorageMigration');
      
      await this.performMigration();
      
      // Mark migration as complete
      await AppStorage.setItem(this.MIGRATION_VERSION_KEY, this.CURRENT_VERSION);
      
      logger.info('Storage migration completed successfully', {}, 'StorageMigration');
    } catch (error) {
      logger.error('Storage migration failed', error, 'StorageMigration');
      throw error;
    }
  }

  /**
   * Perform the actual migration
   */
  private static async performMigration(): Promise<void> {
    let migratedCount = 0;
    let errorCount = 0;

    for (const mapping of MIGRATION_MAPPINGS) {
      try {
        // Check if old key exists
        const oldValue = await AppStorage.getItem(mapping.oldKey);
        
        if (oldValue !== null) {
          // Migrate to new storage
          if (mapping.isSecure) {
            await SecureStorage.setItem(mapping.newKey, oldValue);
          } else {
            await AppStorage.setItem(mapping.newKey, oldValue);
          }
          
          // Remove old key
          await AppStorage.removeItem(mapping.oldKey);
          
          migratedCount++;
          logger.debug('Migrated storage key', { 
            from: mapping.oldKey, 
            to: mapping.newKey, 
            secure: mapping.isSecure 
          }, 'StorageMigration');
        }
      } catch (error) {
        errorCount++;
        logger.warn('Failed to migrate storage key', { 
          key: mapping.oldKey, 
          error 
        }, 'StorageMigration');
      }
    }

    logger.info('Migration summary', { 
      migrated: migratedCount, 
      errors: errorCount 
    }, 'StorageMigration');
  }

  /**
   * Clean up any remaining old storage keys
   */
  static async cleanupOldStorage(): Promise<void> {
    try {
      const allKeys = await AppStorage.getAllKeys();
      const oldKeys = MIGRATION_MAPPINGS.map(m => m.oldKey);
      const keysToRemove = allKeys.filter(key => oldKeys.includes(key));
      
      if (keysToRemove.length > 0) {
        await AppStorage.multiRemove(keysToRemove);
        logger.info('Cleaned up old storage keys', { count: keysToRemove.length }, 'StorageMigration');
      }
    } catch (error) {
      logger.warn('Failed to cleanup old storage', error, 'StorageMigration');
    }
  }

  /**
   * Reset migration (for testing purposes)
   */
  static async resetMigration(): Promise<void> {
    try {
      await AppStorage.removeItem(this.MIGRATION_VERSION_KEY);
      logger.info('Migration reset completed', {}, 'StorageMigration');
    } catch (error) {
      logger.error('Failed to reset migration', error, 'StorageMigration');
    }
  }
}