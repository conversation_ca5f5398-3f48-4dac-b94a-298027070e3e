/**
 * ML Kit Camera View Component
 * High-performance camera component with ML Kit pose detection
 * Replaces Expo Camera with Vision Camera + ML Kit integration
 */

import React, { useRef, useEffect, useState, useCallback, useMemo } from 'react';
import { StyleSheet, View, Text, Platform } from 'react-native';
import {
  Camera,
  useCameraDevices,
  useFrameProcessor,
  CameraPermissionStatus
} from 'react-native-vision-camera';
// import { useSharedValue } from 'react-native-reanimated'; // Temporarily disabled
import { logger } from '../utils/logger';
import { mlKitPoseDetectionService } from '../services/mlKitPoseDetectionService';
import { ARPoseDetection } from '../types';



interface MLKitCameraViewProps {
  onPoseDetected?: (detection: ARPoseDetection) => void;
  isActive: boolean;
  style?: any;
}

export const MLKitCameraView: React.FC<MLKitCameraViewProps> = ({
  onPoseDetected,
  isActive,
  style
}) => {
  const camera = useRef<Camera>(null);
  const devices = useCameraDevices();
  const device = devices.find(d => d.position === 'back');

  // Get the best format for pose detection
  const format = useMemo(() => {
    if (!device?.formats) return undefined;

    // Find a format that supports good resolution and frame rate for pose detection
    return device.formats.find(f =>
      f.videoWidth >= 1280 &&
      f.videoHeight >= 720 &&
      f.maxFps >= 30
    ) || device.formats[0]; // Fallback to first available format
  }, [device?.formats]);
  
  const [hasPermission, setHasPermission] = useState<boolean>(false);
  const [isInitialized, setIsInitialized] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Performance tracking - temporarily disabled
  // const frameCount = useSharedValue(0);

  // Request camera permissions
  useEffect(() => {
    const requestPermissions = async () => {
      try {
        const permission = await Camera.requestCameraPermission();
        setHasPermission(permission === 'granted');
        
        if (permission !== 'granted') {
          setError('Camera permission is required for pose detection');
          logger.warn('Camera permission denied', { permission }, 'MLKitCameraView');
        } else {
          logger.info('Camera permission granted', undefined, 'MLKitCameraView');
        }
      } catch (error) {
        setError('Failed to request camera permission');
        logger.error('Camera permission request failed', error, 'MLKitCameraView');
      }
    };

    requestPermissions();
  }, []);

  // Initialize ML Kit service
  useEffect(() => {
    const initializeMLKit = async () => {
      try {
        await mlKitPoseDetectionService.initialize();
        
        // Set callback for pose detection results
        mlKitPoseDetectionService.setResultsCallback((detection: ARPoseDetection) => {
          onPoseDetected?.(detection);
        });
        
        setIsInitialized(true);
        logger.info('ML Kit camera view initialized', undefined, 'MLKitCameraView');
        
      } catch (error) {
        setError(`ML Kit initialization failed: ${error instanceof Error ? error.message : String(error)}`);
        logger.error('ML Kit initialization failed', error, 'MLKitCameraView');
      }
    };

    if (hasPermission) {
      initializeMLKit();
    }
  }, [hasPermission, onPoseDetected]);

  // Function to process pose results on JS thread - temporarily disabled
  // const processPoseResults = useCallback((detectedPoses: any[], timestamp: number) => {
  //   if (detectedPoses && detectedPoses.length > 0) {
  //     // Convert ML Kit results and send to service
  //     mlKitPoseDetectionService.processPoseResults(detectedPoses, timestamp);
  //   } else {
  //     // No poses detected
  //     mlKitPoseDetectionService.processPoseResults([], timestamp);
  //   }
  // }, []);

  // Frame processor for pose detection - temporarily disabled to avoid worklet errors
  // TODO: Re-enable when ML Kit plugin worklet support is properly configured
  const frameProcessor = useFrameProcessor((frame) => {
    'worklet';

    // Temporarily do nothing to avoid worklet errors
    // The pose detection will be handled through periodic callbacks instead

  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      mlKitPoseDetectionService.cleanup();
    };
  }, []);

  // Handle camera initialization
  const onInitialized = useCallback(() => {
    logger.info('Camera initialized successfully', {
      device: device?.id,
      hasMLKit: isInitialized
    }, 'MLKitCameraView');
  }, [device, isInitialized]);

  // Handle camera errors
  const onError = useCallback((error: any) => {
    setError(`Camera error: ${error.message || error}`);
    logger.error('Camera error occurred', error, 'MLKitCameraView');
  }, []);

  // Show error state
  if (error) {
    return (
      <View style={[styles.container, style]}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorTitle}>Camera Error</Text>
          <Text style={styles.errorMessage}>{error}</Text>
          {Platform.OS === 'ios' && (
            <Text style={styles.errorNote}>
              Note: ML Kit pose detection is currently only available on Android.
              iOS support is coming soon.
            </Text>
          )}
        </View>
      </View>
    );
  }

  // Show loading state
  if (!hasPermission || !device || !format) {
    return (
      <View style={[styles.container, style]}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>
            {!hasPermission ? 'Requesting camera permission...' :
             !device ? 'Initializing camera...' :
             'Loading camera format...'}
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, style]}>
      <Camera
        ref={camera}
        style={styles.camera}
        device={device}
        format={format}
        isActive={isActive && hasPermission && isInitialized}
        frameProcessor={frameProcessor}
        onInitialized={onInitialized}
        onError={onError}
        pixelFormat="yuv"
      />
      
      {/* Performance overlay for development */}
      {__DEV__ && (
        <View style={styles.performanceOverlay}>
          <Text style={styles.performanceText}>
            ML Kit Pose Detection
          </Text>
          <Text style={styles.performanceText}>
            Platform: {Platform.OS}
          </Text>
          <Text style={styles.performanceText}>
            Status: {isInitialized ? 'Active' : 'Initializing'}
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  camera: {
    flex: 1,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#1a1a1a',
  },
  errorTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#ff4444',
    marginBottom: 16,
    textAlign: 'center',
  },
  errorMessage: {
    fontSize: 16,
    color: '#ffffff',
    textAlign: 'center',
    marginBottom: 16,
    lineHeight: 24,
  },
  errorNote: {
    fontSize: 14,
    color: '#cccccc',
    textAlign: 'center',
    fontStyle: 'italic',
    lineHeight: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#1a1a1a',
  },
  loadingText: {
    fontSize: 18,
    color: '#ffffff',
    textAlign: 'center',
  },
  performanceOverlay: {
    position: 'absolute',
    top: 50,
    left: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    padding: 12,
    borderRadius: 8,
  },
  performanceText: {
    color: '#ffffff',
    fontSize: 12,
    fontFamily: 'monospace',
    marginBottom: 4,
  },
});

export default MLKitCameraView;
