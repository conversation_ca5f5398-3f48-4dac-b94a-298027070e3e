/**
 * Feature Flags System for PostureApp
 * Enables gradual rollout of new features and A/B testing
 */

import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { logger } from './logger';

export interface FeatureFlags {
  // Pose detection features
  enableMLKitPoseDetection: boolean;
  enableMediaPipePoseDetection: boolean;
  enableRealTimePoseAnalysis: boolean;
  
  // Performance features
  enablePerformanceOptimizations: boolean;
  enableBatteryOptimizations: boolean;
  enableAdvancedLogging: boolean;
  
  // UI/UX features
  enable3DVisualization: boolean;
  enableAROverlay: boolean;
  enableHapticFeedback: boolean;
  
  // Analytics and monitoring
  enableAnalytics: boolean;
  enableCrashReporting: boolean;
  enablePerformanceMonitoring: boolean;
}

// Default feature flags based on platform and environment
const getDefaultFeatureFlags = (): FeatureFlags => {
  const isProduction = !__DEV__;
  const isAndroid = Platform.OS === 'android';
  const isWeb = Platform.OS === 'web';

  return {
    // Pose detection - only enable where supported
    enableMLKitPoseDetection: isAndroid, // Only Android supports ML Kit currently
    enableMediaPipePoseDetection: isWeb, // Only web supports MediaPipe
    enableRealTimePoseAnalysis: isAndroid || isWeb, // Only where pose detection works
    
    // Performance features - always enabled in production
    enablePerformanceOptimizations: isProduction,
    enableBatteryOptimizations: isProduction && !isWeb,
    enableAdvancedLogging: !isProduction,
    
    // UI/UX features - platform dependent
    enable3DVisualization: true,
    enableAROverlay: isAndroid || isWeb,
    enableHapticFeedback: !isWeb,
    
    // Analytics - production only
    enableAnalytics: isProduction,
    enableCrashReporting: isProduction,
    enablePerformanceMonitoring: true,
  };
};

class FeatureFlagManager {
  private flags: FeatureFlags;
  private initialized = false;
  private readonly STORAGE_KEY = '@PostureApp:FeatureFlags';

  constructor() {
    this.flags = getDefaultFeatureFlags();
  }

  // Initialize feature flags from storage
  async initialize(): Promise<void> {
    try {
      const storedFlags = await AsyncStorage.getItem(this.STORAGE_KEY);
      if (storedFlags) {
        const parsedFlags = JSON.parse(storedFlags);
        this.flags = { ...this.flags, ...parsedFlags };
      }
      
      this.initialized = true;
      
      logger.info('Feature flags initialized', {
        platform: Platform.OS,
        flags: this.flags
      }, 'FeatureFlagManager');
      
    } catch (error) {
      logger.error('Failed to initialize feature flags', error, 'FeatureFlagManager');
      // Continue with default flags
      this.initialized = true;
    }
  }

  // Get a specific feature flag
  isEnabled(flag: keyof FeatureFlags): boolean {
    if (!this.initialized) {
      logger.warn('Feature flags not initialized, using default', { flag }, 'FeatureFlagManager');
      return this.flags[flag];
    }
    
    return this.flags[flag];
  }

  // Get all feature flags
  getAllFlags(): FeatureFlags {
    return { ...this.flags };
  }

  // Update a feature flag (for testing/debugging)
  async setFlag(flag: keyof FeatureFlags, value: boolean): Promise<void> {
    try {
      this.flags[flag] = value;
      await AsyncStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.flags));
      
      logger.info('Feature flag updated', { flag, value }, 'FeatureFlagManager');
      
    } catch (error) {
      logger.error('Failed to update feature flag', error, 'FeatureFlagManager');
    }
  }

  // Reset to default flags
  async resetToDefaults(): Promise<void> {
    try {
      this.flags = getDefaultFeatureFlags();
      await AsyncStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.flags));
      
      logger.info('Feature flags reset to defaults', this.flags, 'FeatureFlagManager');
      
    } catch (error) {
      logger.error('Failed to reset feature flags', error, 'FeatureFlagManager');
    }
  }

  // Check if pose detection is available on current platform
  isPoseDetectionAvailable(): boolean {
    return this.isEnabled('enableMLKitPoseDetection') || 
           this.isEnabled('enableMediaPipePoseDetection');
  }

  // Get pose detection method for current platform
  getPoseDetectionMethod(): 'mlkit' | 'mediapipe' | 'none' {
    if (this.isEnabled('enableMLKitPoseDetection') && Platform.OS === 'android') {
      return 'mlkit';
    }
    if (this.isEnabled('enableMediaPipePoseDetection') && Platform.OS === 'web') {
      return 'mediapipe';
    }
    return 'none';
  }

  // Check if real-time analysis should be enabled
  shouldEnableRealTimeAnalysis(): boolean {
    return this.isEnabled('enableRealTimePoseAnalysis') && this.isPoseDetectionAvailable();
  }

  // Get user-friendly message about pose detection availability
  getPoseDetectionStatusMessage(): string {
    const method = this.getPoseDetectionMethod();
    
    switch (method) {
      case 'mlkit':
        return 'Real-time pose detection enabled using ML Kit';
      case 'mediapipe':
        return 'Real-time pose detection enabled using MediaPipe';
      case 'none':
        if (Platform.OS === 'ios') {
          return 'Pose detection is not yet available on iOS. Coming soon!';
        } else if (Platform.OS === 'android') {
          return 'ML Kit pose detection is disabled. Enable it in settings for real-time analysis.';
        } else if (Platform.OS === 'web') {
          return 'MediaPipe pose detection is disabled. Enable it in settings for real-time analysis.';
        } else {
          return 'Pose detection is not available on this platform.';
        }
      default:
        return 'Pose detection status unknown.';
    }
  }
}

// Export singleton instance
export const featureFlags = new FeatureFlagManager();

// Convenience functions
export const isFeatureEnabled = (flag: keyof FeatureFlags): boolean => {
  return featureFlags.isEnabled(flag);
};

export const isPoseDetectionAvailable = (): boolean => {
  return featureFlags.isPoseDetectionAvailable();
};

export const getPoseDetectionMethod = (): 'mlkit' | 'mediapipe' | 'none' => {
  return featureFlags.getPoseDetectionMethod();
};

export const shouldEnableRealTimeAnalysis = (): boolean => {
  return featureFlags.shouldEnableRealTimeAnalysis();
};

// Initialize feature flags on app start
export const initializeFeatureFlags = async (): Promise<void> => {
  await featureFlags.initialize();
};
