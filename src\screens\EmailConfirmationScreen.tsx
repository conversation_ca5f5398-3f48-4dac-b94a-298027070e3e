import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { AuthService } from '../services/authService';
import { AuthUtils } from '../utils/authUtils';
import { useAuth } from '../contexts/AuthContext';
import { logger } from '../utils/logger';

export const EmailConfirmationScreen: React.FC = () => {
  const navigation = useNavigation();
  const { user } = useAuth();
  const [isResending, setIsResending] = useState(false);
  const [isChecking, setIsChecking] = useState(false);
  const [resendCooldown, setResendCooldown] = useState(0);

  useEffect(() => {
    // Check confirmation status periodically
    const checkInterval = setInterval(async () => {
      try {
        const status = await AuthUtils.getEmailConfirmationStatus();
        if (status.isConfirmed) {
          Alert.alert(
            'Email Confirmed! 🎉',
            'Your email has been confirmed. You can now continue.',
            [
              {
                text: 'Continue',
                onPress: () => navigation.navigate('Questionnaire' as never, { type: 'onboarding' } as never),
              },
            ]
          );
          clearInterval(checkInterval);
        }
      } catch (error) {
        logger.error('Error checking email confirmation status', error, 'EmailConfirmationScreen');
      }
    }, 3000); // Check every 3 seconds

    return () => clearInterval(checkInterval);
  }, [navigation]);

  useEffect(() => {
    // Cooldown timer
    if (resendCooldown > 0) {
      const timer = setTimeout(() => {
        setResendCooldown(resendCooldown - 1);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [resendCooldown]);

  const handleResendEmail = async () => {
    if (resendCooldown > 0) return;

    try {
      setIsResending(true);
      await AuthService.resendEmailVerification();
      setResendCooldown(60); // 60 second cooldown
      Alert.alert(
        'Email Sent! 📧',
        'We\'ve sent another confirmation email. Please check your inbox and spam folder.'
      );
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to resend email');
    } finally {
      setIsResending(false);
    }
  };

  const handleCheckStatus = async () => {
    try {
      setIsChecking(true);
      const status = await AuthUtils.getEmailConfirmationStatus();
      
      if (status.isConfirmed) {
        Alert.alert(
          'Email Confirmed! 🎉',
          'Your email has been confirmed. You can now continue.',
          [
            {
              text: 'Continue',
              onPress: () => navigation.navigate('Questionnaire' as never, { type: 'onboarding' } as never),
            },
          ]
        );
      } else {
        Alert.alert(
          'Not Confirmed Yet',
          'Your email hasn\'t been confirmed yet. Please check your inbox and click the confirmation link.'
        );
      }
    } catch (error: any) {
      Alert.alert('Error', 'Failed to check confirmation status');
    } finally {
      setIsChecking(false);
    }
  };

  const handleSkipForNow = () => {
    Alert.alert(
      'Skip Email Confirmation?',
      'You can continue without confirming your email, but some features may be limited. You can confirm your email later in settings.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Skip for Now',
          style: 'destructive',
          onPress: () => navigation.navigate('Questionnaire' as never, { type: 'onboarding' } as never),
        },
      ]
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <View style={styles.iconContainer}>
          <Ionicons name="mail-outline" size={80} color="#4A90E2" />
        </View>

        <Text style={styles.title}>Check Your Email</Text>
        <Text style={styles.subtitle}>
          We've sent a confirmation link to:
        </Text>
        <Text style={styles.email}>{user?.email}</Text>

        <Text style={styles.description}>
          Click the link in the email to confirm your account and continue setting up your profile.
        </Text>

        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={[styles.button, styles.primaryButton]}
            onPress={handleCheckStatus}
            disabled={isChecking}
          >
            {isChecking ? (
              <ActivityIndicator color="white" />
            ) : (
              <>
                <Ionicons name="checkmark-circle-outline" size={20} color="white" />
                <Text style={styles.buttonText}>I've Confirmed</Text>
              </>
            )}
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.button, styles.secondaryButton, resendCooldown > 0 && styles.disabledButton]}
            onPress={handleResendEmail}
            disabled={isResending || resendCooldown > 0}
          >
            {isResending ? (
              <ActivityIndicator color="#4A90E2" />
            ) : (
              <>
                <Ionicons name="refresh-outline" size={20} color="#4A90E2" />
                <Text style={styles.secondaryButtonText}>
                  {resendCooldown > 0 ? `Resend in ${resendCooldown}s` : 'Resend Email'}
                </Text>
              </>
            )}
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.button, styles.tertiaryButton]}
            onPress={handleSkipForNow}
          >
            <Text style={styles.tertiaryButtonText}>Skip for Now</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.helpContainer}>
          <Text style={styles.helpText}>
            Didn't receive the email? Check your spam folder or try resending.
          </Text>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  content: {
    flex: 1,
    padding: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  iconContainer: {
    marginBottom: 32,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1a1a1a',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 8,
    textAlign: 'center',
  },
  email: {
    fontSize: 16,
    fontWeight: '600',
    color: '#4A90E2',
    marginBottom: 24,
    textAlign: 'center',
  },
  description: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 40,
    paddingHorizontal: 16,
  },
  buttonContainer: {
    width: '100%',
    gap: 12,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    gap: 8,
  },
  primaryButton: {
    backgroundColor: '#4A90E2',
  },
  secondaryButton: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#4A90E2',
  },
  tertiaryButton: {
    backgroundColor: 'transparent',
  },
  disabledButton: {
    opacity: 0.5,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButtonText: {
    color: '#4A90E2',
    fontSize: 16,
    fontWeight: '600',
  },
  tertiaryButtonText: {
    color: '#666',
    fontSize: 14,
    fontWeight: '500',
  },
  helpContainer: {
    marginTop: 32,
    paddingHorizontal: 16,
  },
  helpText: {
    fontSize: 12,
    color: '#999',
    textAlign: 'center',
    lineHeight: 16,
  },
});
