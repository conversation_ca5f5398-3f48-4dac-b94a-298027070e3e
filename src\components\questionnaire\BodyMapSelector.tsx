import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Svg, { Path, Circle, Text as SvgText } from 'react-native-svg';
import { Theme } from '../../constants/designTokens';

const { width: screenWidth } = Dimensions.get('window');

interface BodyArea {
  id: string;
  label: string;
  labelHindi: string;
  coordinates: { x: number; y: number };
}

interface BodyMapSelectorProps {
  bodyAreas: BodyArea[];
  selectedAreas: string[];
  onSelectionChange: (selectedAreas: string[]) => void;
  language: string;
}

const BodyMapSelector: React.FC<BodyMapSelectorProps> = ({
  bodyAreas,
  selectedAreas,
  onSelectionChange,
  language,
}) => {
  const [animatedValues] = useState(
    bodyAreas.reduce((acc, area) => {
      acc[area.id] = new Animated.Value(0);
      return acc;
    }, {} as { [key: string]: Animated.Value })
  );

  const handleAreaPress = (areaId: string) => {
    const isSelected = selectedAreas.includes(areaId);
    let newSelection: string[];

    if (isSelected) {
      newSelection = selectedAreas.filter(id => id !== areaId);
      // Animate out
      Animated.timing(animatedValues[areaId], {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }).start();
    } else {
      newSelection = [...selectedAreas, areaId];
      // Animate in
      Animated.timing(animatedValues[areaId], {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }).start();
    }

    onSelectionChange(newSelection);
  };

  const renderBodyDiagram = () => {
    const svgWidth = screenWidth * 0.6;
    const svgHeight = svgWidth * 1.5;

    return (
      <View style={styles.bodyDiagramContainer}>
        <Svg width={svgWidth} height={svgHeight} viewBox="0 0 100 150">
          {/* Body outline */}
          <Path
            d="M50 10 C45 10 40 15 40 20 L40 30 C35 30 30 35 30 40 L30 80 C30 85 35 90 40 90 L40 120 C40 125 45 130 50 130 C55 130 60 125 60 120 L60 90 C65 90 70 85 70 80 L70 40 C70 35 65 30 60 30 L60 20 C60 15 55 10 50 10 Z"
            fill="none"
            stroke={Theme.colors.neutral[300]}
            strokeWidth="2"
          />

          {/* Interactive body areas */}
          {bodyAreas.map((area) => {
            const isSelected = selectedAreas.includes(area.id);
            return (
              <Circle
                key={area.id}
                cx={area.coordinates.x}
                cy={area.coordinates.y}
                r="8"
                fill={isSelected ? Theme.colors.primary[500] : Theme.colors.neutral[200]}
                stroke={isSelected ? Theme.colors.primary[600] : Theme.colors.neutral[400]}
                strokeWidth="2"
                onPress={() => handleAreaPress(area.id)}
              />
            );
          })}
        </Svg>

        {/* Selected areas list */}
        <View style={styles.selectedAreasContainer}>
          <Text style={styles.selectedAreasTitle}>
            {language === 'hi' ? 'चयनित क्षेत्र:' : 'Selected Areas:'}
          </Text>
          {selectedAreas.length === 0 ? (
            <Text style={styles.noSelectionText}>
              {language === 'hi' ? 'कोई क्षेत्र चयनित नहीं' : 'No areas selected'}
            </Text>
          ) : (
            <View style={styles.selectedAreasList}>
              {selectedAreas.map((areaId) => {
                const area = bodyAreas.find(a => a.id === areaId);
                if (!area) return null;

                return (
                  <Animated.View
                    key={areaId}
                    style={[
                      styles.selectedAreaChip,
                      {
                        opacity: animatedValues[areaId],
                        transform: [{
                          scale: animatedValues[areaId].interpolate({
                            inputRange: [0, 1],
                            outputRange: [0.8, 1],
                          }),
                        }],
                      },
                    ]}
                  >
                    <Text style={styles.selectedAreaText}>
                      {language === 'hi' ? area.labelHindi : area.label}
                    </Text>
                    <TouchableOpacity
                      onPress={() => handleAreaPress(areaId)}
                      style={styles.removeAreaButton}
                    >
                      <Ionicons name="close" size={16} color={Theme.colors.neutral[600]} />
                    </TouchableOpacity>
                  </Animated.View>
                );
              })}
            </View>
          )}
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <Text style={styles.instructionText}>
        {language === 'hi' 
          ? 'दर्द या असुविधा वाले क्षेत्रों पर टैप करें'
          : 'Tap on areas where you experience pain or discomfort'
        }
      </Text>
      {renderBodyDiagram()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 20,
  },
  instructionText: {
    fontSize: 16,
    color: Theme.colors.text.secondary,
    textAlign: 'center',
    marginBottom: 20,
    paddingHorizontal: 20,
  },
  bodyDiagramContainer: {
    alignItems: 'center',
    backgroundColor: Theme.colors.neutral[50],
    borderRadius: 16,
    padding: 20,
    marginHorizontal: 20,
  },
  selectedAreasContainer: {
    marginTop: 20,
    width: '100%',
  },
  selectedAreasTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Theme.colors.text.primary,
    marginBottom: 10,
  },
  noSelectionText: {
    fontSize: 14,
    color: Theme.colors.text.tertiary,
    fontStyle: 'italic',
  },
  selectedAreasList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  selectedAreaChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Theme.colors.primary[100],
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginRight: 8,
    marginBottom: 8,
  },
  selectedAreaText: {
    fontSize: 14,
    color: Theme.colors.primary[700],
    marginRight: 6,
  },
  removeAreaButton: {
    padding: 2,
  },
});

export default BodyMapSelector;
