# Supabase Setup Guide

## 1. Create Supabase Project

1. Go to [supabase.com](https://supabase.com) and create an account
2. Click "New Project"
3. Choose your organization and enter project details:
   - Name: `PostureApp` (or your preferred name)
   - Database Password: Generate a strong password
   - Region: Choose closest to your users
4. Wait for the project to be created (2-3 minutes)

## 2. Get Your Project Credentials

1. In your Supabase dashboard, go to **Settings** → **API**
2. Copy the following values:
   - **Project URL** (looks like: `https://xxxxx.supabase.co`)
   - **Anon/Public Key** (starts with `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`)

## 3. Update Environment Variables

Update your `.env` file with your Supabase credentials:

```env
EXPO_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here
```

## 4. Set Up Database Schema

1. In your Supabase dashboard, go to **SQL Editor**
2. Copy the contents of `supabase-schema.sql` and paste it into the editor
3. Click **Run** to execute the SQL

This will create:
- `profiles` table for user data
- `posture_sessions` table for session tracking
- Row Level Security policies
- Storage buckets for avatars and session data
- Automatic profile creation on user signup

## 5. Configure Authentication

1. Go to **Authentication** → **Settings**
2. Configure your auth settings:
   - **Site URL**: Your app's URL (for development: `exp://localhost:19000`)
   - **Redirect URLs**: Add your app's redirect URLs
3. Enable the auth providers you want (Email is enabled by default)

## 6. Test Your Setup

You can test the integration by importing and using the services:

```typescript
import { supabase } from './src/lib/supabase';
import { AuthService } from './src/lib/auth';
import { useAuth } from './src/hooks/useAuth';

// In your component
const { user, signIn, signUp, signOut, isAuthenticated } = useAuth();
```

## 7. Migration from Firebase (Optional)

If you have existing Firebase data, you can:

1. Export your Firestore data
2. Transform it to match the new schema
3. Import it using Supabase's bulk insert APIs

## 8. Remove Firebase Dependencies (Optional)

Once you've migrated, you can remove Firebase packages:

```bash
npm uninstall @react-native-firebase/app @react-native-firebase/auth @react-native-firebase/firestore firebase
```

## Key Benefits You'll Get

✅ **Free tier includes storage** - No upgrade required for file uploads
✅ **PostgreSQL database** - More powerful than Firestore
✅ **Real-time subscriptions** - Live updates to your data
✅ **Row Level Security** - Built-in data protection
✅ **Auto-generated APIs** - REST and GraphQL endpoints
✅ **Better TypeScript support** - Full type safety

## Next Steps

1. Update your existing Firebase auth components to use the new `useAuth` hook
2. Replace Firestore queries with the new `DatabaseService` methods
3. Update file uploads to use `StorageService`
4. Test authentication and data operations

Need help with the migration? Let me know which components you'd like me to help update first!