import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Animated,
  Dimensions,
  StyleSheet,
  Modal,
  TouchableWithoutFeedback,
  Platform,
  AccessibilityInfo,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { BlurView } from 'expo-blur';
import { LinearGradient } from 'expo-linear-gradient';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');
const isTablet = screenWidth > 768;
const isSmallScreen = screenWidth < 375;

export interface AlertButton {
  text: string;
  onPress?: () => void;
  style?: 'default' | 'cancel' | 'destructive';
}

export interface CustomAlertProps {
  visible: boolean;
  title?: string;
  message: string;
  buttons?: AlertButton[];
  type?: 'info' | 'success' | 'warning' | 'error';
  onDismiss?: () => void;
  backdropDismiss?: boolean;
  autoHide?: boolean;
  autoHideDelay?: number;
  userFriendly?: boolean; // New prop to enable user-friendly messaging
}

// User-friendly message and button text mappings
const getUserFriendlyContent = (title?: string, message?: string, buttons?: AlertButton[], type?: string) => {
  // Common user-friendly titles
  const friendlyTitles: { [key: string]: string } = {
    'questionnaire.exitConfirm': '🤔 Want to take a break?',
    'auth.logout': '👋 Ready to sign out?',
    'error': '😕 Oops, something went wrong',
    'warning': '⚠️ Just a heads up',
    'success': '🎉 Great job!',
    'info': '💡 Quick note',
  };

  // Common user-friendly messages
  const friendlyMessages: { [key: string]: string } = {
    'Your progress will be saved. You can continue later.': "Don't worry! We'll save your answers so you can pick up right where you left off whenever you're ready.",
    'Are you sure you want to logout?': "You'll need to sign in again next time. Is that okay?",
    'questionnaire.exitMessage': "No worries! We'll keep your answers safe. You can come back anytime to continue.",
    'Connection failed': "Hmm, looks like there's a connection issue. Let's try again!",
    'Error occurred': "Oops! Something didn't work as expected. Let's give it another try.",
    'Permission denied': "We need your permission to continue. Would you like to allow this?",
    'Feature not available': "This feature isn't ready yet, but we're working on it!",
    'Session expired': "Your session has timed out. Please sign in again to continue.",
    'Invalid input': "Something doesn't look quite right. Could you check and try again?",
    'Network error': "Having trouble connecting. Please check your internet and try again.",
    'Server error': "Our servers are having a moment. Please try again in a few seconds.",
  };

  // Common user-friendly button texts
  const friendlyButtons: { [key: string]: string } = {
    'questionnaire.saveAndExit': '💾 Save & Exit',
    'common.cancel': '❌ Stay Here',
    'common.yes': '✅ Yes, Continue',
    'auth.logout': '👋 Sign Out',
    'OK': '👍 Got it',
    'Cancel': '❌ Cancel',
    'Delete': '🗑️ Delete',
    'Save': '💾 Save',
    'Continue': '➡️ Continue',
    'Retry': '🔄 Try Again',
    'Close': '❌ Close',
    'Confirm': '✅ Confirm',
    'Allow': '✅ Allow',
    'Deny': '❌ Not Now',
    'Settings': '⚙️ Settings',
    'Later': '⏰ Maybe Later',
    'Skip': '⏭️ Skip',
    'Done': '✅ Done',
    'Next': '➡️ Next',
    'Back': '⬅️ Back',
  };

  const friendlyTitle = title && friendlyTitles[title] ? friendlyTitles[title] :
                      type && friendlyTitles[type] ? friendlyTitles[type] : title;

  const friendlyMessage = message && friendlyMessages[message] ? friendlyMessages[message] : message;

  const friendlyButtonsArray = buttons?.map(button => ({
    ...button,
    text: friendlyButtons[button.text] || button.text
  }));

  return {
    title: friendlyTitle,
    message: friendlyMessage,
    buttons: friendlyButtonsArray
  };
};

const CustomAlert: React.FC<CustomAlertProps> = ({
  visible,
  title,
  message,
  buttons = [{ text: 'OK' }],
  type = 'info',
  onDismiss,
  backdropDismiss = true,
  autoHide = false,
  autoHideDelay = 3000,
  userFriendly = true, // Default to user-friendly
}) => {
  // Apply user-friendly content if enabled
  const friendlyContent = userFriendly ? getUserFriendlyContent(title, message, buttons, type) : { title, message, buttons };
  const displayTitle = friendlyContent.title;
  const displayMessage = friendlyContent.message;
  const displayButtons = friendlyContent.buttons || buttons;

  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;

  useEffect(() => {
    if (visible) {
      // Announce to screen readers
      AccessibilityInfo.announceForAccessibility(
        `Alert: ${displayTitle ? `${displayTitle}. ` : ''}${displayMessage}`
      );

      // Show animation
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 250,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 100,
          friction: 8,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 250,
          useNativeDriver: true,
        }),
      ]).start();

      // Auto hide if enabled
      if (autoHide) {
        const timer = setTimeout(() => {
          handleDismiss();
        }, autoHideDelay);
        return () => clearTimeout(timer);
      }
    } else {
      // Hide animation
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 0.8,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 50,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible, autoHide, autoHideDelay, displayTitle, displayMessage]);

  const handleDismiss = () => {
    if (onDismiss) {
      onDismiss();
    }
  };

  const handleBackdropPress = () => {
    if (backdropDismiss) {
      handleDismiss();
    }
  };

  const getAlertConfig = () => {
    switch (type) {
      case 'success':
        return {
          icon: 'checkmark-circle' as const,
          iconColor: '#10B981',
          iconBgColor: 'rgba(16, 185, 129, 0.15)',
          accentColor: '#10B981',
        };
      case 'warning':
        return {
          icon: 'warning' as const,
          iconColor: '#F59E0B',
          iconBgColor: 'rgba(245, 158, 11, 0.15)',
          accentColor: '#F59E0B',
        };
      case 'error':
        return {
          icon: 'close-circle' as const,
          iconColor: '#EF4444',
          iconBgColor: 'rgba(239, 68, 68, 0.15)',
          accentColor: '#EF4444',
        };
      default:
        return {
          icon: 'information-circle' as const,
          iconColor: '#3B82F6',
          iconBgColor: 'rgba(59, 130, 246, 0.15)',
          accentColor: '#3B82F6',
        };
    }
  };

  const config = getAlertConfig();

  const renderButton = (button: AlertButton, index: number) => {
    const isDestructive = button.style === 'destructive';
    const isCancel = button.style === 'cancel';
    const isPrimary = !isCancel && !isDestructive;
    const isLast = index === displayButtons.length - 1;

    return (
      <TouchableOpacity
        key={index}
        style={[
          styles.buttonWrapper,
          !isLast && displayButtons.length > 1 && styles.buttonMargin,
        ]}
        onPress={() => {
          button.onPress?.();
          handleDismiss();
        }}
        activeOpacity={0.8}
        accessible={true}
        accessibilityRole="button"
        accessibilityLabel={button.text}
        accessibilityHint={
          isDestructive ? 'Destructive action' :
          isCancel ? 'Cancel action' :
          'Confirm action'
        }
      >
        {isPrimary ? (
          <LinearGradient
            colors={['#6366F1', '#8B5CF6']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={[styles.button, styles.primaryButton]}
          >
            <Text
              style={[
                styles.buttonText,
                styles.primaryButtonText,
                isSmallScreen && styles.smallScreenButtonText,
              ]}
              accessible={false}
            >
              {button.text}
            </Text>
          </LinearGradient>
        ) : (
          <View
            style={[
              styles.button,
              isDestructive && styles.destructiveButton,
              isCancel && styles.cancelButton,
            ]}
          >
            <Text
              style={[
                styles.buttonText,
                isDestructive && styles.destructiveButtonText,
                isCancel && styles.cancelButtonText,
                isSmallScreen && styles.smallScreenButtonText,
              ]}
              accessible={false}
            >
              {button.text}
            </Text>
          </View>
        )}
      </TouchableOpacity>
    );
  };

  if (!visible) return null;

  return (
    <Modal
      transparent
      visible={visible}
      animationType="none"
      statusBarTranslucent
      onRequestClose={handleDismiss}
    >
      <TouchableWithoutFeedback onPress={handleBackdropPress}>
        <View style={styles.overlay}>
          <Animated.View
            style={[
              styles.backdrop,
              {
                opacity: fadeAnim,
              },
            ]}
          />
          
          <TouchableWithoutFeedback>
            <Animated.View
              style={[
                styles.alertContainer,
                {
                  opacity: fadeAnim,
                  transform: [
                    { scale: scaleAnim },
                    { translateY: slideAnim },
                  ],
                },
              ]}
            >
              <View
                style={[
                  styles.alertContent,
                  isTablet && styles.tabletAlert,
                  isSmallScreen && styles.smallScreenAlert,
                ]}
                accessible={true}
                accessibilityRole="alert"
                accessibilityLabel={`Alert: ${displayTitle ? `${displayTitle}. ` : ''}${displayMessage}`}
              >
                {/* Icon Header */}
                <View style={styles.iconContainer}>
                  <View style={[styles.iconBackground, { backgroundColor: config.iconBgColor }]}>
                    <Ionicons
                      name={config.icon}
                      size={28}
                      color={config.iconColor}
                      accessible={false}
                    />
                  </View>
                </View>

                {/* Content */}
                <View style={styles.contentContainer}>
                  {displayTitle && (
                    <Text
                      style={[
                        styles.title,
                        isSmallScreen && styles.smallScreenTitle,
                      ]}
                      accessible={false}
                    >
                      {displayTitle}
                    </Text>
                  )}

                  <Text
                    style={[
                      styles.message,
                      isSmallScreen && styles.smallScreenMessage,
                    ]}
                    accessible={false}
                  >
                    {displayMessage}
                  </Text>
                </View>

                {/* Buttons */}
                <View style={[
                  styles.buttonsContainer,
                  displayButtons.length === 1 && styles.singleButtonContainer,
                ]}>
                  {displayButtons.map((button, index) => renderButton(button, index))}
                </View>
              </View>

            </Animated.View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  backdrop: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
  },
  alertContainer: {
    width: '100%',
    maxWidth: 340,
  },
  alertContent: {
    backgroundColor: '#1F2937',
    borderRadius: 20,
    padding: 0,
    borderWidth: 1,
    borderColor: '#374151',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 20,
    },
    shadowOpacity: 0.25,
    shadowRadius: 25,
    elevation: 20,
    overflow: 'hidden',
  },
  iconContainer: {
    alignItems: 'center',
    paddingTop: 32,
    paddingBottom: 16,
  },
  iconBackground: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
  },
  contentContainer: {
    paddingHorizontal: 24,
    paddingBottom: 24,
  },
  title: {
    fontSize: 20,
    fontWeight: '700',
    color: '#F9FAFB',
    textAlign: 'center',
    marginBottom: 8,
    lineHeight: 28,
  },
  message: {
    fontSize: 16,
    lineHeight: 24,
    color: '#D1D5DB',
    textAlign: 'center',
    marginBottom: 0,
  },
  buttonsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 24,
    paddingBottom: 24,
    gap: 12,
  },
  buttonWrapper: {
    flex: 1,
  },
  buttonMargin: {
    marginRight: 0, // Using gap instead
  },
  button: {
    paddingVertical: 16,
    paddingHorizontal: 20,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 12,
    minHeight: 52,
  },
  primaryButton: {
    // Gradient applied via LinearGradient component
  },
  cancelButton: {
    backgroundColor: '#374151',
    borderWidth: 1,
    borderColor: '#4B5563',
  },
  destructiveButton: {
    backgroundColor: '#DC2626',
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  primaryButtonText: {
    color: '#FFFFFF',
  },
  cancelButtonText: {
    color: '#D1D5DB',
  },
  destructiveButtonText: {
    color: '#FFFFFF',
  },
  // Responsive styles
  tabletAlert: {
    maxWidth: 400,
  },
  smallScreenAlert: {
    marginHorizontal: 16,
  },
  smallScreenTitle: {
    fontSize: 18,
  },
  smallScreenMessage: {
    fontSize: 15,
    lineHeight: 22,
  },
  singleButtonContainer: {
    flexDirection: 'column',
  },
  smallScreenButtonText: {
    fontSize: 15,
  },
});

export default CustomAlert;
