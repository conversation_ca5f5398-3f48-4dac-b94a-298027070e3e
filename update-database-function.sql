-- Update the create_questionnaire_session function to fix column name issue
-- Run this in your Supabase SQL Editor

-- Drop the existing function first
DROP FUNCTION IF EXISTS create_questionnaire_session(uuid, text);

-- Recreate the function with correct column names
CREATE OR REPLACE FUNCTION create_questionnaire_session(
    session_id uuid,
    session_type text DEFAULT 'onboarding'
)
RETURNS uuid
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    current_user_id uuid;
    new_session_id uuid;
BEGIN
    -- Get current authenticated user
    current_user_id := auth.uid();
    
    -- Check if user is authenticated
    IF current_user_id IS NULL THEN
        RAISE EXCEPTION 'User not authenticated';
    END IF;
    
    -- Insert the session with correct column names
    INSERT INTO public.questionnaire_sessions (
        id,
        user_id,
        session_type,
        started_at,
        completion_percentage,
        created_at,
        updated_at
    ) VALUES (
        session_id,
        current_user_id,
        session_type,
        NOW(),
        0,
        NOW(),
        NOW()
    ) RETURNING id INTO new_session_id;
    
    RETURN new_session_id;
END;
$$;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION create_questionnaire_session(uuid, text) TO authenticated;

-- Test the function (optional - you can run this to verify it works)
-- SELECT create_questionnaire_session(gen_random_uuid(), 'onboarding');
