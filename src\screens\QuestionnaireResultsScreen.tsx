import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  Animated,
  Share,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useNavigation, useRoute } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { LinearGradient } from 'expo-linear-gradient';
import { useAuth } from '../contexts/AuthContext';
import {
  RootStackParamList,
  UserInsight,
  PersonalizedRecommendation,
  InsightCategory,
  RecommendationType,
} from '../types';
import { Theme } from '../constants/designTokens';
import Card from '../components/Card';
import Button from '../components/Button';
import { logger } from '../utils/logger';

type QuestionnaireResultsScreenNavigationProp = StackNavigationProp<RootStackParamList, 'QuestionnaireResults'>;

interface RouteParams {
  sessionId: string;
  insights: UserInsight[];
  recommendations: PersonalizedRecommendation[];
}

const { width: screenWidth } = Dimensions.get('window');

const QuestionnaireResultsScreen: React.FC = () => {
  const { t, i18n } = useTranslation();
  const navigation = useNavigation<QuestionnaireResultsScreenNavigationProp>();
  const route = useRoute();
  const { user } = useAuth();
  const { sessionId, insights, recommendations } = route.params as RouteParams;

  // State management
  const [selectedTab, setSelectedTab] = useState<'insights' | 'recommendations'>('insights');
  const [expandedInsights, setExpandedInsights] = useState<Set<string>>(new Set());
  const [expandedRecommendations, setExpandedRecommendations] = useState<Set<string>>(new Set());
  
  // Animation values
  const [fadeAnim] = useState(new Animated.Value(0));
  const [slideAnim] = useState(new Animated.Value(50));

  useEffect(() => {
    // Animate entrance
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();

    logger.info('Questionnaire results displayed', {
      sessionId,
      insightsCount: insights.length,
      recommendationsCount: recommendations.length,
    }, 'QuestionnaireResultsScreen');
  }, []);

  const getSeverityColor = (severity: 'low' | 'medium' | 'high') => {
    switch (severity) {
      case 'high':
        return Theme.colors.error[500];
      case 'medium':
        return Theme.colors.warning[500];
      case 'low':
        return Theme.colors.success[500];
      default:
        return Theme.colors.neutral[500];
    }
  };

  const normalizePriority = (priority: 'high' | 'medium' | 'low' | number): 'high' | 'medium' | 'low' => {
    if (typeof priority === 'string') {
      return priority;
    }
    // Convert numeric priority to string (assuming 1-10 scale)
    if (priority >= 8) return 'high';
    if (priority >= 5) return 'medium';
    return 'low';
  };

  const getPriorityColor = (priority: 'high' | 'medium' | 'low' | number) => {
    const normalizedPriority = normalizePriority(priority);
    switch (normalizedPriority) {
      case 'high':
        return Theme.colors.error[500];
      case 'medium':
        return Theme.colors.warning[500];
      case 'low':
        return Theme.colors.success[500];
      default:
        return Theme.colors.neutral[500];
    }
  };

  const getCategoryIcon = (category: InsightCategory) => {
    switch (category) {
      case 'posture_risk':
        return 'warning';
      case 'ergonomic_issue':
        return 'desktop';
      case 'lifestyle_factor':
        return 'fitness';
      case 'health_concern':
        return 'medical';
      case 'goal_alignment':
        return 'target';
      case 'motivation_opportunity':
        return 'trophy';
      case 'habit_formation':
        return 'repeat';
      default:
        return 'information-circle';
    }
  };

  const getRecommendationIcon = (type: RecommendationType) => {
    switch (type) {
      case 'exercise_routine':
        return 'fitness';
      case 'ergonomic_adjustment':
        return 'construct';
      case 'lifestyle_change':
        return 'leaf';
      case 'habit_formation':
        return 'repeat';
      case 'goal_modification':
        return 'flag';
      case 'app_setting':
        return 'settings';
      case 'professional_consultation':
        return 'people';
      default:
        return 'bulb';
    }
  };

  const toggleInsightExpansion = (insightId: string) => {
    const newExpanded = new Set(expandedInsights);
    if (newExpanded.has(insightId)) {
      newExpanded.delete(insightId);
    } else {
      newExpanded.add(insightId);
    }
    setExpandedInsights(newExpanded);
  };

  const toggleRecommendationExpansion = (recommendationId: string) => {
    const newExpanded = new Set(expandedRecommendations);
    if (newExpanded.has(recommendationId)) {
      newExpanded.delete(recommendationId);
    } else {
      newExpanded.add(recommendationId);
    }
    setExpandedRecommendations(newExpanded);
  };

  const handleShare = async () => {
    try {
      const shareContent = `My PostureApp Assessment Results:\n\n` +
        `📊 Insights: ${insights.length} key findings\n` +
        `💡 Recommendations: ${recommendations.length} personalized suggestions\n\n` +
        `Ready to improve my posture and wellness! 💪`;

      await Share.share({
        message: shareContent,
        title: 'My PostureApp Assessment Results',
      });
    } catch (error) {
      logger.error('Failed to share results', error, 'QuestionnaireResultsScreen');
    }
  };

  const handleStartJourney = () => {
    // Navigate to main app with personalized settings applied
    navigation.reset({
      index: 0,
      routes: [{ name: 'Main' }],
    });
  };

  const renderInsightCard = (insight: UserInsight) => {
    const isExpanded = expandedInsights.has(insight.id);
    
    return (
      <Card key={insight.id} variant="outlined" style={styles.insightCard}>
        <TouchableOpacity
          onPress={() => toggleInsightExpansion(insight.id)}
          style={styles.insightHeader}
        >
          <View style={styles.insightIconContainer}>
            <Ionicons
              name={getCategoryIcon(insight.category) as any}
              size={24}
              color={getSeverityColor(insight.severity)}
            />
          </View>
          
          <View style={styles.insightContent}>
            <View style={styles.insightTitleRow}>
              <Text style={styles.insightTitle}>{insight.title}</Text>
              <View style={[
                styles.severityBadge,
                { backgroundColor: getSeverityColor(insight.severity) }
              ]}>
                <Text style={styles.severityText}>
                  {insight.severity.toUpperCase()}
                </Text>
              </View>
            </View>
            
            <Text style={styles.insightDescription} numberOfLines={isExpanded ? undefined : 2}>
              {insight.description}
            </Text>
            
            <View style={styles.insightMeta}>
              <View style={styles.confidenceContainer}>
                <Ionicons name="analytics" size={14} color={Theme.colors.text.tertiary} />
                <Text style={styles.confidenceText}>
                  {Math.round(insight.confidence * 100)}% confidence
                </Text>
              </View>
              
              {insight.actionable && (
                <View style={styles.actionableContainer}>
                  <Ionicons name="flash" size={14} color={Theme.colors.primary[500]} />
                  <Text style={styles.actionableText}>
                    {t('questionnaire.actionable', 'Actionable')}
                  </Text>
                </View>
              )}
            </View>
          </View>
          
          <Ionicons
            name={isExpanded ? 'chevron-up' : 'chevron-down'}
            size={20}
            color={Theme.colors.text.secondary}
          />
        </TouchableOpacity>
      </Card>
    );
  };

  const renderRecommendationCard = (recommendation: PersonalizedRecommendation) => {
    const isExpanded = expandedRecommendations.has(recommendation.id);
    
    return (
      <Card key={recommendation.id} variant="outlined" style={styles.recommendationCard}>
        <TouchableOpacity
          onPress={() => toggleRecommendationExpansion(recommendation.id)}
          style={styles.recommendationHeader}
        >
          <View style={styles.recommendationIconContainer}>
            <Ionicons
              name={getRecommendationIcon(recommendation.type) as any}
              size={24}
              color={Theme.colors.primary[500]}
            />
          </View>
          
          <View style={styles.recommendationContent}>
            <View style={styles.recommendationTitleRow}>
              <Text style={styles.recommendationTitle}>{recommendation.title}</Text>
              <View style={[
                styles.priorityBadge,
                { backgroundColor: getPriorityColor(recommendation.priority) }
              ]}>
                <Text style={styles.priorityText}>
                  {normalizePriority(recommendation.priority).toUpperCase()}
                </Text>
              </View>
            </View>
            
            <Text style={styles.recommendationDescription} numberOfLines={isExpanded ? undefined : 2}>
              {recommendation.description}
            </Text>
            
            <View style={styles.recommendationMeta}>
              <View style={styles.impactContainer}>
                <Ionicons name="trending-up" size={14} color={Theme.colors.success[500]} />
                <Text style={styles.impactText}>
                  {recommendation.estimatedImpact}/10 impact
                </Text>
              </View>
              
              <View style={styles.timeContainer}>
                <Ionicons name="time" size={14} color={Theme.colors.text.tertiary} />
                <Text style={styles.timeText}>
                  {recommendation.timeToImplement}
                </Text>
              </View>
            </View>
            
            {isExpanded && recommendation.actionSteps && recommendation.actionSteps.length > 0 && (
              <View style={styles.actionStepsContainer}>
                <Text style={styles.actionStepsTitle}>
                  {t('questionnaire.actionSteps', 'Action Steps:')}
                </Text>
                {recommendation.actionSteps.map((step, index) => (
                  <View key={index} style={styles.actionStep}>
                    <Text style={styles.actionStepNumber}>{index + 1}</Text>
                    <Text style={styles.actionStepText}>{step}</Text>
                  </View>
                ))}
              </View>
            )}
          </View>
          
          <Ionicons
            name={isExpanded ? 'chevron-up' : 'chevron-down'}
            size={20}
            color={Theme.colors.text.secondary}
          />
        </TouchableOpacity>
      </Card>
    );
  };

  const renderSummaryStats = () => {
    const highPriorityRecommendations = recommendations.filter(r => r.priority === 'high').length;
    const highSeverityInsights = insights.filter(i => i.severity === 'high').length;
    const actionableInsights = insights.filter(i => i.actionable).length;
    
    return (
      <View style={styles.summaryContainer}>
        <View style={styles.summaryCard}>
          <Text style={styles.summaryNumber}>{insights.length}</Text>
          <Text style={styles.summaryLabel}>
            {t('questionnaire.insights', 'Insights')}
          </Text>
        </View>
        
        <View style={styles.summaryCard}>
          <Text style={styles.summaryNumber}>{recommendations.length}</Text>
          <Text style={styles.summaryLabel}>
            {t('questionnaire.recommendations', 'Recommendations')}
          </Text>
        </View>
        
        <View style={styles.summaryCard}>
          <Text style={styles.summaryNumber}>{actionableInsights}</Text>
          <Text style={styles.summaryLabel}>
            {t('questionnaire.actionable', 'Actionable')}
          </Text>
        </View>
      </View>
    );
  };

  return (
    <LinearGradient
      colors={[Theme.colors.primary[50], Theme.colors.neutral[0]]}
      style={styles.container}
    >
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.closeButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="close" size={24} color={Theme.colors.text.primary} />
        </TouchableOpacity>
        
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>
            {t('questionnaire.resultsTitle', 'Your Personal Assessment')}
          </Text>
          <Text style={styles.headerSubtitle}>
            {t('questionnaire.resultsSubtitle', 'Insights & Recommendations')}
          </Text>
        </View>
        
        <TouchableOpacity 
          style={styles.shareButton}
          onPress={handleShare}
        >
          <Ionicons name="share" size={24} color={Theme.colors.primary[500]} />
        </TouchableOpacity>
      </View>

      <Animated.View 
        style={[
          styles.content,
          {
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }],
          },
        ]}
      >
        {/* Summary Stats */}
        {renderSummaryStats()}

        {/* Tab Navigation */}
        <View style={styles.tabContainer}>
          <TouchableOpacity
            style={[
              styles.tab,
              selectedTab === 'insights' && styles.tabActive,
            ]}
            onPress={() => setSelectedTab('insights')}
          >
            <Ionicons 
              name="analytics" 
              size={20} 
              color={selectedTab === 'insights' ? Theme.colors.primary[500] : Theme.colors.text.secondary} 
            />
            <Text style={[
              styles.tabText,
              selectedTab === 'insights' && styles.tabTextActive,
            ]}>
              {t('questionnaire.insights', 'Insights')}
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.tab,
              selectedTab === 'recommendations' && styles.tabActive,
            ]}
            onPress={() => setSelectedTab('recommendations')}
          >
            <Ionicons 
              name="bulb" 
              size={20} 
              color={selectedTab === 'recommendations' ? Theme.colors.primary[500] : Theme.colors.text.secondary} 
            />
            <Text style={[
              styles.tabText,
              selectedTab === 'recommendations' && styles.tabTextActive,
            ]}>
              {t('questionnaire.recommendations', 'Recommendations')}
            </Text>
          </TouchableOpacity>
        </View>

        {/* Content */}
        <ScrollView 
          style={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContentContainer}
        >
          {selectedTab === 'insights' ? (
            <View style={styles.insightsContainer}>
              {insights.length > 0 ? (
                insights.map(renderInsightCard)
              ) : (
                <View style={styles.emptyState}>
                  <Ionicons name="analytics" size={48} color={Theme.colors.text.tertiary} />
                  <Text style={styles.emptyStateText}>
                    {t('questionnaire.noInsights', 'No insights available')}
                  </Text>
                </View>
              )}
            </View>
          ) : (
            <View style={styles.recommendationsContainer}>
              {recommendations.length > 0 ? (
                recommendations.map(renderRecommendationCard)
              ) : (
                <View style={styles.emptyState}>
                  <Ionicons name="bulb" size={48} color={Theme.colors.text.tertiary} />
                  <Text style={styles.emptyStateText}>
                    {t('questionnaire.noRecommendations', 'No recommendations available')}
                  </Text>
                </View>
              )}
            </View>
          )}
        </ScrollView>
      </Animated.View>

      {/* Action Button */}
      <View style={styles.actionContainer}>
        <Button
          title={t('questionnaire.startJourney', 'Start My Wellness Journey')}
          onPress={handleStartJourney}
          style={styles.actionButton}
          icon="arrow-forward"
        />
      </View>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Theme.spacing.lg,
    paddingTop: Theme.spacing['2xl'],
    paddingBottom: Theme.spacing.lg,
  },
  closeButton: {
    padding: Theme.spacing.sm,
  },
  headerContent: {
    flex: 1,
    marginHorizontal: Theme.spacing.lg,
  },
  headerTitle: {
    fontSize: Theme.typography.fontSize.xl,
    fontWeight: Theme.typography.fontWeight.bold as any,
    color: Theme.colors.text.primary,
    textAlign: 'center',
  },
  headerSubtitle: {
    fontSize: Theme.typography.fontSize.sm,
    color: Theme.colors.text.secondary,
    textAlign: 'center',
    marginTop: Theme.spacing.xs,
  },
  shareButton: {
    padding: Theme.spacing.sm,
  },
  content: {
    flex: 1,
  },
  summaryContainer: {
    flexDirection: 'row',
    paddingHorizontal: Theme.spacing.lg,
    marginBottom: Theme.spacing.lg,
    gap: Theme.spacing.md,
  },
  summaryCard: {
    flex: 1,
    backgroundColor: Theme.colors.neutral[0],
    borderRadius: Theme.borderRadius.lg,
    padding: Theme.spacing.lg,
    alignItems: 'center',
    shadowColor: Theme.colors.neutral[950],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  summaryNumber: {
    fontSize: Theme.typography.fontSize['2xl'],
    fontWeight: Theme.typography.fontWeight.bold as any,
    color: Theme.colors.primary[500],
  },
  summaryLabel: {
    fontSize: Theme.typography.fontSize.sm,
    color: Theme.colors.text.secondary,
    marginTop: Theme.spacing.xs,
    textAlign: 'center',
  },
  tabContainer: {
    flexDirection: 'row',
    marginHorizontal: Theme.spacing.lg,
    marginBottom: Theme.spacing.lg,
    backgroundColor: Theme.colors.neutral[100],
    borderRadius: Theme.borderRadius.lg,
    padding: Theme.spacing.xs,
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Theme.spacing.md,
    paddingHorizontal: Theme.spacing.lg,
    borderRadius: Theme.borderRadius.md,
    gap: Theme.spacing.sm,
  },
  tabActive: {
    backgroundColor: Theme.colors.neutral[0],
    shadowColor: Theme.colors.neutral[950],
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  tabText: {
    fontSize: Theme.typography.fontSize.base,
    color: Theme.colors.text.secondary,
    fontWeight: Theme.typography.fontWeight.medium as any,
  },
  tabTextActive: {
    color: Theme.colors.primary[500],
  },
  scrollContent: {
    flex: 1,
  },
  scrollContentContainer: {
    paddingHorizontal: Theme.spacing.lg,
    paddingBottom: Theme.spacing['2xl'],
  },
  insightsContainer: {
    gap: Theme.spacing.md,
  },
  recommendationsContainer: {
    gap: Theme.spacing.md,
  },
  insightCard: {
    padding: 0,
  },
  insightHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: Theme.spacing.lg,
  },
  insightIconContainer: {
    width: 40,
    height: 40,
    borderRadius: Theme.borderRadius.full,
    backgroundColor: Theme.colors.neutral[100],
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Theme.spacing.md,
  },
  insightContent: {
    flex: 1,
  },
  insightTitleRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    marginBottom: Theme.spacing.sm,
  },
  insightTitle: {
    flex: 1,
    fontSize: Theme.typography.fontSize.lg,
    fontWeight: Theme.typography.fontWeight.semibold as any,
    color: Theme.colors.text.primary,
    marginRight: Theme.spacing.sm,
  },
  severityBadge: {
    paddingHorizontal: Theme.spacing.sm,
    paddingVertical: Theme.spacing.xs,
    borderRadius: Theme.borderRadius.sm,
  },
  severityText: {
    fontSize: Theme.typography.fontSize.xs,
    fontWeight: Theme.typography.fontWeight.bold as any,
    color: Theme.colors.neutral[0],
  },
  insightDescription: {
    fontSize: Theme.typography.fontSize.base,
    color: Theme.colors.text.secondary,
    lineHeight: 22,
    marginBottom: Theme.spacing.md,
  },
  insightMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Theme.spacing.lg,
  },
  confidenceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Theme.spacing.xs,
  },
  confidenceText: {
    fontSize: Theme.typography.fontSize.sm,
    color: Theme.colors.text.tertiary,
  },
  actionableContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Theme.spacing.xs,
  },
  actionableText: {
    fontSize: Theme.typography.fontSize.sm,
    color: Theme.colors.primary[500],
    fontWeight: Theme.typography.fontWeight.medium as any,
  },
  recommendationCard: {
    padding: 0,
  },
  recommendationHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: Theme.spacing.lg,
  },
  recommendationIconContainer: {
    width: 40,
    height: 40,
    borderRadius: Theme.borderRadius.full,
    backgroundColor: Theme.colors.primary[50],
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Theme.spacing.md,
  },
  recommendationContent: {
    flex: 1,
  },
  recommendationTitleRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    marginBottom: Theme.spacing.sm,
  },
  recommendationTitle: {
    flex: 1,
    fontSize: Theme.typography.fontSize.lg,
    fontWeight: Theme.typography.fontWeight.semibold as any,
    color: Theme.colors.text.primary,
    marginRight: Theme.spacing.sm,
  },
  priorityBadge: {
    paddingHorizontal: Theme.spacing.sm,
    paddingVertical: Theme.spacing.xs,
    borderRadius: Theme.borderRadius.sm,
  },
  priorityText: {
    fontSize: Theme.typography.fontSize.xs,
    fontWeight: Theme.typography.fontWeight.bold as any,
    color: Theme.colors.neutral[0],
  },
  recommendationDescription: {
    fontSize: Theme.typography.fontSize.base,
    color: Theme.colors.text.secondary,
    lineHeight: 22,
    marginBottom: Theme.spacing.md,
  },
  recommendationMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Theme.spacing.lg,
  },
  impactContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Theme.spacing.xs,
  },
  impactText: {
    fontSize: Theme.typography.fontSize.sm,
    color: Theme.colors.success[500],
    fontWeight: Theme.typography.fontWeight.medium as any,
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Theme.spacing.xs,
  },
  timeText: {
    fontSize: Theme.typography.fontSize.sm,
    color: Theme.colors.text.tertiary,
  },
  actionStepsContainer: {
    marginTop: Theme.spacing.lg,
    paddingTop: Theme.spacing.lg,
    borderTopWidth: 1,
    borderTopColor: Theme.colors.neutral[200],
  },
  actionStepsTitle: {
    fontSize: Theme.typography.fontSize.base,
    fontWeight: Theme.typography.fontWeight.semibold as any,
    color: Theme.colors.text.primary,
    marginBottom: Theme.spacing.md,
  },
  actionStep: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: Theme.spacing.sm,
  },
  actionStepNumber: {
    width: 20,
    height: 20,
    borderRadius: Theme.borderRadius.full,
    backgroundColor: Theme.colors.primary[500],
    color: Theme.colors.neutral[0],
    fontSize: Theme.typography.fontSize.xs,
    fontWeight: Theme.typography.fontWeight.bold as any,
    textAlign: 'center',
    lineHeight: 20,
    marginRight: Theme.spacing.sm,
  },
  actionStepText: {
    flex: 1,
    fontSize: Theme.typography.fontSize.sm,
    color: Theme.colors.text.secondary,
    lineHeight: 20,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: Theme.spacing['4xl'],
  },
  emptyStateText: {
    fontSize: Theme.typography.fontSize.lg,
    color: Theme.colors.text.tertiary,
    marginTop: Theme.spacing.lg,
    textAlign: 'center',
  },
  actionContainer: {
    paddingHorizontal: Theme.spacing.lg,
    paddingVertical: Theme.spacing.lg,
    backgroundColor: Theme.colors.neutral[0],
    borderTopWidth: 1,
    borderTopColor: Theme.colors.neutral[100],
  },
  actionButton: {
    width: '100%',
  },
});

export default QuestionnaireResultsScreen;