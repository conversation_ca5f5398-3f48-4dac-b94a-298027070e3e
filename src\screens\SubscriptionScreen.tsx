import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,

  Animated,
  StatusBar,
  Platform,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { useAuth } from '../contexts/AuthContext';
import { useAlert } from '../contexts/AlertContext';
import { RootStackParamList, SubscriptionTier, SUBSCRIPTION_PRICES, SUBSCRIPTION_FEATURES } from '../types';
import { PaymentService } from '../services/paymentService';

type SubscriptionNavigationProp = StackNavigationProp<RootStackParamList>;

const { width, height } = Dimensions.get('window');

interface PlanFeature {
  key: string;
  included: boolean;
  text: string;
}

interface Plan {
  tier: SubscriptionTier;
  name: string;
  tagline: string;
  price: { monthly: number; yearly: number } | null;
  popular?: boolean;
  features: PlanFeature[];
  gradient: [string, string];
}

const SubscriptionScreen: React.FC = () => {
  const { t } = useTranslation();
  const navigation = useNavigation<SubscriptionNavigationProp>();
  const { user } = useAuth();
  const { showAlert } = useAlert();

  const [selectedDuration, setSelectedDuration] = useState<'monthly' | 'yearly'>('yearly');
  const [selectedPlan, setSelectedPlan] = useState<SubscriptionTier>('plus');
  const [loading, setLoading] = useState<string | null>(null);

  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;

  React.useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(slideAnim, {
        toValue: 0,
        tension: 50,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const plans: Plan[] = [
    {
      tier: 'free',
      name: 'Starter',
      tagline: 'Perfect for beginners',
      price: null,
      gradient: ['#1a1a1a', '#2d2d2d'] as [string, string],
      features: [
        { key: 'posture_checks', included: true, text: '5 posture checks/month' },
        { key: 'basic_exercises', included: true, text: 'Basic exercise library' },
        { key: 'progress_tracking', included: true, text: 'Basic progress tracking' },
        { key: 'ai_coaching', included: false, text: 'AI-powered coaching' },
        { key: 'family_tracking', included: false, text: 'Family health tracking' },
        { key: 'premium_support', included: false, text: 'Priority support' },
      ],
    },
    {
      tier: 'plus',
      name: 'Professional',
      tagline: 'Most popular choice',
      price: SUBSCRIPTION_PRICES.plus,
      popular: true,
      gradient: ['#000000', '#1a1a1a'] as [string, string],
      features: [
        { key: 'posture_checks', included: true, text: 'Unlimited posture analysis' },
        { key: 'all_exercises', included: true, text: 'Complete exercise library' },
        { key: 'advanced_analytics', included: true, text: 'Advanced analytics & insights' },
        { key: 'ai_coaching', included: true, text: 'AI-powered coaching' },
        { key: 'ad_free', included: true, text: 'Ad-free experience' },
        { key: 'family_tracking', included: false, text: 'Family health tracking' },
      ],
    },
    {
      tier: 'pro',
      name: 'Enterprise',
      tagline: 'For serious wellness enthusiasts',
      price: SUBSCRIPTION_PRICES.pro,
      gradient: ['#2d2d2d', '#000000'] as [string, string],
      features: [
        { key: 'everything_plus', included: true, text: 'Everything in Professional' },
        { key: 'family_tracking', included: true, text: 'Family health tracking (up to 6)' },
        { key: 'priority_support', included: true, text: 'Priority customer support' },
        { key: 'custom_programs', included: true, text: 'Custom wellness programs' },
        { key: 'health_reports', included: true, text: 'Detailed health reports' },
        { key: 'early_access', included: true, text: 'Early access to new features' },
      ],
    },
  ];

  const handleSubscribe = async (tier: SubscriptionTier) => {
    if (!user) {
      showAlert({
        title: 'Authentication Required',
        message: 'Please sign in to subscribe.',
        type: 'warning',
      });
      return;
    }

    if (tier === 'free') {
      navigation.goBack();
      return;
    }

    try {
      setLoading(tier);
      const amount = plans.find(p => p.tier === tier)?.price?.[selectedDuration];
      if (!amount) throw new Error('Invalid plan');

      await PaymentService.initiateSubscriptionPayment(tier, selectedDuration, user.id);

      showAlert({
        title: 'Subscription Successful!',
        message: 'Welcome to PostureApp Premium. Enjoy your enhanced wellness journey!',
        type: 'success',
        buttons: [{ text: 'Continue', onPress: () => navigation.goBack() }],
      });
    } catch (error) {
      showAlert({
        title: 'Subscription Failed',
        message: 'Please try again or contact support.',
        type: 'error',
      });
    } finally {
      setLoading(null);
    }
  };

  const renderPlanCard = (plan: Plan, index: number) => {
    const isSelected = selectedPlan === plan.tier;
    const animationDelay = index * 100;

    return (
      <Animated.View
        key={plan.tier}
        style={[
          styles.planCard,
          isSelected && styles.planCardSelected,
          plan.popular && styles.planCardPopular,
          {
            opacity: fadeAnim,
            transform: [
              {
                translateY: slideAnim.interpolate({
                  inputRange: [0, 50],
                  outputRange: [0, 50 + animationDelay],
                }),
              },
            ],
          },
        ]}
      >
        <TouchableOpacity
          onPress={() => setSelectedPlan(plan.tier)}
          activeOpacity={0.9}
          style={styles.planCardTouchable}
        >
          <LinearGradient
            colors={plan.gradient}
            style={styles.planCardGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            {plan.popular && (
              <View style={styles.popularBadge}>
                <Text style={styles.popularBadgeText}>Most Popular</Text>
              </View>
            )}

            <View style={styles.planHeader}>
              <View style={styles.planTitleContainer}>
                <Text style={styles.planName}>{plan.name}</Text>
                <Text style={styles.planTagline}>{plan.tagline}</Text>
              </View>

              {plan.price && (
                <View style={styles.priceContainer}>
                  <View style={styles.priceRow}>
                    <Text style={styles.currency}>₹</Text>
                    <Text style={styles.price}>
                      {plan.price[selectedDuration]}
                    </Text>
                  </View>
                  <Text style={styles.pricePeriod}>
                    /{selectedDuration === 'monthly' ? 'month' : 'year'}
                  </Text>
                  {selectedDuration === 'yearly' && (
                    <Text style={styles.savingsText}>Save 20%</Text>
                  )}
                </View>
              )}

              {!plan.price && (
                <View style={styles.freeContainer}>
                  <Text style={styles.freeText}>Free</Text>
                  <Text style={styles.freeSubtext}>Forever</Text>
                </View>
              )}
            </View>

            <View style={styles.featuresContainer}>
              {plan.features.map((feature, featureIndex) => (
                <View key={feature.key} style={styles.featureItem}>
                  <View style={styles.featureIconContainer}>
                    <Ionicons
                      name={feature.included ? 'checkmark' : 'close'}
                      size={16}
                      color={feature.included ? '#4ADE80' : 'rgba(255, 255, 255, 0.3)'}
                    />
                  </View>
                  <Text
                    style={[
                      styles.featureText,
                      !feature.included && styles.featureTextDisabled,
                    ]}
                  >
                    {feature.text}
                  </Text>
                </View>
              ))}
            </View>

            <TouchableOpacity
              style={[
                styles.selectButton,
                plan.tier === 'free' && styles.selectButtonFree,
                plan.popular && styles.selectButtonPopular,
              ]}
              onPress={() => handleSubscribe(plan.tier)}
              disabled={loading === plan.tier}
            >
              {loading === plan.tier ? (
                <Text style={styles.selectButtonText}>Processing...</Text>
              ) : (
                <Text
                  style={[
                    styles.selectButtonText,
                    plan.tier === 'free' && styles.selectButtonTextFree,
                  ]}
                >
                  {plan.tier === 'free' ? 'Continue Free' : 'Choose Plan'}
                </Text>
              )}
            </TouchableOpacity>
          </LinearGradient>
        </TouchableOpacity>
      </Animated.View>
    );
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#000000" />

      {/* Background */}
      <LinearGradient
        colors={['#000000', '#1a1a1a', '#000000']}
        style={StyleSheet.absoluteFillObject}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
          activeOpacity={0.7}
        >
          <Ionicons name="chevron-back" size={24} color="#FFFFFF" />
        </TouchableOpacity>

        <View style={styles.headerTitleContainer}>
          <Text style={styles.headerTitle}>Choose Your Plan</Text>
          <Text style={styles.headerSubtitle}>Unlock your wellness potential</Text>
        </View>

        <View style={styles.headerSpacer} />
      </View>

      {/* Duration Toggle */}
      <Animated.View
        style={[
          styles.durationContainer,
          {
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }],
          },
        ]}
      >
        <View style={styles.durationToggle}>
          <TouchableOpacity
            style={[
              styles.durationButton,
              selectedDuration === 'monthly' && styles.durationButtonActive,
            ]}
            onPress={() => setSelectedDuration('monthly')}
            activeOpacity={0.7}
          >
            <Text
              style={[
                styles.durationButtonText,
                selectedDuration === 'monthly' && styles.durationButtonTextActive,
              ]}
            >
              Monthly
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.durationButton,
              selectedDuration === 'yearly' && styles.durationButtonActive,
            ]}
            onPress={() => setSelectedDuration('yearly')}
            activeOpacity={0.7}
          >
            <Text
              style={[
                styles.durationButtonText,
                selectedDuration === 'yearly' && styles.durationButtonTextActive,
              ]}
            >
              Yearly
            </Text>
            <View style={styles.savingsBadge}>
              <Text style={styles.savingsBadgeText}>20% OFF</Text>
            </View>
          </TouchableOpacity>
        </View>
      </Animated.View>

      {/* Plans */}
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {plans.map((plan, index) => renderPlanCard(plan, index))}

        {/* Footer */}
        <View style={styles.footer}>
          <Text style={styles.footerText}>
            All plans include a 7-day free trial. Cancel anytime.
          </Text>
          <Text style={styles.footerSubtext}>
            Terms and conditions apply. Privacy policy available.
          </Text>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
    paddingBottom: 20,
  },
  backButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitleContainer: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '300',
    color: '#FFFFFF',
    letterSpacing: 1,
  },
  headerSubtitle: {
    fontSize: 14,
    fontWeight: '400',
    color: 'rgba(255, 255, 255, 0.7)',
    letterSpacing: 0.5,
    marginTop: 2,
  },
  headerSpacer: {
    width: 44,
  },
  durationContainer: {
    paddingHorizontal: 24,
    marginBottom: 32,
  },
  durationToggle: {
    flexDirection: 'row',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 4,
  },
  durationButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 8,
    position: 'relative',
  },
  durationButtonActive: {
    backgroundColor: '#FFFFFF',
  },
  durationButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: 'rgba(255, 255, 255, 0.7)',
    letterSpacing: 0.3,
  },
  durationButtonTextActive: {
    color: '#000000',
  },
  savingsBadge: {
    position: 'absolute',
    top: -8,
    right: 8,
    backgroundColor: '#4ADE80',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 6,
  },
  savingsBadgeText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#000000',
    letterSpacing: 0.3,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 24,
    paddingBottom: 40,
  },
  planCard: {
    marginBottom: 20,
    borderRadius: 20,
    overflow: 'hidden',
  },
  planCardSelected: {
    transform: [{ scale: 1.02 }],
  },
  planCardPopular: {
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  planCardTouchable: {
    flex: 1,
  },
  planCardGradient: {
    padding: 24,
    position: 'relative',
  },
  popularBadge: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    backgroundColor: '#FFFFFF',
    paddingVertical: 8,
    alignItems: 'center',
  },
  popularBadgeText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#000000',
    letterSpacing: 1,
    textTransform: 'uppercase',
  },
  planHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 24,
    marginTop: 16,
  },
  planTitleContainer: {
    flex: 1,
  },
  planName: {
    fontSize: 24,
    fontWeight: '300',
    color: '#FFFFFF',
    letterSpacing: 1,
    marginBottom: 4,
  },
  planTagline: {
    fontSize: 14,
    fontWeight: '400',
    color: 'rgba(255, 255, 255, 0.7)',
    letterSpacing: 0.3,
  },
  priceContainer: {
    alignItems: 'flex-end',
  },
  priceRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  currency: {
    fontSize: 20,
    fontWeight: '300',
    color: '#FFFFFF',
    marginTop: 4,
  },
  price: {
    fontSize: 36,
    fontWeight: '200',
    color: '#FFFFFF',
    letterSpacing: -1,
  },
  pricePeriod: {
    fontSize: 14,
    fontWeight: '400',
    color: 'rgba(255, 255, 255, 0.6)',
    letterSpacing: 0.3,
  },
  savingsText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#4ADE80',
    letterSpacing: 0.5,
    marginTop: 2,
  },
  freeContainer: {
    alignItems: 'flex-end',
  },
  freeText: {
    fontSize: 36,
    fontWeight: '200',
    color: '#FFFFFF',
    letterSpacing: -1,
  },
  freeSubtext: {
    fontSize: 14,
    fontWeight: '400',
    color: 'rgba(255, 255, 255, 0.6)',
    letterSpacing: 0.3,
  },
  featuresContainer: {
    marginBottom: 24,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  featureIconContainer: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  featureText: {
    fontSize: 15,
    fontWeight: '400',
    color: '#FFFFFF',
    letterSpacing: 0.3,
    flex: 1,
  },
  featureTextDisabled: {
    color: 'rgba(255, 255, 255, 0.4)',
  },
  selectButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  selectButtonFree: {
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
  },
  selectButtonPopular: {
    backgroundColor: '#FFFFFF',
    borderColor: '#FFFFFF',
  },
  selectButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#FFFFFF',
    letterSpacing: 0.5,
  },
  selectButtonTextFree: {
    color: 'rgba(255, 255, 255, 0.7)',
  },
  footer: {
    alignItems: 'center',
    paddingTop: 32,
    paddingBottom: 16,
  },
  footerText: {
    fontSize: 14,
    fontWeight: '400',
    color: 'rgba(255, 255, 255, 0.6)',
    textAlign: 'center',
    letterSpacing: 0.3,
    marginBottom: 8,
  },
  footerSubtext: {
    fontSize: 12,
    fontWeight: '400',
    color: 'rgba(255, 255, 255, 0.4)',
    textAlign: 'center',
    letterSpacing: 0.3,
  },
});

export default SubscriptionScreen;