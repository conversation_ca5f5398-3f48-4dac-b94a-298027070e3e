import { supabase } from './supabase';

export interface PostureSession {
  id?: string;
  user_id: string;
  session_data: any;
  duration: number;
  score?: number;
  created_at?: string;
}

export interface UserStats {
  total_sessions: number;
  total_duration: number;
  average_score: number;
  best_score: number;
  streak_days: number;
}

export class DatabaseService {
  // Save posture session
  static async savePostureSession(session: PostureSession) {
    try {
      const { data, error } = await supabase
        .from('posture_sessions')
        .insert(session)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error: any) {
      throw error;
    }
  }

  // Get user's posture sessions
  static async getUserSessions(userId: string, limit = 50) {
    try {
      const { data, error } = await supabase
        .from('posture_sessions')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return data || [];
    } catch (error: any) {
      console.error('Get user sessions error:', error.message);
      return [];
    }
  }

  // Get user statistics
  static async getUserStats(userId: string): Promise<UserStats> {
    try {
      const { data, error } = await supabase
        .from('posture_sessions')
        .select('duration, score, created_at')
        .eq('user_id', userId);

      if (error) throw error;

      const sessions = data || [];
      const totalSessions = sessions.length;
      const totalDuration = sessions.reduce((sum, session) => sum + session.duration, 0);
      const scores = sessions.filter(s => s.score !== null).map(s => s.score);
      const averageScore = scores.length > 0 ? scores.reduce((sum, score) => sum + score, 0) / scores.length : 0;
      const bestScore = scores.length > 0 ? Math.max(...scores) : 0;

      // Calculate streak (simplified - consecutive days with sessions)
      const streakDays = this.calculateStreak(sessions);

      return {
        total_sessions: totalSessions,
        total_duration: totalDuration,
        average_score: Math.round(averageScore * 100) / 100,
        best_score: bestScore,
        streak_days: streakDays,
      };
    } catch (error: any) {
      console.error('Get user stats error:', error.message);
      return {
        total_sessions: 0,
        total_duration: 0,
        average_score: 0,
        best_score: 0,
        streak_days: 0,
      };
    }
  }

  // Calculate streak days
  private static calculateStreak(sessions: any[]): number {
    if (sessions.length === 0) return 0;

    const dates = sessions
      .map(session => new Date(session.created_at).toDateString())
      .filter((date, index, array) => array.indexOf(date) === index)
      .sort((a, b) => new Date(b).getTime() - new Date(a).getTime());

    let streak = 0;
    const today = new Date().toDateString();
    let currentDate = new Date();

    for (const dateStr of dates) {
      const sessionDate = new Date(dateStr);
      const diffDays = Math.floor((currentDate.getTime() - sessionDate.getTime()) / (1000 * 60 * 60 * 24));

      if (diffDays === streak) {
        streak++;
        currentDate = sessionDate;
      } else {
        break;
      }
    }

    return streak;
  }

  // Get sessions for a specific date range
  static async getSessionsInRange(userId: string, startDate: string, endDate: string) {
    try {
      const { data, error } = await supabase
        .from('posture_sessions')
        .select('*')
        .eq('user_id', userId)
        .gte('created_at', startDate)
        .lte('created_at', endDate)
        .order('created_at', { ascending: true });

      if (error) throw error;
      return data || [];
    } catch (error: any) {
      console.error('Get sessions in range error:', error.message);
      return [];
    }
  }

  // Delete a session
  static async deleteSession(sessionId: string) {
    try {
      const { error } = await supabase
        .from('posture_sessions')
        .delete()
        .eq('id', sessionId);

      if (error) throw error;
    } catch (error: any) {
      throw error;
    }
  }

  // Real-time subscription to user sessions
  static subscribeToUserSessions(userId: string, callback: (sessions: PostureSession[]) => void) {
    const subscription = supabase
      .channel('user_sessions')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'posture_sessions',
          filter: `user_id=eq.${userId}`,
        },
        () => {
          // Refetch sessions when changes occur
          this.getUserSessions(userId).then(callback);
        }
      )
      .subscribe();

    return subscription;
  }
}