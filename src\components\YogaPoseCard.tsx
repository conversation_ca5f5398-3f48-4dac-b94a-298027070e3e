import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { YogaPose } from '../types';
import { Theme } from '../constants/designTokens';

interface YogaPoseCardProps {
  pose: YogaPose;
  accessible: boolean;
  onPress: () => void;
  getDifficultyColor: (difficulty: string) => string;
}

const YogaPoseCard: React.FC<YogaPoseCardProps> = ({
  pose,
  accessible,
  onPress,
  getDifficultyColor,
}) => {
  const { i18n } = useTranslation();

  return (
    <TouchableOpacity
      style={[styles.container, !accessible && styles.containerLocked]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View style={styles.content}>
        <View style={styles.imageContainer}>
          <View style={styles.imagePlaceholder}>
            <Ionicons name="fitness" size={28} color={Theme.colors.primary[400]} />
          </View>
          {!accessible && (
            <View style={styles.lockOverlay}>
              <Ionicons name="lock-closed" size={16} color={Theme.colors.text.inverse} />
            </View>
          )}
          <View style={[styles.difficultyIndicator, { backgroundColor: getDifficultyColor(pose.difficulty) }]} />
        </View>
        
        <View style={styles.info}>
          <View style={styles.header}>
            <Text style={styles.name} numberOfLines={1}>
              {i18n.language === 'hi' ? pose.nameHindi : pose.name}
            </Text>
            <View style={styles.duration}>
              <Ionicons name="time-outline" size={12} color={Theme.colors.text.tertiary} />
              <Text style={styles.durationText}>
                {Math.floor(pose.duration / 60)}m
              </Text>
            </View>
          </View>
          
          <Text style={styles.description} numberOfLines={2}>
            {i18n.language === 'hi' ? pose.descriptionHindi : pose.description}
          </Text>
          
          <View style={styles.footer}>
            <View style={styles.targetAreas}>
              {pose.targetAreas.slice(0, 2).map((area, index) => (
                <View key={index} style={styles.targetArea}>
                  <Text style={styles.targetAreaText}>
                    {area.replace('_', ' ')}
                  </Text>
                </View>
              ))}
              {pose.targetAreas.length > 2 && (
                <Text style={styles.moreAreas}>+{pose.targetAreas.length - 2}</Text>
              )}
            </View>
            
            <View style={styles.action}>
              <Ionicons 
                name={accessible ? "play-circle" : "lock-closed"} 
                size={20} 
                color={accessible ? Theme.colors.primary[500] : Theme.colors.text.tertiary} 
              />
            </View>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: Theme.borderRadius['2xl'],
    marginBottom: Theme.spacing.lg,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    overflow: 'hidden',
  },
  containerLocked: {
    opacity: 0.6,
  },
  content: {
    padding: Theme.spacing.lg,
  },
  imageContainer: {
    position: 'relative',
    marginBottom: Theme.spacing.md,
  },
  imagePlaceholder: {
    height: 80,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: Theme.borderRadius.lg,
    justifyContent: 'center',
    alignItems: 'center',
  },
  difficultyIndicator: {
    position: 'absolute',
    top: Theme.spacing.xs,
    right: Theme.spacing.xs,
    width: 8,
    height: 8,
    borderRadius: Theme.borderRadius.full,
  },
  lockOverlay: {
    position: 'absolute',
    top: Theme.spacing.xs,
    left: Theme.spacing.xs,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    borderRadius: Theme.borderRadius.sm,
    padding: Theme.spacing.xs,
  },
  info: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Theme.spacing.xs,
  },
  name: {
    flex: 1,
    fontSize: Theme.typography.fontSize.lg,
    fontWeight: Theme.typography.fontWeight.semibold,
    color: '#FFFFFF',
    marginRight: Theme.spacing.sm,
  },
  description: {
    fontSize: Theme.typography.fontSize.sm,
    color: 'rgba(255, 255, 255, 0.7)',
    lineHeight: Theme.typography.lineHeight.normal * Theme.typography.fontSize.sm,
    marginBottom: Theme.spacing.md,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  duration: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  durationText: {
    fontSize: Theme.typography.fontSize.xs,
    color: 'rgba(255, 255, 255, 0.6)',
    marginLeft: Theme.spacing.xs,
    fontWeight: Theme.typography.fontWeight.medium,
  },
  targetAreas: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  targetArea: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: Theme.spacing.xs,
    paddingVertical: 2,
    borderRadius: Theme.borderRadius.sm,
    marginRight: Theme.spacing.xs,
  },
  targetAreaText: {
    fontSize: Theme.typography.fontSize.xs,
    color: '#FFFFFF',
    fontWeight: Theme.typography.fontWeight.medium,
    textTransform: 'capitalize',
  },
  moreAreas: {
    fontSize: Theme.typography.fontSize.xs,
    color: 'rgba(255, 255, 255, 0.6)',
    fontWeight: Theme.typography.fontWeight.medium,
  },
  action: {
    marginLeft: Theme.spacing.sm,
  },
});

export default YogaPoseCard;