import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-community/netinfo';
import {
  QuestionnaireSession,
  QuestionnaireResponse,
  UserInsight,
  PersonalizedRecommendation,
} from '../types';
import { dataService } from './dataService';
import { logger } from '../utils/logger';

/**
 * Offline questionnaire manager for seamless offline/online experience
 * Handles data synchronization and offline storage
 */
export class OfflineQuestionnaireManager {
  private static instance: OfflineQuestionnaireManager;
  private syncQueue: Array<{
    type: 'session' | 'response' | 'insights' | 'recommendations';
    data: any;
    timestamp: number;
  }> = [];

  private constructor() {
    this.initializeOfflineSupport();
  }

  public static getInstance(): OfflineQuestionnaireManager {
    if (!OfflineQuestionnaireManager.instance) {
      OfflineQuestionnaireManager.instance = new OfflineQuestionnaireManager();
    }
    return OfflineQuestionnaireManager.instance;
  }

  /**
   * Initialize offline support and sync monitoring
   */
  private async initializeOfflineSupport(): Promise<void> {
    try {
      // Load pending sync queue
      await this.loadSyncQueue();

      // Monitor network connectivity
      NetInfo.addEventListener(state => {
        if (state.isConnected && state.isInternetReachable) {
          this.processSyncQueue();
        }
      });

      // Initial sync if online
      const netInfo = await NetInfo.fetch();
      if (netInfo.isConnected && netInfo.isInternetReachable) {
        this.processSyncQueue();
      }

      logger.info('Offline questionnaire manager initialized', {
        queueSize: this.syncQueue.length,
      }, 'OfflineQuestionnaireManager');

    } catch (error) {
      logger.error('Failed to initialize offline support', error, 'OfflineQuestionnaireManager');
    }
  }

  /**
   * Save questionnaire session offline
   */
  async saveSessionOffline(session: QuestionnaireSession): Promise<void> {
    try {
      const key = `offline_session_${session.id}`;
      await AsyncStorage.setItem(key, JSON.stringify(session));

      // Add to sync queue
      this.addToSyncQueue('session', session);

      logger.info('Session saved offline', {
        sessionId: session.id,
        userId: session.userId,
      }, 'OfflineQuestionnaireManager');

    } catch (error) {
      logger.error('Failed to save session offline', error, 'OfflineQuestionnaireManager');
      throw error;
    }
  }

  /**
   * Save questionnaire response offline
   */
  async saveResponseOffline(response: QuestionnaireResponse): Promise<void> {
    try {
      const key = `offline_response_${response.id}`;
      await AsyncStorage.setItem(key, JSON.stringify(response));

      // Add to sync queue
      this.addToSyncQueue('response', response);

      logger.info('Response saved offline', {
        responseId: response.id,
        questionId: response.questionId,
      }, 'OfflineQuestionnaireManager');

    } catch (error) {
      logger.error('Failed to save response offline', error, 'OfflineQuestionnaireManager');
      throw error;
    }
  }

  /**
   * Get offline session
   */
  async getOfflineSession(sessionId: string): Promise<QuestionnaireSession | null> {
    try {
      const key = `offline_session_${sessionId}`;
      const sessionData = await AsyncStorage.getItem(key);
      
      if (sessionData) {
        const session = JSON.parse(sessionData);
        // Convert date strings back to Date objects
        session.startedAt = new Date(session.startedAt);
        if (session.completedAt) {
          session.completedAt = new Date(session.completedAt);
        }
        return session;
      }

      return null;
    } catch (error) {
      logger.error('Failed to get offline session', error, 'OfflineQuestionnaireManager');
      return null;
    }
  }

  /**
   * Get all offline sessions for a user
   */
  async getOfflineSessionsForUser(userId: string): Promise<QuestionnaireSession[]> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const sessionKeys = keys.filter(key => key.startsWith('offline_session_'));
      
      const sessions: QuestionnaireSession[] = [];
      
      for (const key of sessionKeys) {
        const sessionData = await AsyncStorage.getItem(key);
        if (sessionData) {
          const session = JSON.parse(sessionData);
          if (session.userId === userId) {
            // Convert date strings back to Date objects
            session.startedAt = new Date(session.startedAt);
            if (session.completedAt) {
              session.completedAt = new Date(session.completedAt);
            }
            sessions.push(session);
          }
        }
      }

      return sessions.sort((a, b) => b.startedAt.getTime() - a.startedAt.getTime());
    } catch (error) {
      logger.error('Failed to get offline sessions', error, 'OfflineQuestionnaireManager');
      return [];
    }
  }

  /**
   * Add item to sync queue
   */
  private addToSyncQueue(type: 'session' | 'response' | 'insights' | 'recommendations', data: any): void {
    this.syncQueue.push({
      type,
      data,
      timestamp: Date.now(),
    });

    // Save sync queue to storage
    this.saveSyncQueue();
  }

  /**
   * Process sync queue when online
   */
  private async processSyncQueue(): Promise<void> {
    if (this.syncQueue.length === 0) return;

    try {
      logger.info('Processing sync queue', {
        queueSize: this.syncQueue.length,
      }, 'OfflineQuestionnaireManager');

      const processedItems: number[] = [];

      for (let i = 0; i < this.syncQueue.length; i++) {
        const item = this.syncQueue[i];
        
        try {
          switch (item.type) {
            case 'session':
              await dataService.saveQuestionnaireSession(item.data);
              break;
            case 'response':
              await dataService.saveQuestionnaireResponse(item.data);
              break;
            case 'insights':
              await dataService.saveUserInsights(item.data.userId, item.data.insights);
              break;
            case 'recommendations':
              await dataService.savePersonalizedRecommendations(item.data.userId, item.data.recommendations);
              break;
          }

          processedItems.push(i);
          
          // Remove from offline storage
          if (item.type === 'session') {
            await AsyncStorage.removeItem(`offline_session_${item.data.id}`);
          } else if (item.type === 'response') {
            await AsyncStorage.removeItem(`offline_response_${item.data.id}`);
          }

        } catch (error) {
          logger.error('Failed to sync item', {
            error,
            itemType: item.type,
            itemId: item.data.id,
          }, 'OfflineQuestionnaireManager');
        }
      }

      // Remove processed items from queue
      this.syncQueue = this.syncQueue.filter((_, index) => !processedItems.includes(index));
      
      // Save updated queue
      await this.saveSyncQueue();

      logger.info('Sync queue processed', {
        processedCount: processedItems.length,
        remainingCount: this.syncQueue.length,
      }, 'OfflineQuestionnaireManager');

    } catch (error) {
      logger.error('Failed to process sync queue', error, 'OfflineQuestionnaireManager');
    }
  }

  /**
   * Save sync queue to storage
   */
  private async saveSyncQueue(): Promise<void> {
    try {
      await AsyncStorage.setItem('questionnaire_sync_queue', JSON.stringify(this.syncQueue));
    } catch (error) {
      logger.error('Failed to save sync queue', error, 'OfflineQuestionnaireManager');
    }
  }

  /**
   * Load sync queue from storage
   */
  private async loadSyncQueue(): Promise<void> {
    try {
      const queueData = await AsyncStorage.getItem('questionnaire_sync_queue');
      if (queueData) {
        this.syncQueue = JSON.parse(queueData);
      }
    } catch (error) {
      logger.error('Failed to load sync queue', error, 'OfflineQuestionnaireManager');
      this.syncQueue = [];
    }
  }

  /**
   * Check if device is online
   */
  async isOnline(): Promise<boolean> {
    try {
      const netInfo = await NetInfo.fetch();
      return Boolean(netInfo.isConnected && netInfo.isInternetReachable);
    } catch (error) {
      return false;
    }
  }

  /**
   * Get sync status
   */
  getSyncStatus(): {
    pendingItems: number;
    lastSyncAttempt?: Date;
    isOnline: boolean;
  } {
    return {
      pendingItems: this.syncQueue.length,
      lastSyncAttempt: this.syncQueue.length > 0 ? new Date(Math.max(...this.syncQueue.map(item => item.timestamp))) : undefined,
      isOnline: false, // Will be updated by network listener
    };
  }

  /**
   * Force sync attempt
   */
  async forcSync(): Promise<void> {
    const isOnline = await this.isOnline();
    if (isOnline) {
      await this.processSyncQueue();
    } else {
      throw new Error('Device is offline');
    }
  }

  /**
   * Clear all offline data (use with caution)
   */
  async clearOfflineData(): Promise<void> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const questionnaireKeys = keys.filter(key => 
        key.startsWith('offline_session_') || 
        key.startsWith('offline_response_') ||
        key === 'questionnaire_sync_queue'
      );

      await AsyncStorage.multiRemove(questionnaireKeys);
      this.syncQueue = [];

      logger.info('Offline questionnaire data cleared', {
        clearedKeys: questionnaireKeys.length,
      }, 'OfflineQuestionnaireManager');

    } catch (error) {
      logger.error('Failed to clear offline data', error, 'OfflineQuestionnaireManager');
      throw error;
    }
  }
}

export const offlineQuestionnaireManager = OfflineQuestionnaireManager.getInstance();
