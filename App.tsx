import React, { useEffect } from 'react';
import { StatusBar } from 'expo-status-bar';
import { StyleSheet } from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { I18nextProvider } from 'react-i18next';

// Import services and navigation
import AppNavigator from './src/navigation/AppNavigator';
import { AuthProvider } from './src/contexts/AuthContext';
import { ThemeProvider } from './src/contexts/ThemeContext';
import { GuestProvider } from './src/contexts/GuestContext';
import { AlertProvider } from './src/contexts/AlertContext';
import i18n from './src/locales/i18n';
import { StorageMigration } from './src/utils/storageMigration';

export default function App() {
  useEffect(() => {
    async function initializeApp() {
      try {
        console.log('Starting app initialization...');
        
        // Run storage migration first
        await StorageMigration.migrateIfNeeded();
        console.log('Storage migration completed');

        // Test storage functionality
        const { AppStorage, STORAGE_KEYS } = await import('./src/utils/storage');
        
        // Test write and read
        await AppStorage.setItem('test_key', 'test_value');
        const testValue = await AppStorage.getItem('test_key');
        console.log('Storage test result:', testValue);
        
        // Check onboarding status
        const onboardingStatus = await AppStorage.getItem(STORAGE_KEYS.ONBOARDING_COMPLETE);
        console.log('Onboarding status:', onboardingStatus);
        
        // Uncomment the line below to reset onboarding for testing
        // await AppStorage.removeItem(STORAGE_KEYS.ONBOARDING_COMPLETE);
        // console.log('Onboarding status reset for testing');
      } catch (error) {
        console.error('App initialization failed:', error);
      }
    }

    initializeApp();
  }, []);

  return (
    <GestureHandlerRootView style={styles.container}>
      <SafeAreaProvider>
        <I18nextProvider i18n={i18n}>
          <ThemeProvider>
            <AuthProvider>
              <GuestProvider>
                <AlertProvider>
                  <AppNavigator />
                  <StatusBar style="auto" />
                </AlertProvider>
              </GuestProvider>
            </AuthProvider>
          </ThemeProvider>
        </I18nextProvider>
      </SafeAreaProvider>
    </GestureHandlerRootView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
