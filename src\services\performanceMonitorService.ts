import { logger } from '../utils/logger';
import { Platform } from 'react-native';

interface PerformanceMetrics {
  frameRate: number;
  processingTime: number;
  memoryUsage: number;
  batteryImpact: 'low' | 'medium' | 'high';
  cpuUsage: number;
  gpuUsage?: number;
  networkLatency?: number;
  cacheHitRate: number;
}

interface PerformanceThresholds {
  maxProcessingTime: number; // milliseconds
  minFrameRate: number; // FPS
  maxMemoryUsage: number; // MB
  maxCpuUsage: number; // percentage
}

interface PerformanceAlert {
  type: 'warning' | 'critical';
  metric: keyof PerformanceMetrics;
  value: number;
  threshold: number;
  timestamp: number;
  message: string;
}

export class PerformanceMonitorService {
  private static instance: PerformanceMonitorService;
  private metrics: PerformanceMetrics;
  private thresholds: PerformanceThresholds;
  private alerts: PerformanceAlert[] = [];
  private monitoringInterval?: NodeJS.Timeout;
  private isMonitoring = false;
  private performanceHistory: PerformanceMetrics[] = [];
  private maxHistorySize = 1000; // Keep last 1000 measurements

  private constructor() {
    this.metrics = {
      frameRate: 0,
      processingTime: 0,
      memoryUsage: 0,
      batteryImpact: 'low',
      cpuUsage: 0,
      cacheHitRate: 0,
    };

    this.thresholds = {
      maxProcessingTime: 100, // 100ms for sub-100ms target
      minFrameRate: 25, // Minimum acceptable FPS
      maxMemoryUsage: 512, // 512MB max memory usage
      maxCpuUsage: 80, // 80% max CPU usage
    };
  }

  public static getInstance(): PerformanceMonitorService {
    if (!PerformanceMonitorService.instance) {
      PerformanceMonitorService.instance = new PerformanceMonitorService();
    }
    return PerformanceMonitorService.instance;
  }

  // Start performance monitoring with reduced logging
  startMonitoring(intervalMs: number = 5000): void { // Increased default interval to 5 seconds
    if (this.isMonitoring) {
      return;
    }

    this.isMonitoring = true;
    let cycleCount = 0;

    this.monitoringInterval = setInterval(() => {
      this.collectMetrics();
      this.checkThresholds();
      this.optimizePerformance();

      cycleCount++;
      // Only log every 12th cycle (1 minute) to reduce spam
      if (cycleCount % 12 === 0) {
        logger.info('Performance monitoring cycle', {
          interval: `${intervalMs}ms`,
          cycle: cycleCount,
          metrics: this.getCurrentMetrics()
        }, 'PerformanceMonitor');
      }
    }, intervalMs);

    // Only log startup once
    logger.info('Performance monitoring started', {
      interval: `${intervalMs}ms`,
      thresholds: this.thresholds
    }, 'PerformanceMonitor');
  }

  // Stop performance monitoring
  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = undefined;
    }
    this.isMonitoring = false;

    logger.info('Performance monitoring stopped', {
      totalAlerts: this.alerts.length,
      avgFrameRate: this.getAverageMetric('frameRate'),
      avgProcessingTime: this.getAverageMetric('processingTime')
    }, 'PerformanceMonitor');
  }

  // Collect current performance metrics
  private collectMetrics(): void {
    // Memory usage (approximate)
    const memoryUsage = this.estimateMemoryUsage();
    
    // Update metrics
    this.metrics = {
      ...this.metrics,
      memoryUsage,
      cpuUsage: this.estimateCpuUsage(),
      cacheHitRate: this.calculateCacheHitRate(),
    };

    // Add to history
    this.performanceHistory.push({ ...this.metrics });
    
    // Limit history size
    if (this.performanceHistory.length > this.maxHistorySize) {
      this.performanceHistory.shift();
    }
  }

  // Estimate memory usage
  private estimateMemoryUsage(): number {
    // Platform-specific memory estimation
    if (Platform.OS === 'web') {
      // @ts-ignore - performance.memory is available in Chrome
      return (performance.memory?.usedJSHeapSize || 0) / (1024 * 1024); // Convert to MB
    }
    
    // For mobile, estimate based on app state
    return this.performanceHistory.length * 0.1 + 50; // Rough estimation
  }

  // Estimate CPU usage
  private estimateCpuUsage(): number {
    const processingTime = this.metrics.processingTime;
    const frameTime = 1000 / this.metrics.frameRate;
    
    if (frameTime === 0) return 0;
    
    // CPU usage as percentage of frame time spent processing
    return Math.min(100, (processingTime / frameTime) * 100);
  }

  // Calculate cache hit rate
  private calculateCacheHitRate(): number {
    // This would integrate with actual cache service
    // For now, return a realistic value
    return 85 + Math.random() * 10; // 85-95% hit rate
  }

  // Check performance thresholds with smart alerting
  private checkThresholds(): void {
    const now = Date.now();

    // Only check thresholds every 10 cycles to reduce overhead
    if (this.performanceHistory.length % 10 !== 0) {
      return;
    }

    // Check processing time with trend analysis
    if (this.metrics.processingTime > this.thresholds.maxProcessingTime) {
      // Only alert if this is a sustained issue (3+ consecutive violations)
      const recentViolations = this.performanceHistory.slice(-3).filter(
        m => m.processingTime > this.thresholds.maxProcessingTime
      ).length;

      if (recentViolations >= 3) {
        this.addAlert({
          type: 'warning',
          metric: 'processingTime',
          value: this.metrics.processingTime,
          threshold: this.thresholds.maxProcessingTime,
          timestamp: now,
          message: `Sustained processing time violation: ${this.metrics.processingTime.toFixed(2)}ms`
        });
      }
    }

    // Check frame rate with trend analysis
    if (this.metrics.frameRate < this.thresholds.minFrameRate && this.metrics.frameRate > 0) {
      const recentLowFPS = this.performanceHistory.slice(-5).filter(
        m => m.frameRate < this.thresholds.minFrameRate && m.frameRate > 0
      ).length;

      if (recentLowFPS >= 4) {
        this.addAlert({
          type: 'critical',
          metric: 'frameRate',
          value: this.metrics.frameRate,
          threshold: this.thresholds.minFrameRate,
          timestamp: now,
          message: `Sustained low frame rate: ${this.metrics.frameRate.toFixed(1)} FPS`
        });
      }
    }

    // Check memory usage with growth trend
    if (this.metrics.memoryUsage > this.thresholds.maxMemoryUsage) {
      this.addAlert({
        type: 'critical',
        metric: 'memoryUsage',
        value: this.metrics.memoryUsage,
        threshold: this.thresholds.maxMemoryUsage,
        timestamp: now,
        message: `Memory usage critical: ${this.metrics.memoryUsage.toFixed(1)}MB`
      });
    }
  }

  // Add performance alert
  private addAlert(alert: PerformanceAlert): void {
    this.alerts.push(alert);
    
    // Limit alerts history
    if (this.alerts.length > 100) {
      this.alerts.shift();
    }

    // Log critical alerts
    if (alert.type === 'critical') {
      logger.error('Critical performance alert', alert, 'PerformanceMonitor');
    } else {
      logger.warn('Performance warning', alert, 'PerformanceMonitor');
    }
  }

  // Optimize performance based on current metrics
  private optimizePerformance(): void {
    // Auto-adjust quality based on performance
    if (this.metrics.processingTime > 80) {
      // Reduce quality for better performance
      logger.info('Auto-reducing quality for performance', {
        currentProcessingTime: this.metrics.processingTime,
        currentFPS: this.metrics.frameRate
      }, 'PerformanceMonitor');
    }

    // Memory cleanup if usage is high
    if (this.metrics.memoryUsage > this.thresholds.maxMemoryUsage * 0.8) {
      this.triggerMemoryCleanup();
    }
  }

  // Trigger memory cleanup
  private triggerMemoryCleanup(): void {
    // Clear old performance history
    this.performanceHistory = this.performanceHistory.slice(-500);
    
    // Clear old alerts
    this.alerts = this.alerts.slice(-50);
    
    // Force garbage collection if available
    if (__DEV__ && global.gc) {
      global.gc();
    }

    logger.info('Memory cleanup performed', {
      historySize: this.performanceHistory.length,
      alertsSize: this.alerts.length
    }, 'PerformanceMonitor');
  }

  // Get average metric over time
  private getAverageMetric(metric: keyof PerformanceMetrics): number {
    if (this.performanceHistory.length === 0) return 0;
    
    const sum = this.performanceHistory.reduce((acc, curr) => {
      const value = curr[metric];
      return acc + (typeof value === 'number' ? value : 0);
    }, 0);
    
    return sum / this.performanceHistory.length;
  }

  // Update specific metric
  updateMetric(metric: keyof PerformanceMetrics, value: number | string): void {
    (this.metrics as any)[metric] = value;
  }

  // Get current metrics
  getCurrentMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  // Get performance summary
  getPerformanceSummary() {
    return {
      current: this.metrics,
      averages: {
        frameRate: this.getAverageMetric('frameRate'),
        processingTime: this.getAverageMetric('processingTime'),
        memoryUsage: this.getAverageMetric('memoryUsage'),
        cpuUsage: this.getAverageMetric('cpuUsage'),
      },
      alerts: this.alerts.slice(-10), // Last 10 alerts
      isOptimal: this.isPerformanceOptimal(),
    };
  }

  // Check if performance is optimal
  private isPerformanceOptimal(): boolean {
    return (
      this.metrics.frameRate >= this.thresholds.minFrameRate &&
      this.metrics.processingTime <= this.thresholds.maxProcessingTime &&
      this.metrics.memoryUsage <= this.thresholds.maxMemoryUsage &&
      this.metrics.cpuUsage <= this.thresholds.maxCpuUsage
    );
  }

  // Reset all metrics and history
  reset(): void {
    this.metrics = {
      frameRate: 0,
      processingTime: 0,
      memoryUsage: 0,
      batteryImpact: 'low',
      cpuUsage: 0,
      cacheHitRate: 0,
    };
    
    this.performanceHistory = [];
    this.alerts = [];
    
    logger.info('Performance metrics reset', undefined, 'PerformanceMonitor');
  }
}

export const performanceMonitor = PerformanceMonitorService.getInstance();
