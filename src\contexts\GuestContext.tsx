import React, { createContext, useContext, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface GuestData {
  postureChecksUsed: number;
  yogaSessionsCompleted: number;
  lastUsed: string;
  hasSeenSignupPrompt: boolean;
}

interface GuestContextType {
  guestData: GuestData;
  isGuestMode: boolean;
  incrementPostureCheck: () => Promise<void>;
  incrementYogaSession: () => Promise<void>;
  canUsePostureCheck: boolean;
  canUseYogaExercises: boolean;
  canViewProgress: boolean;
  shouldShowSignupPrompt: () => boolean;
  markSignupPromptSeen: () => Promise<void>;
  clearGuestData: () => Promise<void>;
  getFeatureLimitMessage: (feature: string) => string;
}

const GuestContext = createContext<GuestContextType | undefined>(undefined);

const GUEST_DATA_KEY = 'guest_data';
const MAX_POSTURE_CHECKS = 3;
const MAX_YOGA_SESSIONS = 2;

const defaultGuestData: GuestData = {
  postureChecksUsed: 0,
  yogaSessionsCompleted: 0,
  lastUsed: new Date().toISOString(),
  hasSeenSignupPrompt: false,
};

export const GuestProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [guestData, setGuestData] = useState<GuestData>(defaultGuestData);
  const [isGuestMode, setIsGuestMode] = useState(true);

  // Load guest data on mount
  useEffect(() => {
    loadGuestData();
  }, []);

  const loadGuestData = async () => {
    try {
      const stored = await AsyncStorage.getItem(GUEST_DATA_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        setGuestData(parsed);
      }
    } catch (error) {
      console.error('Error loading guest data:', error);
    }
  };

  const saveGuestData = async (data: GuestData) => {
    try {
      await AsyncStorage.setItem(GUEST_DATA_KEY, JSON.stringify(data));
      setGuestData(data);
    } catch (error) {
      console.error('Error saving guest data:', error);
    }
  };

  const incrementPostureCheck = async () => {
    const newData = {
      ...guestData,
      postureChecksUsed: guestData.postureChecksUsed + 1,
      lastUsed: new Date().toISOString(),
    };
    await saveGuestData(newData);
  };

  const incrementYogaSession = async () => {
    const newData = {
      ...guestData,
      yogaSessionsCompleted: guestData.yogaSessionsCompleted + 1,
      lastUsed: new Date().toISOString(),
    };
    await saveGuestData(newData);
  };

  const canUsePostureCheck = guestData.postureChecksUsed < MAX_POSTURE_CHECKS;
  const canUseYogaExercises = guestData.yogaSessionsCompleted < MAX_YOGA_SESSIONS;
  const canViewProgress = false; // Always requires authentication

  const shouldShowSignupPrompt = () => {
    // Show prompt after 2nd posture check or 1st yoga session
    return (
      !guestData.hasSeenSignupPrompt &&
      (guestData.postureChecksUsed >= 2 || guestData.yogaSessionsCompleted >= 1)
    );
  };

  const markSignupPromptSeen = async () => {
    const newData = {
      ...guestData,
      hasSeenSignupPrompt: true,
    };
    await saveGuestData(newData);
  };

  const clearGuestData = async () => {
    try {
      await AsyncStorage.removeItem(GUEST_DATA_KEY);
      setGuestData(defaultGuestData);
      setIsGuestMode(false);
    } catch (error) {
      console.error('Error clearing guest data:', error);
    }
  };

  const getFeatureLimitMessage = (feature: string) => {
    switch (feature) {
      case 'posture_check':
        return `You've used ${guestData.postureChecksUsed}/${MAX_POSTURE_CHECKS} free posture checks. Sign up to get unlimited access!`;
      case 'yoga_exercises':
        return `You've completed ${guestData.yogaSessionsCompleted}/${MAX_YOGA_SESSIONS} free yoga sessions. Create an account to continue!`;
      case 'progress':
        return 'Progress tracking requires an account. Sign up to track your wellness journey!';
      case 'family':
        return 'Family sharing requires an account. Create one to share with loved ones!';
      case 'premium':
        return 'This is a premium feature. Sign up and upgrade to unlock advanced capabilities!';
      default:
        return 'This feature requires an account. Sign up to continue!';
    }
  };

  const value: GuestContextType = {
    guestData,
    isGuestMode,
    incrementPostureCheck,
    incrementYogaSession,
    canUsePostureCheck,
    canUseYogaExercises,
    canViewProgress,
    shouldShowSignupPrompt,
    markSignupPromptSeen,
    clearGuestData,
    getFeatureLimitMessage,
  };

  return (
    <GuestContext.Provider value={value}>
      {children}
    </GuestContext.Provider>
  );
};

export const useGuest = () => {
  const context = useContext(GuestContext);
  if (!context) {
    throw new Error('useGuest must be used within a GuestProvider');
  }
  return context;
};