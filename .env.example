# Supabase Configuration
EXPO_PUBLIC_SUPABASE_URL=your_supabase_project_url
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# Payment Gateway Configuration
EXPO_PUBLIC_RAZORPAY_KEY_ID=your_razorpay_key_id
EXPO_PUBLIC_RAZORPAY_KEY_SECRET=your_razorpay_key_secret

# Google Services
EXPO_PUBLIC_GOOGLE_PLACES_API_KEY=your_google_places_api_key

# App Configuration
EXPO_PUBLIC_APP_ENV=development
EXPO_PUBLIC_API_BASE_URL=https://your-api-domain.com

# Analytics
EXPO_PUBLIC_ANALYTICS_ENABLED=true

# Notifications
EXPO_PUBLIC_FCM_VAPID_KEY=your_fcm_vapid_key

# Social Login
EXPO_PUBLIC_GOOGLE_CLIENT_ID=your_google_client_id
EXPO_PUBLIC_FACEBOOK_APP_ID=your_facebook_app_id

# MediaPipe Configuration
EXPO_PUBLIC_MEDIAPIPE_MODEL_URL=https://cdn.jsdelivr.net/npm/@mediapipe/pose

# Feature Flags
EXPO_PUBLIC_ENABLE_AR_FEATURES=true
EXPO_PUBLIC_ENABLE_FAMILY_FEATURES=true
EXPO_PUBLIC_ENABLE_PREMIUM_FEATURES=true

# Debug Settings
EXPO_PUBLIC_DEBUG_MODE=false
EXPO_PUBLIC_LOG_LEVEL=info