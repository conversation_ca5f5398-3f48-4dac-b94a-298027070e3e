import React, { useState } from 'react';
import {
  TextInput,
  View,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Theme } from '../constants/designTokens';

interface InputProps {
  label?: string;
  placeholder?: string;
  value: string;
  onChangeText: (text: string) => void;
  secureTextEntry?: boolean;
  keyboardType?: 'default' | 'email-address' | 'numeric' | 'phone-pad';
  autoCapitalize?: 'none' | 'sentences' | 'words' | 'characters';
  error?: string;
  disabled?: boolean;
  multiline?: boolean;
  numberOfLines?: number;
  leftIcon?: string;
  rightIcon?: string;
  onRightIconPress?: () => void;
  style?: ViewStyle;
  inputStyle?: TextStyle;
  autoFocus?: boolean;
}

const Input: React.FC<InputProps> = ({
  label,
  placeholder,
  value,
  onChangeText,
  secureTextEntry = false,
  keyboardType = 'default',
  autoCapitalize = 'none',
  error,
  disabled = false,
  multiline = false,
  numberOfLines = 1,
  leftIcon,
  rightIcon,
  onRightIconPress,
  style,
  inputStyle,
  autoFocus = false,
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [isPasswordVisible, setIsPasswordVisible] = useState(!secureTextEntry);

  const getContainerStyle = (): ViewStyle => {
    return {
      marginBottom: Theme.spacing.md,
    };
  };

  const getInputContainerStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      flexDirection: 'row',
      alignItems: multiline ? 'flex-start' : 'center',
      backgroundColor: Theme.colors.surface.primary,
      borderRadius: Theme.borderRadius.lg,
      borderWidth: 1,
      paddingHorizontal: Theme.spacing.lg,
      paddingVertical: multiline ? Theme.spacing.md : Theme.spacing.md,
      minHeight: multiline ? Theme.spacing['6xl'] : Theme.layout.input.height,
    };

    let borderColor = Theme.colors.border.primary;
    if (error) {
      borderColor = Theme.colors.error[500];
    } else if (isFocused) {
      borderColor = Theme.colors.primary[500];
    }

    return {
      ...baseStyle,
      borderColor,
      opacity: disabled ? 0.6 : 1,
    };
  };

  const getInputStyle = (): TextStyle => {
    return {
      flex: 1,
      fontSize: Theme.typography.fontSize.base,
      color: disabled ? Theme.colors.text.disabled : Theme.colors.text.primary,
      paddingLeft: leftIcon ? Theme.spacing.sm : 0,
      paddingRight: (rightIcon || secureTextEntry) ? Theme.spacing.sm : 0,
      textAlignVertical: multiline ? 'top' : 'center',
      lineHeight: Theme.typography.fontSize.base * Theme.typography.lineHeight.normal,
    };
  };

  const getLabelStyle = (): TextStyle => {
    return {
      fontSize: Theme.typography.fontSize.sm,
      fontWeight: Theme.typography.fontWeight.medium,
      color: error ? Theme.colors.error[600] : Theme.colors.text.secondary,
      marginBottom: Theme.spacing.xs,
    };
  };

  const getErrorStyle = (): TextStyle => {
    return {
      fontSize: Theme.typography.fontSize.sm,
      color: Theme.colors.error[600],
      marginTop: Theme.spacing.xs,
    };
  };

  const handlePasswordToggle = () => {
    setIsPasswordVisible(!isPasswordVisible);
  };

  const renderIcon = (iconName: string, onPress?: () => void, position: 'left' | 'right' = 'left') => {
    const iconColor = isFocused ? Theme.colors.primary[500] : Theme.colors.text.tertiary;
    
    if (onPress) {
      return (
        <TouchableOpacity onPress={onPress} style={{ padding: Theme.spacing.xs }}>
          <Ionicons
            name={iconName as any}
            size={20}
            color={iconColor}
          />
        </TouchableOpacity>
      );
    }

    return (
      <Ionicons
        name={iconName as any}
        size={20}
        color={iconColor}
        style={{
          marginRight: position === 'left' ? Theme.spacing.xs : 0,
          marginLeft: position === 'right' ? Theme.spacing.xs : 0,
        }}
      />
    );
  };

  return (
    <View style={[getContainerStyle(), style]}>
      {label && (
        <Text style={getLabelStyle()}>
          {label}
        </Text>
      )}
      
      <View style={getInputContainerStyle()}>
        {leftIcon && renderIcon(leftIcon, undefined, 'left')}
        
        <TextInput
          style={[getInputStyle(), inputStyle]}
          placeholder={placeholder}
          placeholderTextColor={Theme.colors.text.tertiary}
          value={value}
          onChangeText={onChangeText}
          secureTextEntry={secureTextEntry && !isPasswordVisible}
          keyboardType={keyboardType}
          autoCapitalize={autoCapitalize}
          editable={!disabled}
          multiline={multiline}
          numberOfLines={numberOfLines}
          autoFocus={autoFocus}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
        />
        
        {secureTextEntry && (
          renderIcon(
            isPasswordVisible ? 'eye-off' : 'eye',
            handlePasswordToggle,
            'right'
          )
        )}
        
        {rightIcon && !secureTextEntry && (
          renderIcon(rightIcon, onRightIconPress, 'right')
        )}
      </View>
      
      {error && (
        <Text style={getErrorStyle()}>
          {error}
        </Text>
      )}
    </View>
  );
};

export default Input;