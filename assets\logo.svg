<svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background Circle -->
  <circle cx="100" cy="100" r="90" fill="#FF6B35" fill-opacity="0.1" stroke="#FF6B35" stroke-width="2"/>
  
  <!-- Main Figure - Representing Good Posture -->
  <g transform="translate(100, 100)">
    <!-- Head -->
    <circle cx="0" cy="-45" r="12" fill="#FF6B35"/>
    
    <!-- Spine/Torso - Straight line representing good posture -->
    <line x1="0" y1="-33" x2="0" y2="25" stroke="#FF6B35" stroke-width="4" stroke-linecap="round"/>
    
    <!-- Shoulders -->
    <line x1="-18" y1="-20" x2="18" y2="-20" stroke="#FF6B35" stroke-width="3" stroke-linecap="round"/>
    
    <!-- Arms -->
    <line x1="-18" y1="-20" x2="-25" y2="5" stroke="#FF6B35" stroke-width="3" stroke-linecap="round"/>
    <line x1="18" y1="-20" x2="25" y2="5" stroke="#FF6B35" stroke-width="3" stroke-linecap="round"/>
    
    <!-- Hips -->
    <line x1="-12" y1="25" x2="12" y2="25" stroke="#FF6B35" stroke-width="3" stroke-linecap="round"/>
    
    <!-- Legs -->
    <line x1="-12" y1="25" x2="-15" y2="50" stroke="#FF6B35" stroke-width="3" stroke-linecap="round"/>
    <line x1="12" y1="25" x2="15" y2="50" stroke="#FF6B35" stroke-width="3" stroke-linecap="round"/>
  </g>
  
  <!-- Alignment Grid Lines - Subtle background pattern -->
  <g opacity="0.3">
    <line x1="100" y1="20" x2="100" y2="180" stroke="#FF6B35" stroke-width="1" stroke-dasharray="2,4"/>
    <line x1="40" y1="100" x2="160" y2="100" stroke="#FF6B35" stroke-width="1" stroke-dasharray="2,4"/>
  </g>
  
  <!-- Wellness Accent - Subtle leaf/growth element -->
  <g transform="translate(140, 60)" opacity="0.6">
    <path d="M0,0 Q8,-8 16,0 Q8,8 0,0" fill="#4ADE80" stroke="#4ADE80" stroke-width="1"/>
    <path d="M4,0 Q12,-6 20,2 Q12,10 4,0" fill="#4ADE80" stroke="#4ADE80" stroke-width="1"/>
  </g>
  
  <!-- App Name -->
  <text x="100" y="190" text-anchor="middle" font-family="-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif" font-size="16" font-weight="600" fill="#1F2937">PostureApp</text>
</svg>