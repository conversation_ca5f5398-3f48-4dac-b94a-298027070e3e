/**
 * ML Kit Pose Detection Service Tests
 * Comprehensive testing suite for production-ready pose detection
 */

import { Platform } from 'react-native';
import { MLKitPoseDetectionService } from '../services/mlKitPoseDetectionService';
import { featureFlags } from '../utils/featureFlags';

// Mock Platform
jest.mock('react-native', () => ({
  Platform: {
    OS: 'android', // Default to Android for ML Kit tests
  },
}));

// Mock logger
jest.mock('../utils/logger', () => ({
  logger: {
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
  },
}));

// Mock feature flags
jest.mock('../utils/featureFlags', () => ({
  featureFlags: {
    initialize: jest.fn(),
    isEnabled: jest.fn(),
    getPoseDetectionStatusMessage: jest.fn(),
  },
}));

describe('MLKitPoseDetectionService', () => {
  let service: MLKitPoseDetectionService;

  beforeEach(() => {
    service = MLKitPoseDetectionService.getInstance();
    jest.clearAllMocks();
  });

  describe('Initialization', () => {
    it('should initialize successfully on Android', async () => {
      (Platform as any).OS = 'android';
      
      await expect(service.initialize()).resolves.not.toThrow();
      expect(service.isReady()).toBe(true);
    });

    it('should throw error on unsupported platforms', async () => {
      (Platform as any).OS = 'ios';
      
      await expect(service.initialize()).rejects.toThrow(
        'ML Kit pose detection is not yet available on ios'
      );
    });

    it('should not initialize twice', async () => {
      (Platform as any).OS = 'android';
      
      await service.initialize();
      const firstInit = service.isReady();
      
      await service.initialize();
      const secondInit = service.isReady();
      
      expect(firstInit).toBe(true);
      expect(secondInit).toBe(true);
    });
  });

  describe('Device Profile Detection', () => {
    it('should detect Android device profile', () => {
      (Platform as any).OS = 'android';
      const profile = service.getDeviceProfile();
      
      expect(profile.targetFPS).toBe(30);
      expect(profile.maxProcessingTime).toBe(50);
      expect(profile.enableOptimizations).toBe(true);
    });

    it('should detect iOS device profile', () => {
      (Platform as any).OS = 'ios';
      const profile = service.getDeviceProfile();
      
      expect(profile.targetFPS).toBe(60);
      expect(profile.maxProcessingTime).toBe(30);
      expect(profile.enableOptimizations).toBe(false);
    });
  });

  describe('Pose Processing', () => {
    beforeEach(async () => {
      (Platform as any).OS = 'android';
      await service.initialize();
    });

    it('should process ML Kit pose results', () => {
      const mockCallback = jest.fn();
      service.setResultsCallback(mockCallback);

      const mockPoses = [{
        landmarks: [
          {
            type: 'NOSE',
            position: { x: 0.5, y: 0.2, z: 0 },
            inFrameLikelihood: 0.9
          },
          {
            type: 'LEFT_SHOULDER',
            position: { x: 0.4, y: 0.3, z: 0 },
            inFrameLikelihood: 0.8
          }
        ],
        confidence: 0.85
      }];

      service.processPoseResults(mockPoses, Date.now());

      expect(mockCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          landmarks: expect.arrayContaining([
            expect.objectContaining({
              name: 'nose',
              x: 0.5,
              y: 0.2,
              z: 0,
              visibility: 0.9
            }),
            expect.objectContaining({
              name: 'left_shoulder',
              x: 0.4,
              y: 0.3,
              z: 0,
              visibility: 0.8
            })
          ]),
          confidence: expect.any(Number),
          timestamp: expect.any(Number),
          processingTime: expect.any(Number),
          frameRate: expect.any(Number)
        })
      );
    });

    it('should handle empty pose results', () => {
      const mockCallback = jest.fn();
      service.setResultsCallback(mockCallback);

      service.processPoseResults([], Date.now());

      expect(mockCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          landmarks: [],
          confidence: 0,
          timestamp: expect.any(Number),
          processingTime: expect.any(Number),
          frameRate: expect.any(Number)
        })
      );
    });

    it('should calculate confidence correctly', () => {
      const mockCallback = jest.fn();
      service.setResultsCallback(mockCallback);

      const mockPoses = [{
        landmarks: [
          {
            type: 'NOSE',
            position: { x: 0.5, y: 0.2, z: 0 },
            inFrameLikelihood: 0.9
          },
          {
            type: 'LEFT_EYE',
            position: { x: 0.48, y: 0.18, z: 0 },
            inFrameLikelihood: 0.7
          }
        ]
      }];

      service.processPoseResults(mockPoses, Date.now());

      expect(mockCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          confidence: 0.8 // (0.9 + 0.7) / 2
        })
      );
    });
  });

  describe('Performance Metrics', () => {
    beforeEach(async () => {
      (Platform as any).OS = 'android';
      await service.initialize();
    });

    it('should track performance metrics', () => {
      const metrics = service.getCurrentMetrics();
      
      expect(metrics).toHaveProperty('processingTime');
      expect(metrics).toHaveProperty('frameRate');
      expect(metrics).toHaveProperty('droppedFrames');
      expect(metrics).toHaveProperty('totalFrames');
      expect(metrics).toHaveProperty('errorCount');
    });

    it('should update metrics after processing', () => {
      const mockCallback = jest.fn();
      service.setResultsCallback(mockCallback);

      const initialMetrics = service.getCurrentMetrics();
      
      service.processPoseResults([{
        landmarks: [],
        confidence: 0
      }], Date.now());

      const updatedMetrics = service.getCurrentMetrics();
      
      expect(updatedMetrics.totalFrames).toBeGreaterThan(initialMetrics.totalFrames);
    });
  });

  describe('Error Handling', () => {
    beforeEach(async () => {
      (Platform as any).OS = 'android';
      await service.initialize();
    });

    it('should handle processing errors gracefully', () => {
      const mockCallback = jest.fn();
      service.setResultsCallback(mockCallback);

      // Simulate processing error by passing invalid data
      expect(() => {
        service.processPoseResults(null as any, Date.now());
      }).not.toThrow();

      // Should still call callback with empty detection
      expect(mockCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          landmarks: [],
          confidence: 0
        })
      );
    });

    it('should track error count', () => {
      const mockCallback = jest.fn();
      service.setResultsCallback(mockCallback);

      const initialMetrics = service.getCurrentMetrics();
      
      // Cause an error
      service.processPoseResults(null as any, Date.now());
      
      const updatedMetrics = service.getCurrentMetrics();
      expect(updatedMetrics.errorCount).toBeGreaterThan(initialMetrics.errorCount);
    });
  });

  describe('Cleanup', () => {
    it('should cleanup resources properly', async () => {
      (Platform as any).OS = 'android';
      await service.initialize();
      
      const mockCallback = jest.fn();
      service.setResultsCallback(mockCallback);
      
      service.cleanup();
      
      // Should not call callback after cleanup
      service.processPoseResults([{
        landmarks: [],
        confidence: 0
      }], Date.now());
      
      expect(mockCallback).not.toHaveBeenCalled();
    });
  });

  describe('Landmark Name Mapping', () => {
    it('should map ML Kit landmark types correctly', () => {
      const mockCallback = jest.fn();
      service.setResultsCallback(mockCallback);

      const mockPoses = [{
        landmarks: [
          {
            type: 'NOSE',
            position: { x: 0.5, y: 0.2, z: 0 },
            inFrameLikelihood: 0.9
          },
          {
            type: 'LEFT_SHOULDER',
            position: { x: 0.4, y: 0.3, z: 0 },
            inFrameLikelihood: 0.8
          },
          {
            type: 'RIGHT_ELBOW',
            position: { x: 0.65, y: 0.45, z: 0 },
            inFrameLikelihood: 0.7
          }
        ]
      }];

      service.processPoseResults(mockPoses, Date.now());

      expect(mockCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          landmarks: expect.arrayContaining([
            expect.objectContaining({ name: 'nose' }),
            expect.objectContaining({ name: 'left_shoulder' }),
            expect.objectContaining({ name: 'right_elbow' })
          ])
        })
      );
    });
  });
});
