import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Dimensions } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { LinearGradient } from 'expo-linear-gradient';
import RealTimePoseAnalyzer from './RealTimePoseAnalyzer';
import { ARPoseDetection, PostureAnalysis } from '../types';
import { performanceMonitor } from '../services/performanceMonitorService';
import { realTimePostureService } from '../services/realTimePostureService';

const { width, height } = Dimensions.get('window');

interface DemoStats {
  totalDetections: number;
  averageScore: number;
  bestScore: number;
  worstScore: number;
  sessionDuration: number;
  issuesDetected: string[];
}

const PostureAnalysisDemo: React.FC = () => {
  const { t } = useTranslation();
  const [isActive, setIsActive] = useState(false);
  const [visualizationMode, setVisualizationMode] = useState<'2d' | '3d' | 'both'>('both');
  const [currentDetection, setCurrentDetection] = useState<ARPoseDetection | null>(null);
  const [currentAnalysis, setCurrentAnalysis] = useState<PostureAnalysis | null>(null);
  const [demoStats, setDemoStats] = useState<DemoStats>({
    totalDetections: 0,
    averageScore: 0,
    bestScore: 0,
    worstScore: 100,
    sessionDuration: 0,
    issuesDetected: [],
  });

  // Handle pose detection
  const handlePoseDetected = (detection: ARPoseDetection) => {
    setCurrentDetection(detection);
    
    setDemoStats(prev => ({
      ...prev,
      totalDetections: prev.totalDetections + 1,
    }));
  };

  // Handle posture analysis
  const handlePostureAnalysis = (analysis: PostureAnalysis) => {
    setCurrentAnalysis(analysis);
    
    setDemoStats(prev => {
      const newBest = Math.max(prev.bestScore, analysis.overallScore);
      const newWorst = Math.min(prev.worstScore, analysis.overallScore);
      const newAverage = (prev.averageScore * (prev.totalDetections - 1) + analysis.overallScore) / prev.totalDetections;
      
      const newIssues = analysis.issues?.map(issue => issue.type) || [];
      const uniqueIssues = Array.from(new Set([...prev.issuesDetected, ...newIssues]));
      
      return {
        ...prev,
        averageScore: newAverage,
        bestScore: newBest,
        worstScore: newWorst,
        issuesDetected: uniqueIssues,
      };
    });
  };

  // Toggle demo
  const toggleDemo = () => {
    if (isActive) {
      setIsActive(false);
      realTimePostureService.stopMonitoring();
      performanceMonitor.stopMonitoring();
    } else {
      setIsActive(true);
      realTimePostureService.startMonitoring();
      performanceMonitor.startMonitoring();
      
      // Reset stats
      setDemoStats({
        totalDetections: 0,
        averageScore: 0,
        bestScore: 0,
        worstScore: 100,
        sessionDuration: 0,
        issuesDetected: [],
      });
    }
  };

  // Cycle visualization mode
  const cycleVisualizationMode = () => {
    setVisualizationMode(prev => {
      switch (prev) {
        case '2d': return '3d';
        case '3d': return 'both';
        case 'both': return '2d';
        default: return '2d';
      }
    });
  };

  // Update session duration
  useEffect(() => {
    if (!isActive) return;
    
    const interval = setInterval(() => {
      setDemoStats(prev => ({
        ...prev,
        sessionDuration: prev.sessionDuration + 1,
      }));
    }, 1000);

    return () => clearInterval(interval);
  }, [isActive]);

  const getScoreColor = (score: number): string => {
    if (score >= 85) return '#4ADE80';
    if (score >= 70) return '#FDE047';
    if (score >= 50) return '#FB923C';
    return '#EF4444';
  };

  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Production Pose Detection Demo</Text>
        <Text style={styles.subtitle}>Real-time MediaPipe Integration</Text>
      </View>

      {/* Camera and Visualization */}
      <View style={styles.cameraSection}>
        {isActive ? (
          <RealTimePoseAnalyzer
            onPoseDetected={handlePoseDetected}
            onPostureAnalysis={handlePostureAnalysis}
            enableRealTimeAnalysis={true}
            enable3DVisualization={true}
            visualizationMode={visualizationMode}
            width={width}
            height={height * 0.5}
            userId="demo_user"
          />
        ) : (
          <View style={styles.inactiveCamera}>
            <Ionicons name="camera-outline" size={64} color="#6B7280" />
            <Text style={styles.inactiveCameraText}>Tap Start to begin pose detection</Text>
          </View>
        )}
      </View>

      {/* Controls */}
      <View style={styles.controls}>
        <TouchableOpacity
          style={[styles.controlButton, isActive && styles.controlButtonActive]}
          onPress={toggleDemo}
        >
          <Ionicons 
            name={isActive ? "stop" : "play"} 
            size={24} 
            color="#FFFFFF" 
          />
          <Text style={styles.controlButtonText}>
            {isActive ? 'Stop' : 'Start'} Demo
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.controlButton}
          onPress={cycleVisualizationMode}
          disabled={!isActive}
        >
          <Ionicons name="cube-outline" size={24} color="#FFFFFF" />
          <Text style={styles.controlButtonText}>
            Mode: {visualizationMode.toUpperCase()}
          </Text>
        </TouchableOpacity>
      </View>

      {/* Real-time Stats */}
      <ScrollView style={styles.statsSection}>
        <Text style={styles.statsTitle}>Live Performance Metrics</Text>
        
        <View style={styles.statsGrid}>
          <View style={styles.statCard}>
            <Text style={styles.statLabel}>Detections</Text>
            <Text style={styles.statValue}>{demoStats.totalDetections}</Text>
          </View>
          
          <View style={styles.statCard}>
            <Text style={styles.statLabel}>Duration</Text>
            <Text style={styles.statValue}>{formatDuration(demoStats.sessionDuration)}</Text>
          </View>
          
          <View style={styles.statCard}>
            <Text style={styles.statLabel}>Avg Score</Text>
            <Text style={[styles.statValue, { color: getScoreColor(demoStats.averageScore) }]}>
              {demoStats.averageScore.toFixed(1)}
            </Text>
          </View>
          
          <View style={styles.statCard}>
            <Text style={styles.statLabel}>Best Score</Text>
            <Text style={[styles.statValue, { color: getScoreColor(demoStats.bestScore) }]}>
              {demoStats.bestScore}
            </Text>
          </View>
        </View>

        {/* Current Detection Info */}
        {currentDetection && (
          <View style={styles.detectionInfo}>
            <Text style={styles.detectionTitle}>Current Detection</Text>
            <Text style={styles.detectionText}>
              Landmarks: {currentDetection.landmarks.length}
            </Text>
            <Text style={styles.detectionText}>
              Confidence: {(currentDetection.confidence * 100).toFixed(1)}%
            </Text>
            <Text style={styles.detectionText}>
              Processing: {currentDetection.processingTime?.toFixed(1) || 0}ms
            </Text>
            <Text style={styles.detectionText}>
              FPS: {currentDetection.frameRate?.toFixed(1) || 0}
            </Text>
          </View>
        )}

        {/* Issues Detected */}
        {demoStats.issuesDetected.length > 0 && (
          <View style={styles.issuesInfo}>
            <Text style={styles.issuesTitle}>Issues Detected This Session</Text>
            {demoStats.issuesDetected.map((issue, index) => (
              <Text key={index} style={styles.issueText}>
                • {issue.replace('_', ' ').toUpperCase()}
              </Text>
            ))}
          </View>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1F2937',
  },
  header: {
    padding: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#9CA3AF',
    textAlign: 'center',
    marginTop: 4,
  },
  cameraSection: {
    height: height * 0.5,
    backgroundColor: '#000000',
  },
  inactiveCamera: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  inactiveCameraText: {
    color: '#9CA3AF',
    fontSize: 16,
    marginTop: 12,
  },
  controls: {
    flexDirection: 'row',
    padding: 20,
    gap: 12,
  },
  controlButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#374151',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    gap: 8,
  },
  controlButtonActive: {
    backgroundColor: '#FF6B35',
  },
  controlButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  statsSection: {
    flex: 1,
    padding: 20,
  },
  statsTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginBottom: 20,
  },
  statCard: {
    backgroundColor: '#374151',
    padding: 16,
    borderRadius: 12,
    flex: 1,
    minWidth: '45%',
    alignItems: 'center',
  },
  statLabel: {
    color: '#9CA3AF',
    fontSize: 14,
    marginBottom: 4,
  },
  statValue: {
    color: '#FFFFFF',
    fontSize: 20,
    fontWeight: 'bold',
  },
  detectionInfo: {
    backgroundColor: '#374151',
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  detectionTitle: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  detectionText: {
    color: '#D1D5DB',
    fontSize: 14,
    marginBottom: 4,
  },
  issuesInfo: {
    backgroundColor: '#374151',
    padding: 16,
    borderRadius: 12,
  },
  issuesTitle: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  issueText: {
    color: '#F59E0B',
    fontSize: 14,
    marginBottom: 4,
  },
});

export default PostureAnalysisDemo;
