import { supabase } from './supabase';

export interface AuthUser {
  id: string;
  email: string;
  full_name?: string;
  avatar_url?: string;
}

export class AuthService {
  // Sign up with email and password
  static async signUp(email: string, password: string, fullName?: string) {
    try {
      console.log('Starting signup process for:', email);

      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
          },
          // Skip email confirmation for development
          // In production, remove this or set to true
          emailRedirectTo: undefined,
        },
      });

      if (error) {
        console.error('Supabase auth.signUp error:', error);
        throw error;
      }

      console.log('Auth user created successfully:', data.user?.id);

      // Profile creation is handled automatically by the database trigger
      // Wait for the session to be fully established and trigger to complete
      if (data.user && data.session) {
        // Wait longer to ensure session is fully established
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Verify the session is active
        const { data: { session: currentSession } } = await supabase.auth.getSession();
        if (!currentSession) {
          console.warn('Session not established after signup, but user was created');
        }

        console.log('Profile should be created by trigger for user:', data.user.id);
      }

      return { user: data.user, session: data.session };
    } catch (error: any) {
      console.error('SignUp error:', error);
      throw new Error(error.message || 'Registration failed');
    }
  }

  // Sign in with email and password
  static async signIn(email: string, password: string) {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) throw error;
      return { user: data.user, session: data.session };
    } catch (error: any) {
      throw error;
    }
  }

  // Sign out
  static async signOut() {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
    } catch (error: any) {
      throw error;
    }
  }

  // Get current user
  static async getCurrentUser() {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      return user;
    } catch (error: any) {
      console.error('Get current user error:', error.message);
      return null;
    }
  }

  // Get user profile
  static async getUserProfile(userId: string) {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) throw error;
      return data;
    } catch (error: any) {
      console.error('Get user profile error:', error.message);
      return null;
    }
  }

  // Update user profile
  static async updateProfile(userId: string, updates: Partial<AuthUser>) {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq('id', userId)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error: any) {
      throw error;
    }
  }

  // Reset password
  static async resetPassword(email: string) {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email);
      if (error) throw error;
      // Success - let the calling code handle UI feedback
    } catch (error: any) {
      throw error;
    }
  }
}