{"name": "Posture App", "slug": "posture-app", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "dark", "newArchEnabled": false, "scheme": "<PERSON><PERSON><PERSON>", "platforms": ["ios", "android", "web"], "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#000000"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.postureapp.ios", "buildNumber": "1", "icon": "./assets/icon.png"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#000000"}, "package": "com.postureapp.android", "versionCode": 1, "edgeToEdgeEnabled": true, "permissions": ["android.permission.CAMERA"]}, "web": {"favicon": "./assets/favicon.png", "bundler": "metro"}, "updates": {"fallbackToCacheTimeout": 0}, "runtimeVersion": "exposdk:53.0.0", "plugins": ["expo-secure-store", ["expo-camera", {"cameraPermission": "Allow $(PRODUCT_NAME) to access your camera for posture analysis.", "microphonePermission": false, "recordAudioAndroid": false}]], "extra": {"eas": {"projectId": "aae54f47-3338-4229-a8be-7da0b844f719"}}, "description": "AI-powered posture improvement app with real-time pose detection and personalized wellness recommendations", "sdkVersion": "53.0.0"}