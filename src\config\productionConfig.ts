/**
 * Production-ready configuration for PostureApp
 * Optimized for industrial-grade performance and reliability
 */

export interface ProductionConfig {
  // Performance settings
  performance: {
    targetFPS: number;
    maxProcessingLatency: number; // milliseconds
    memoryLimit: number; // MB
    cpuThreshold: number; // percentage
    batteryOptimization: boolean;
  };
  
  // Logging configuration
  logging: {
    level: 'error' | 'warn' | 'info' | 'debug';
    enableConsole: boolean;
    enableRemote: boolean;
    throttleWindow: number; // milliseconds
    maxLocalEntries: number;
  };
  
  // Pose detection settings
  poseDetection: {
    modelComplexity: 0 | 1 | 2;
    minDetectionConfidence: number;
    minTrackingConfidence: number;
    enableWorldLandmarks: boolean;
    smoothingFrames: number;
    errorRecoveryThreshold: number;
  };
  
  // AR visualization settings
  ar: {
    renderingFPS: number;
    enable3D: boolean;
    landmarkSize: number;
    connectionWidth: number;
    confidenceThreshold: number;
  };
  
  // Monitoring settings
  monitoring: {
    metricsInterval: number; // milliseconds
    alertThreshold: number; // consecutive violations
    historySize: number; // number of metrics to keep
    enablePredictiveAlerts: boolean;
  };
}

// Production configuration optimized for continuous operation
export const PRODUCTION_CONFIG: ProductionConfig = {
  performance: {
    targetFPS: 60,
    maxProcessingLatency: 50, // Sub-100ms total latency target
    memoryLimit: 512, // 512MB limit
    cpuThreshold: 70, // 70% CPU threshold
    batteryOptimization: true,
  },
  
  logging: {
    level: 'warn', // Only warnings and errors in production
    enableConsole: false, // Disable console logging in production
    enableRemote: true, // Enable remote logging for monitoring
    throttleWindow: 5000, // 5 second throttle window
    maxLocalEntries: 100, // Minimal local storage
  },
  
  poseDetection: {
    modelComplexity: 1, // Balanced accuracy/performance
    minDetectionConfidence: 0.6,
    minTrackingConfidence: 0.4,
    enableWorldLandmarks: false, // Disable for better performance
    smoothingFrames: 3, // Minimal smoothing for low latency
    errorRecoveryThreshold: 10, // Recover after 10 consecutive errors
  },
  
  ar: {
    renderingFPS: 60, // Smooth 60fps rendering
    enable3D: true,
    landmarkSize: 4,
    connectionWidth: 2,
    confidenceThreshold: 0.5,
  },
  
  monitoring: {
    metricsInterval: 5000, // 5 second intervals
    alertThreshold: 3, // 3 consecutive violations before alert
    historySize: 200, // Keep 200 metrics (16+ minutes at 5s intervals)
    enablePredictiveAlerts: true,
  },
};

// Development configuration with enhanced debugging
export const DEVELOPMENT_CONFIG: ProductionConfig = {
  performance: {
    targetFPS: 30,
    maxProcessingLatency: 100,
    memoryLimit: 1024, // More generous for development
    cpuThreshold: 80,
    batteryOptimization: false,
  },
  
  logging: {
    level: 'warn', // Reduced from debug to prevent spam
    enableConsole: true,
    enableRemote: false,
    throttleWindow: 2000, // Shorter throttle for development
    maxLocalEntries: 500,
  },
  
  poseDetection: {
    modelComplexity: 0, // Lightweight for development
    minDetectionConfidence: 0.5,
    minTrackingConfidence: 0.3,
    enableWorldLandmarks: true, // Enable for testing
    smoothingFrames: 5,
    errorRecoveryThreshold: 5,
  },
  
  ar: {
    renderingFPS: 30,
    enable3D: true,
    landmarkSize: 6, // Larger for visibility during development
    connectionWidth: 3,
    confidenceThreshold: 0.3,
  },
  
  monitoring: {
    metricsInterval: 2000, // More frequent for development
    alertThreshold: 2,
    historySize: 100,
    enablePredictiveAlerts: false, // Disable for simpler debugging
  },
};

// Get configuration based on environment
export const getConfig = (): ProductionConfig => {
  return __DEV__ ? DEVELOPMENT_CONFIG : PRODUCTION_CONFIG;
};

// Configuration validation
export const validateConfig = (config: ProductionConfig): boolean => {
  try {
    // Validate performance settings
    if (config.performance.targetFPS < 1 || config.performance.targetFPS > 120) {
      throw new Error('Invalid targetFPS: must be between 1 and 120');
    }
    
    if (config.performance.maxProcessingLatency < 10 || config.performance.maxProcessingLatency > 1000) {
      throw new Error('Invalid maxProcessingLatency: must be between 10ms and 1000ms');
    }
    
    // Validate pose detection settings
    if (config.poseDetection.minDetectionConfidence < 0 || config.poseDetection.minDetectionConfidence > 1) {
      throw new Error('Invalid minDetectionConfidence: must be between 0 and 1');
    }
    
    // Validate monitoring settings
    if (config.monitoring.metricsInterval < 1000) {
      throw new Error('Invalid metricsInterval: must be at least 1000ms');
    }
    
    return true;
  } catch (error) {
    console.error('Configuration validation failed:', error);
    return false;
  }
};

// Export current active configuration
export const activeConfig = getConfig();

// Validate configuration on import
if (!validateConfig(activeConfig)) {
  console.error('Invalid configuration detected - using safe defaults');
}
