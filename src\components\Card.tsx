import React from 'react';
import {
  View,
  StyleSheet,
  ViewStyle,
  TouchableOpacity,
} from 'react-native';
import { Theme } from '../constants/designTokens';

interface CardProps {
  children: React.ReactNode;
  variant?: 'default' | 'elevated' | 'outlined' | 'ghost';
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  onPress?: () => void;
  style?: ViewStyle;
  disabled?: boolean;
}

const Card: React.FC<CardProps> = ({
  children,
  variant = 'default',
  padding = 'lg',
  onPress,
  style,
  disabled = false,
}) => {
  const getCardStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      borderRadius: Theme.borderRadius.xl,
      overflow: 'hidden',
    };

    // Padding variations
    const paddingStyles: Record<string, ViewStyle> = {
      none: { padding: 0 },
      sm: { padding: Theme.spacing.md },
      md: { padding: Theme.spacing.lg },
      lg: { padding: Theme.spacing['2xl'] },
      xl: { padding: Theme.spacing['3xl'] },
    };

    // Variant styles
    const variantStyles: Record<string, ViewStyle> = {
      default: {
        backgroundColor: Theme.colors.surface.primary,
        ...Theme.shadows.sm,
        borderWidth: 1,
        borderColor: Theme.colors.border.primary,
      },
      elevated: {
        backgroundColor: Theme.colors.surface.primary,
        ...Theme.shadows.md,
        borderWidth: 0,
      },
      outlined: {
        backgroundColor: Theme.colors.surface.primary,
        borderWidth: 1,
        borderColor: Theme.colors.border.primary,
        shadowOpacity: 0,
        elevation: 0,
      },
      ghost: {
        backgroundColor: 'transparent',
        borderWidth: 0,
        shadowOpacity: 0,
        elevation: 0,
      },
    };

    return {
      ...baseStyle,
      ...variantStyles[variant],
      ...paddingStyles[padding],
      opacity: disabled ? 0.6 : 1,
    };
  };

  const CardComponent = onPress ? TouchableOpacity : View;

  return (
    <CardComponent
      style={[getCardStyle(), style]}
      onPress={onPress}
      disabled={disabled}
      activeOpacity={onPress ? 0.95 : 1}
    >
      {children}
    </CardComponent>
  );
};

export default Card;