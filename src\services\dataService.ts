import { supabase } from '../lib/supabase';
import { DatabaseService } from '../lib/database';
import { User, PostureAnalysis, YogaSession, UserAnalytics, DailyProgress } from '../types';
import { logger } from '../utils/logger';
import { cacheManager } from '../utils/cache';
import { AuthUtils } from '../utils/authUtils';

/**
 * Production-ready data service with Supabase integration
 * Includes error handling, data validation, caching, and security measures
 */
export class DataService {
  private static instance: DataService;
  private subscriptions: any[] = [];

  private constructor() {}

  public static getInstance(): DataService {
    if (!DataService.instance) {
      DataService.instance = new DataService();
    }
    return DataService.instance;
  }

  /**
   * Get current authenticated user ID with validation and retry logic
   */
  private async getCurrentUserId(): Promise<string> {
    try {
      const userId = await AuthUtils.getCurrentUserId(3);

      if (!userId) {
        logger.error('User not authenticated in DataService', null, 'DataService');
        throw new Error('User not authenticated');
      }

      return userId;
    } catch (error: any) {
      logger.error('Authentication error in getCurrentUserId', error, 'DataService');
      throw new Error('User not authenticated');
    }
  }

  /**
   * Check if user is authenticated
   */
  private async isAuthenticated(): Promise<boolean> {
    try {
      return await AuthUtils.isAuthenticated(3);
    } catch (error) {
      logger.error('Authentication check failed', error, 'DataService');
      return false;
    }
  }

  /**
   * Handle Supabase errors with proper logging and user-friendly messages
   */
  private handleError(error: any, operation: string): never {
    logger.error(`DataService ${operation} failure: ${error.message || error}`, error, 'DataService');
    
    if (error.code === 'PGRST301') {
      throw new Error('Access denied. Please check your permissions.');
    } else if (error.code === 'PGRST116') {
      throw new Error('Requested data not found.');
    } else if (error.message?.includes('JWT')) {
      throw new Error('Authentication required. Please sign in again.');
    } else {
      throw new Error(`Operation failed: ${error.message || 'Unknown error'}`);
    }
  }

  // ==================== USER MANAGEMENT ====================

  /**
   * Create or update user profile with validation
   */
  async createOrUpdateUser(userData: Partial<User>): Promise<User> {
    try {
      const userId = await this.getCurrentUserId();
      
      const { data, error } = await supabase
        .from('profiles')
        .upsert({
          id: userId,
          email: userData.email,
          full_name: userData.name,
          avatar_url: userData.profileImage,
          updated_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (error) throw error;

      // Clear cache for this user
      cacheManager.delete(`user_${userId}`);
      
      logger.info('User profile updated successfully', { userId });
      
      return {
        id: data.id,
        email: data.email,
        name: data.full_name || 'User',
        profileImage: data.avatar_url,
        subscriptionTier: 'free',
        createdAt: new Date(data.created_at),
        lastActiveAt: new Date(data.updated_at),
        preferences: {
          language: 'en',
          notifications: true,
          yogaExperience: 'beginner',
          focusAreas: ['general_wellness'],
        },
      } as User;
    } catch (error) {
      this.handleError(error, 'createOrUpdateUser');
    }
  }

  /**
   * Get user profile with caching
   */
  async getUser(userId?: string): Promise<User | null> {
    try {
      const targetUserId = userId || await this.getCurrentUserId();
      const cacheKey = `user_${targetUserId}`;
      
      // Check cache first
      const cachedUser = cacheManager.get<User>(cacheKey);
      if (cachedUser) {
        return cachedUser;
      }

      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', targetUserId)
        .single();
      
      if (error) {
        if (error.code === 'PGRST116') return null; // Not found
        throw error;
      }

      const userData: User = {
        id: data.id,
        email: data.email,
        name: data.full_name || 'User',
        profileImage: data.avatar_url,
        subscriptionTier: 'free',
        createdAt: new Date(data.created_at),
        lastActiveAt: new Date(data.updated_at),
        preferences: {
          language: 'en',
          notifications: true,
          yogaExperience: 'beginner',
          focusAreas: ['general_wellness'],
        },
      };
      
      // Cache for 5 minutes
      cacheManager.set(cacheKey, userData, 5 * 60 * 1000);
      
      return userData;
    } catch (error) {
      this.handleError(error, 'getUser');
    }
  }

  /**
   * Listen to real-time user updates
   */
  subscribeToUser(userId: string, callback: (user: User | null) => void): () => void {
    try {
      const subscription = supabase
        .channel(`user_${userId}`)
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'profiles',
            filter: `id=eq.${userId}`,
          },
          async () => {
            // Refetch user data when changes occur
            const user = await this.getUser(userId);
            callback(user);
          }
        )
        .subscribe();

      this.subscriptions.push(subscription);
      return () => subscription.unsubscribe();
    } catch (error) {
      this.handleError(error, 'subscribeToUser');
    }
  }

  // ==================== POSTURE ANALYTICS ====================

  /**
   * Save posture analysis with validation
   */
  async savePostureAnalysis(analysis: Omit<PostureAnalysis, 'id' | 'createdAt'>): Promise<PostureAnalysis> {
    try {
      const userId = await this.getCurrentUserId();
      
      // Convert to the format expected by our database
      const sessionData = {
        overallScore: analysis.overallScore,
        neckAngle: analysis.neckAngle,
        shoulderAlignment: analysis.shoulderAlignment,
        spineAlignment: analysis.spineAlignment,
        hipAlignment: analysis.hipAlignment,
        issues: analysis.issues,
        recommendations: analysis.recommendations,
        landmarks: analysis.landmarks,
        imageUrl: analysis.imageUrl,
        deviceInfo: analysis.deviceInfo,
        environmentalFactors: analysis.environmentalFactors,
      };

      const session = await DatabaseService.savePostureSession({
        user_id: userId,
        session_data: sessionData,
        duration: analysis.duration || 0,
        score: analysis.overallScore,
      });
      
      // Clear related caches
      cacheManager.delete(`user_analytics_${userId}`);
      cacheManager.delete(`recent_analyses_${userId}`);
      
      logger.info('Posture analysis saved', { userId, sessionId: session.id });
      
      return {
        id: session.id!,
        userId,
        overallScore: analysis.overallScore,
        neckAngle: analysis.neckAngle,
        shoulderAlignment: analysis.shoulderAlignment,
        spineAlignment: analysis.spineAlignment,
        hipAlignment: analysis.hipAlignment,
        recommendations: analysis.recommendations,
        landmarks: analysis.landmarks,
        imageUrl: analysis.imageUrl,
        sessionDuration: analysis.duration || 0,
        duration: analysis.duration,
        issues: analysis.issues,
        deviceInfo: analysis.deviceInfo,
        environmentalFactors: analysis.environmentalFactors,
        createdAt: new Date(session.created_at!),
      } as PostureAnalysis;
    } catch (error) {
      this.handleError(error, 'savePostureAnalysis');
    }
  }

  /**
   * Get user's posture analyses with pagination
   */
  async getPostureAnalyses(
    userId?: string,
    limitCount: number = 20
  ): Promise<PostureAnalysis[]> {
    try {
      if (!await this.isAuthenticated()) {
        throw new Error('User not authenticated');
      }

      const targetUserId = userId || await this.getCurrentUserId();
      const cacheKey = `recent_analyses_${targetUserId}_${limitCount}`;
      
      const cached = await cacheManager.get<PostureAnalysis[]>(cacheKey);
      if (cached && Array.isArray(cached)) {
        logger.debug('Returning cached posture analyses', { count: cached.length }, 'DataService');
        return cached;
      }

      const sessions = await DatabaseService.getUserSessions(targetUserId, limitCount);
      
      const analyses: PostureAnalysis[] = sessions.map(session => ({
        id: session.id!,
        userId: session.user_id,
        overallScore: session.score || 0,
        neckAngle: session.session_data?.neckAngle || 0,
        shoulderAlignment: session.session_data?.shoulderAlignment || 0,
        spineAlignment: session.session_data?.spineAlignment || 0,
        hipAlignment: session.session_data?.hipAlignment || 0,
        recommendations: session.session_data?.recommendations || [],
        landmarks: session.session_data?.landmarks || [],
        imageUrl: session.session_data?.imageUrl,
        sessionDuration: session.duration,
        duration: session.duration,
        issues: session.session_data?.issues || [],
        deviceInfo: session.session_data?.deviceInfo,
        environmentalFactors: session.session_data?.environmentalFactors,
        createdAt: new Date(session.created_at!),
      }));

      cacheManager.set(cacheKey, analyses, 2 * 60 * 1000); // Cache for 2 minutes
      
      logger.info('Posture analyses loaded successfully', { count: analyses.length, userId: targetUserId }, 'DataService');
      return analyses;
    } catch (error) {
      this.handleError(error, 'getPostureAnalyses');
    }
  }

  /**
   * Get real-time posture analytics
   */
  subscribeToPostureAnalytics(
    userId: string,
    callback: (analytics: UserAnalytics) => void
  ): () => void {
    try {
      if (!this.isAuthenticated()) {
        throw new Error('User not authenticated');
      }

      const subscription = DatabaseService.subscribeToUserSessions(userId, async (sessions) => {
        try {
          // Convert sessions to analyses format
          const analyses: PostureAnalysis[] = sessions.map(session => ({
            id: session.id!,
            userId: session.user_id,
            overallScore: session.score || 0,
            neckAngle: session.session_data?.neckAngle || 0,
            shoulderAlignment: session.session_data?.shoulderAlignment || 0,
            spineAlignment: session.session_data?.spineAlignment || 0,
            hipAlignment: session.session_data?.hipAlignment || 0,
            recommendations: session.session_data?.recommendations || [],
            landmarks: session.session_data?.landmarks || [],
            imageUrl: session.session_data?.imageUrl,
            sessionDuration: session.duration,
            duration: session.duration,
            issues: session.session_data?.issues || [],
            deviceInfo: session.session_data?.deviceInfo,
            environmentalFactors: session.session_data?.environmentalFactors,
            createdAt: new Date(session.created_at!),
          }));

          // Calculate analytics
          const analytics = this.calculateUserAnalytics(analyses);
          cacheManager.set(`user_analytics_${userId}`, analytics, 5 * 60 * 1000);
          callback(analytics);
          logger.debug('Posture analytics updated via subscription', { analysesCount: analyses.length }, 'DataService');
        } catch (error) {
          logger.error('Error processing posture analytics subscription data', error, 'DataService');
        }
      });

      this.subscriptions.push(subscription);
      return () => subscription.unsubscribe();
    } catch (error) {
      logger.error('Posture analytics subscription error', error, 'DataService');
      // Return a no-op function if subscription fails
      return () => {};
    }
  }

  // ==================== YOGA SESSIONS ====================

  /**
   * Save yoga session with validation
   */
  async saveYogaSession(session: Omit<YogaSession, 'id' | 'createdAt'>): Promise<YogaSession> {
    try {
      const userId = await this.getCurrentUserId();
      
      // For now, store yoga sessions as posture sessions with yoga-specific data
      const sessionData = {
        type: 'yoga',
        exerciseId: session.exerciseId,
        exerciseName: session.exerciseName,
        difficulty: session.difficulty,
        completedPoses: session.completedPoses,
        overallAccuracy: session.overallAccuracy,
        caloriesBurned: session.caloriesBurned,
        heartRateData: session.heartRateData,
        feedback: session.feedback,
      };

      const savedSession = await DatabaseService.savePostureSession({
        user_id: userId,
        session_data: sessionData,
        duration: session.duration,
        score: session.overallAccuracy,
      });
      
      // Clear related caches
      cacheManager.delete(`user_analytics_${userId}`);
      cacheManager.delete(`recent_sessions_${userId}`);
      
      logger.info('Yoga session saved', { userId, sessionId: savedSession.id });
      
      return {
        id: savedSession.id!,
        userId,
        exerciseId: session.exerciseId,
        exerciseName: session.exerciseName,
        duration: session.duration,
        difficulty: session.difficulty,
        completedPoses: session.completedPoses,
        overallAccuracy: session.overallAccuracy,
        caloriesBurned: session.caloriesBurned,
        heartRateData: session.heartRateData,
        feedback: session.feedback,
        createdAt: new Date(savedSession.created_at!),
      } as YogaSession;
    } catch (error) {
      this.handleError(error, 'saveYogaSession');
    }
  }

  /**
   * Get user's yoga sessions
   */
  async getYogaSessions(
    userId?: string,
    limitCount: number = 20
  ): Promise<YogaSession[]> {
    try {
      if (!await this.isAuthenticated()) {
        throw new Error('User not authenticated');
      }

      const targetUserId = userId || await this.getCurrentUserId();
      const cacheKey = `recent_sessions_${targetUserId}_${limitCount}`;
      
      const cached = await cacheManager.get<YogaSession[]>(cacheKey);
      if (cached && Array.isArray(cached)) {
        logger.debug('Returning cached yoga sessions', { count: cached.length }, 'DataService');
        return cached;
      }

      const sessions = await DatabaseService.getUserSessions(targetUserId, limitCount);
      
      // Filter for yoga sessions and convert format
      const yogaSessions: YogaSession[] = sessions
        .filter(session => session.session_data?.type === 'yoga')
        .map(session => ({
          id: session.id!,
          userId: session.user_id,
          exerciseId: session.session_data?.exerciseId || '',
          exerciseName: session.session_data?.exerciseName || '',
          duration: session.duration,
          difficulty: session.session_data?.difficulty || 'beginner',
          completedPoses: session.session_data?.completedPoses || [],
          overallAccuracy: session.score || 0,
          caloriesBurned: session.session_data?.caloriesBurned || 0,
          heartRateData: session.session_data?.heartRateData,
          feedback: session.session_data?.feedback,
          createdAt: new Date(session.created_at!),
        }));

      cacheManager.set(cacheKey, yogaSessions, 2 * 60 * 1000);
      logger.info('Yoga sessions loaded successfully', { count: yogaSessions.length, userId: targetUserId }, 'DataService');
      return yogaSessions;
    } catch (error) {
      this.handleError(error, 'getYogaSessions');
    }
  }

  // ==================== ANALYTICS CALCULATIONS ====================

  /**
   * Calculate user analytics from posture analyses
   */
  private calculateUserAnalytics(analyses: PostureAnalysis[]): UserAnalytics {
    if (analyses.length === 0) {
      return {
        totalSessions: 0,
        averageScore: 0,
        improvementRate: 0,
        weeklyProgress: [],
        monthlyProgress: [],
        streakDays: 0,
        lastSessionDate: null,
        totalMinutes: 0,
        bestScore: 0,
        problemAreas: [],
        recommendations: [],
      };
    }

    const totalSessions = analyses.length;
    const averageScore = analyses.reduce((sum, a) => sum + a.overallScore, 0) / totalSessions;
    
    // Calculate improvement rate (last 10 vs previous 10)
    const recent = analyses.slice(0, 10);
    const previous = analyses.slice(10, 20);
    const recentAvg = recent.reduce((sum, a) => sum + a.overallScore, 0) / recent.length;
    const previousAvg = previous.length > 0 
      ? previous.reduce((sum, a) => sum + a.overallScore, 0) / previous.length 
      : recentAvg;
    const improvementRate = previousAvg > 0 ? ((recentAvg - previousAvg) / previousAvg) * 100 : 0;

    // Calculate weekly and monthly progress
    const weeklyProgress = this.calculateWeeklyProgress(analyses);
    const monthlyProgress = this.calculateMonthlyProgress(analyses);
    
    // Calculate streak days
    const streakDays = this.calculateStreakDays(analyses);
    
    const lastSessionDate = analyses[0]?.createdAt || null;

    return {
      totalSessions,
      averageScore: Math.round(averageScore * 100) / 100,
      improvementRate: Math.round(improvementRate * 100) / 100,
      weeklyProgress,
      monthlyProgress,
      streakDays,
      lastSessionDate,
      totalMinutes: analyses.reduce((total, analysis) => total + (analysis.duration || 0), 0),
      bestScore: Math.max(...analyses.map(a => a.overallScore), 0),
      problemAreas: this.extractProblemAreas(analyses),
      recommendations: this.generateRecommendations(analyses),
    };
  }

  /**
   * Extract common problem areas from analyses
   */
  private extractProblemAreas(analyses: PostureAnalysis[]): string[] {
    const problemCounts: { [key: string]: number } = {};
    
    analyses.forEach(analysis => {
      if (analysis.issues) {
        analysis.issues.forEach(issue => {
          problemCounts[issue.type] = (problemCounts[issue.type] || 0) + 1;
        });
      }
    });
    
    return Object.entries(problemCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .map(([problem]) => problem);
  }

  /**
   * Generate recommendations based on analyses
   */
  private generateRecommendations(analyses: PostureAnalysis[]): string[] {
    const recommendations: string[] = [];
    const problemAreas = this.extractProblemAreas(analyses);
    
    if (problemAreas.includes('forward_head')) {
      recommendations.push('Keep your screen at eye level to reduce forward head posture');
    }
    if (problemAreas.includes('rounded_shoulders')) {
      recommendations.push('Take regular breaks to stretch your shoulders and chest');
    }
    if (problemAreas.includes('hunched_back')) {
      recommendations.push('Focus on maintaining a straight spine while sitting');
    }
    
    if (recommendations.length === 0) {
      recommendations.push('Keep up the great work! Continue with regular posture checks');
    }
    
    return recommendations.slice(0, 3);
  }

  private calculateWeeklyProgress(analyses: PostureAnalysis[]): DailyProgress[] {
    const weeklyData: { [key: string]: { total: number; count: number } } = {};
    const now = new Date();
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    analyses
      .filter(a => a.createdAt && a.createdAt >= oneWeekAgo)
      .forEach(analysis => {
        const date = analysis.createdAt.toISOString().split('T')[0];
        if (!weeklyData[date]) {
          weeklyData[date] = { total: 0, count: 0 };
        }
        weeklyData[date].total += analysis.overallScore;
        weeklyData[date].count += 1;
      });

    return Object.entries(weeklyData).map(([date, data]) => ({
      date,
      score: Math.round((data.total / data.count) * 100) / 100,
      sessionsCount: data.count,
    }));
  }

  private calculateMonthlyProgress(analyses: PostureAnalysis[]): DailyProgress[] {
    const monthlyData: { [key: string]: { total: number; count: number } } = {};
    const now = new Date();
    const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    analyses
      .filter(a => a.createdAt && a.createdAt >= oneMonthAgo)
      .forEach(analysis => {
        const date = analysis.createdAt.toISOString().split('T')[0];
        if (!monthlyData[date]) {
          monthlyData[date] = { total: 0, count: 0 };
        }
        monthlyData[date].total += analysis.overallScore;
        monthlyData[date].count += 1;
      });

    return Object.entries(monthlyData).map(([date, data]) => ({
      date,
      score: Math.round((data.total / data.count) * 100) / 100,
      sessionsCount: data.count,
    }));
  }

  private calculateStreakDays(analyses: PostureAnalysis[]): number {
    if (analyses.length === 0) return 0;

    const dates = analyses
      .map(a => a.createdAt.toISOString().split('T')[0])
      .filter((date, index, arr) => arr.indexOf(date) === index)
      .sort((a, b) => new Date(b).getTime() - new Date(a).getTime());

    let streak = 0;
    const today = new Date().toISOString().split('T')[0];
    const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0];

    // Check if user has activity today or yesterday
    if (dates[0] !== today && dates[0] !== yesterday) {
      return 0;
    }

    for (let i = 0; i < dates.length; i++) {
      const currentDate = new Date(dates[i]);
      const expectedDate = new Date(Date.now() - i * 24 * 60 * 60 * 1000);
      
      if (currentDate.toISOString().split('T')[0] === expectedDate.toISOString().split('T')[0]) {
        streak++;
      } else {
        break;
      }
    }

    return streak;
  }

  // ==================== QUESTIONNAIRE & PREFERENCES ====================

  /**
   * Save questionnaire session
   */
  async saveQuestionnaireSession(session: any): Promise<void> {
    try {
      const { error } = await supabase
        .from('questionnaire_sessions')
        .insert({
          id: session.id,
          user_id: session.userId,
          session_type: session.type || 'onboarding',
          started_at: session.startedAt,
          completed_at: session.completedAt,
          completion_percentage: session.completionPercentage || 0,
          total_questions: session.totalQuestions || 0,
          answered_questions: session.responses?.length || 0,
          session_data: session.sessionData || session,
        });

      if (error) throw error;
      logger.info('Questionnaire session saved', { sessionId: session.id });
    } catch (error) {
      this.handleError(error, 'saveQuestionnaireSession');
    }
  }

  /**
   * Get last questionnaire session for user
   */
  async getLastQuestionnaireSession(userId: string): Promise<any | null> {
    try {
      const { data, error } = await supabase
        .from('questionnaire_sessions')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(1)
        .single();
      
      if (error) {
        if (error.code === 'PGRST116') return null; // Not found
        throw error;
      }
      
      return data.session_data;
    } catch (error: any) {
      logger.error('Failed to get last questionnaire session', error);
      return null;
    }
  }

  /**
   * Update user preferences
   */
  async updateUserPreferences(userId: string, preferences: Partial<any>): Promise<void> {
    try {
      const { error } = await supabase
        .from('profiles')
        .update({
          preferences: preferences,
          updated_at: new Date().toISOString(),
        })
        .eq('id', userId);
      
      if (error) throw error;
      
      // Update cache
      const cacheKey = `user_${userId}`;
      cacheManager.delete(cacheKey);
      
      logger.info('User preferences updated', { userId });
    } catch (error) {
      this.handleError(error, 'updateUserPreferences');
    }
  }

  async updateQuestionnaireSession(session: any): Promise<void> {
    try {
      const { error } = await supabase
        .from('questionnaire_sessions')
        .update({
          completed_at: session.completedAt,
          completion_percentage: session.completionPercentage,
          answered_questions: session.responses?.length || 0,
          session_data: session.sessionData || session,
        })
        .eq('id', session.id);

      if (error) throw error;
      logger.info('Questionnaire session updated', { sessionId: session.id });
    } catch (error) {
      this.handleError(error, 'updateQuestionnaireSession');
    }
  }

  async saveQuestionnaireResponse(response: any): Promise<string> {
    try {
      const { data, error } = await supabase
        .from('questionnaire_responses')
        .insert({
          // Let Supabase auto-generate the ID
          session_id: response.sessionId,
          user_id: response.userId,
          question_id: response.questionId,
          question_category: response.questionCategory,
          question_text: response.questionText,
          answer_value: response.answerValue,
          answer_data: response.answerData || {},
          confidence_level: response.confidenceLevel,
          response_time_ms: response.responseTimeMs,
        })
        .select('id')
        .single();

      if (error) throw error;

      const generatedId = data.id;
      logger.info('Questionnaire response saved', { responseId: generatedId });
      return generatedId;
    } catch (error) {
      this.handleError(error, 'saveQuestionnaireResponse');
      throw error;
    }
  }

  async getUserPreferences(userId: string): Promise<any> {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('preferences')
        .eq('id', userId)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      return data?.preferences || {};
    } catch (error) {
      logger.error('Failed to get user preferences', error);
      return {};
    }
  }

  async saveUserInsights(userId: string, insights: any[]): Promise<void> {
    try {
      const insightData = insights.map(insight => ({
        id: insight.id,
        user_id: userId,
        insight_category: insight.category,
        insight_type: insight.type || 'general',
        title: insight.title,
        description: insight.description,
        severity: insight.severity,
        priority: insight.priority || 5,
        actionable: insight.actionable,
        data: insight.data || {},
      }));

      const { error } = await supabase
        .from('user_insights')
        .insert(insightData);

      if (error) throw error;
      logger.info('User insights saved', { userId, count: insights.length });
    } catch (error) {
      this.handleError(error, 'saveUserInsights');
    }
  }

  async getUserInsights(userId: string): Promise<any[]> {
    try {
      const { data, error } = await supabase
        .from('user_insights')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      this.handleError(error, 'getUserInsights');
      return [];
    }
  }

  async savePersonalizedRecommendations(userId: string, recommendations: any[]): Promise<void> {
    try {
      const recommendationData = recommendations.map(rec => ({
        id: rec.id,
        user_id: userId,
        recommendation_type: rec.type,
        title: rec.title,
        description: rec.description,
        priority: typeof rec.priority === 'number' ? rec.priority : 5,
        estimated_time_minutes: rec.estimatedTimeMinutes,
        difficulty_level: rec.difficultyLevel || 'beginner',
        category: rec.category,
        tags: rec.tags || [],
        completion_status: rec.completionStatus || 'pending',
        data: rec.data || {},
      }));

      const { error } = await supabase
        .from('personalized_recommendations')
        .insert(recommendationData);

      if (error) throw error;
      logger.info('Personalized recommendations saved', { userId, count: recommendations.length });
    } catch (error) {
      this.handleError(error, 'savePersonalizedRecommendations');
    }
  }

  async updateUserProfile(userId: string, updates: any): Promise<void> {
    try {
      const { error } = await supabase
        .from('profiles')
        .update(updates)
        .eq('id', userId);

      if (error) throw error;
      logger.info('User profile updated', { userId });
    } catch (error) {
      this.handleError(error, 'updateUserProfile');
    }
  }

  // ==================== CLEANUP ====================

  /**
   * Clean up all subscriptions
   */
  cleanup(): void {
    this.subscriptions.forEach(subscription => subscription.unsubscribe());
    this.subscriptions = [];
    cacheManager.clear();
  }
}

// Export singleton instance
export const dataService = DataService.getInstance();