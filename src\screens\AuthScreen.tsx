import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Dimensions,
  Animated,
  StatusBar,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { LinearGradient } from 'expo-linear-gradient';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useAuth } from '../contexts/AuthContext';
import { useTheme } from '../contexts/ThemeContext';
import Toast from '../components/Toast';
import { RootStackParamList } from '../types';
import { ValidationUtils } from '../utils/validation';

type AuthScreenNavigationProp = StackNavigationProp<RootStackParamList>;

const { width, height } = Dimensions.get('window');

const AuthScreen: React.FC = () => {
  const { t } = useTranslation();
  const { login, register, user, loading: authLoading } = useAuth();
  const { theme, setInputActive, switchTheme, animatedValues } = useTheme();
  const navigation = useNavigation<AuthScreenNavigationProp>();

  const [isLogin, setIsLogin] = useState(true);
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState(0);

  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    name: '',
  });

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;
  const buttonScale = useRef(new Animated.Value(1)).current;

  // Toast state
  const [toast, setToast] = useState({
    visible: false,
    message: '',
    type: 'info' as 'success' | 'error' | 'info' | 'warning',
  });

  const showToast = (message: string, type: 'success' | 'error' | 'info' | 'warning' = 'info') => {
    setToast({ visible: true, message, type });
  };

  const hideToast = () => {
    setToast(prev => ({ ...prev, visible: false }));
  };

  const resetForm = () => {
    setFormData({
      email: '',
      password: '',
      confirmPassword: '',
      name: '',
    });
    setPasswordStrength(0);
    setShowPassword(false);
    setShowConfirmPassword(false);
  };

  const handleModeSwitch = (loginMode: boolean) => {
    setIsLogin(loginMode);
    // Clear sensitive fields when switching modes
    setFormData(prev => ({
      ...prev,
      password: '',
      confirmPassword: '',
      name: loginMode ? '' : prev.name,
    }));
    setPasswordStrength(0);
    setShowPassword(false);
    setShowConfirmPassword(false);
    hideToast();
  };

  React.useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.spring(slideAnim, {
        toValue: 0,
        tension: 80,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const handleInputChange = (field: string, value: string) => {
    // Handle password field specially for strength calculation
    if (field === 'password') {
      setFormData(prev => ({ ...prev, [field]: value }));
      if (!isLogin) {
        const strength = ValidationUtils.getPasswordStrength(value);
        setPasswordStrength(strength.score);
      }
    } else if (field === 'name') {
      // For name field, allow spaces and don't sanitize in real-time
      // Only basic filtering to prevent excessive spaces
      const cleanValue = value.replace(/\s{3,}/g, '  '); // Limit to max 2 consecutive spaces
      setFormData(prev => ({ ...prev, [field]: cleanValue }));
    } else {
      // Sanitize other inputs (email, etc.)
      const sanitizedValue = field === 'email'
        ? value.trim().toLowerCase()
        : ValidationUtils.sanitizeInput(value);
      setFormData(prev => ({ ...prev, [field]: sanitizedValue }));
    }
  };

  const validateForm = () => {
    // Email validation
    if (!formData.email.trim()) {
      showToast('Please enter your email address', 'error');
      return false;
    }

    if (!ValidationUtils.validateEmail(formData.email.trim())) {
      showToast('Please enter a valid email address', 'error');
      return false;
    }

    // Password validation
    if (!formData.password.trim()) {
      showToast('Please enter your password', 'error');
      return false;
    }

    // Consistent password requirements for both login and signup
    const minPasswordLength = isLogin ? 6 : 8;
    if (formData.password.length < minPasswordLength) {
      showToast(`Password must be at least ${minPasswordLength} characters`, 'error');
      return false;
    }

    // Additional validation for signup
    if (!isLogin) {
      // Name validation
      if (!formData.name.trim()) {
        showToast('Please enter your full name', 'error');
        return false;
      }

      const nameValidation = ValidationUtils.validateName(formData.name.trim());
      if (!nameValidation.isValid) {
        showToast('Please enter a valid name (2-50 characters)', 'error');
        return false;
      }

      // Strong password validation for signup
      const passwordValidation = ValidationUtils.validatePassword(formData.password);
      if (!passwordValidation.isValid) {
        showToast('Password must contain at least 8 characters with letters and numbers', 'error');
        return false;
      }

      // Confirm password validation
      if (!formData.confirmPassword.trim()) {
        showToast('Please confirm your password', 'error');
        return false;
      }

      if (formData.password !== formData.confirmPassword) {
        showToast('Passwords don\'t match. Please try again', 'error');
        return false;
      }
    }

    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setLoading(true);

    // Button press animation
    Animated.sequence([
      Animated.spring(buttonScale, {
        toValue: 0.96,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
      Animated.spring(buttonScale, {
        toValue: 1,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();

    try {
      if (isLogin) {
        await login(formData.email.trim().toLowerCase(), formData.password);
        showToast('Welcome back! 👋', 'success');
      } else {
        const newUser = await register(
          formData.email.trim().toLowerCase(),
          formData.password,
          formData.name.trim()
        );
        showToast('Account created successfully! 🎉', 'success');

        // Clear form after successful registration
        setTimeout(async () => {
          setFormData({
            email: '',
            password: '',
            confirmPassword: '',
            name: '',
          });
          setPasswordStrength(0);

          // Wait for auth state to fully propagate before navigation
          await new Promise(resolve => setTimeout(resolve, 2000));

          // Check if email confirmation is required
          try {
            const { AuthUtils } = await import('../utils/authUtils');
            const confirmationStatus = await AuthUtils.getEmailConfirmationStatus();

            if (!confirmationStatus.isConfirmed) {
              // Navigate to email confirmation screen
              navigation.replace('EmailConfirmation');
            } else {
              // Redirect new users to onboarding questionnaire
              if (newUser.needsOnboardingQuestionnaire) {
                navigation.replace('Questionnaire', { type: 'onboarding' });
              }
            }
          } catch (error) {

            // Fallback: proceed to questionnaire
            if (newUser.needsOnboardingQuestionnaire) {
              navigation.replace('Questionnaire', { type: 'onboarding' });
            }
          }
        }, 1500);
      }
    } catch (error: any) {

      
      let errorMessage = 'Unable to complete request. Please try again.';

      // Handle Supabase error format
      const errorCode = error?.code || error?.error_description || error?.message;
      
      if (typeof errorCode === 'string') {
        const lowerErrorCode = errorCode.toLowerCase();
        
        if (lowerErrorCode.includes('invalid login credentials') || 
            lowerErrorCode.includes('invalid_credentials') ||
            lowerErrorCode.includes('user not found') ||
            lowerErrorCode.includes('wrong password')) {
          errorMessage = 'Incorrect email or password. Please try again.';
        } else if (lowerErrorCode.includes('email already registered') ||
                   lowerErrorCode.includes('user already registered') ||
                   lowerErrorCode.includes('email_already_in_use')) {
          errorMessage = 'This email is already registered. Try signing in instead.';
        } else if (lowerErrorCode.includes('weak password') ||
                   lowerErrorCode.includes('password should be at least')) {
          errorMessage = 'Please choose a stronger password with at least 8 characters.';
        } else if (lowerErrorCode.includes('invalid email') ||
                   lowerErrorCode.includes('email_invalid')) {
          errorMessage = 'Please enter a valid email address.';
        } else if (lowerErrorCode.includes('network') ||
                   lowerErrorCode.includes('connection')) {
          errorMessage = 'Connection issue. Please check your internet and try again.';
        } else if (lowerErrorCode.includes('too many requests') ||
                   lowerErrorCode.includes('rate limit')) {
          errorMessage = 'Too many attempts. Please wait a few minutes before trying again.';
        } else if (lowerErrorCode.includes('user disabled') ||
                   lowerErrorCode.includes('account disabled')) {
          errorMessage = 'This account has been temporarily disabled. Contact support if needed.';
        } else if (lowerErrorCode.includes('signup disabled') ||
                   lowerErrorCode.includes('signups not allowed')) {
          errorMessage = 'New registrations are temporarily disabled. Please try again later.';
        } else if (lowerErrorCode.includes('email not confirmed')) {
          errorMessage = 'Please check your email and click the confirmation link before signing in.';
        }
      }

      showToast(errorMessage, 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleForgotPassword = async () => {
    if (!formData.email.trim()) {
      showToast('Please enter your email address first', 'error');
      return;
    }

    if (!ValidationUtils.validateEmail(formData.email.trim())) {
      showToast('Please enter a valid email address', 'error');
      return;
    }

    try {
      setLoading(true);
      const { AuthService } = await import('../services/authService');
      await AuthService.resetPassword(formData.email.trim().toLowerCase());
      showToast('If this email is registered, you\'ll receive a reset link shortly 📧', 'success');
    } catch (error: any) {

      // Always show the same message to prevent email enumeration attacks
      showToast('If this email is registered, you\'ll receive a reset link shortly 📧', 'success');
    } finally {
      setLoading(false);
    }
  };

  // Handle navigation after successful authentication
  useEffect(() => {
    if (user && !authLoading) {
      setTimeout(() => {
        navigation.reset({
          index: 0,
          routes: [{ name: 'Main' }],
        });
      }, 1000);
    }
  }, [user, authLoading, navigation]);

  const renderInput = (
    placeholder: string,
    value: string,
    onChangeText: (text: string) => void,
    options: {
      secureTextEntry?: boolean;
      keyboardType?: 'default' | 'email-address' | 'phone-pad';
      autoCapitalize?: 'none' | 'sentences' | 'words' | 'characters';
      rightIcon?: string;
      onRightIconPress?: () => void;
      testID?: string;
      autoComplete?: string;
    } = {}
  ) => {
    return (
      <View style={styles.inputContainer}>
        <TextInput
          style={[styles.input, loading && styles.inputDisabled]}
          placeholder={placeholder}
          placeholderTextColor="rgba(255, 255, 255, 0.5)"
          value={value}
          onChangeText={onChangeText}
          secureTextEntry={options.secureTextEntry}
          keyboardType={options.keyboardType || 'default'}
          autoCapitalize={options.autoCapitalize || 'none'}
          autoCorrect={placeholder === 'Full name' ? true : false}
          spellCheck={placeholder === 'Full name' ? true : false}
          editable={!loading}
          testID={options.testID}
          autoComplete={options.autoComplete as any}
          textContentType={options.autoComplete as any}
        />
        {options.rightIcon && (
          <TouchableOpacity 
            onPress={options.onRightIconPress} 
            style={styles.rightIconContainer}
            disabled={loading}
            accessibilityLabel={options.secureTextEntry ? 'Show password' : 'Hide password'}
          >
            <Ionicons
              name={options.rightIcon as any}
              size={20}
              color="rgba(255, 255, 255, 0.6)"
            />
          </TouchableOpacity>
        )}
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#000000" />

      {/* Background gradient */}
      <LinearGradient
        colors={['#000000', '#1a1a1a', '#2d2d2d', '#000000']}
        style={StyleSheet.absoluteFillObject}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      />

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          bounces={false}
        >
          {/* Claude-style Header */}
          <Animated.View
            style={[
              styles.header,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }],
              },
            ]}
          >
            <View style={styles.logoContainer}>
              <Image
                source={require('../../assets/logo.jpeg')}
                style={styles.logo}
                resizeMode="contain"
              />
            </View>
            <Text style={styles.welcomeText}>
              {isLogin ? 'Welcome back' : 'Get started'}
            </Text>
            <Text style={styles.subtitle}>
              {isLogin ? 'Sign in to your account' : 'Create your account'}
            </Text>
          </Animated.View>

          {/* Main Form Container */}
          <Animated.View
            style={[
              styles.formContainer,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }],
              },
            ]}
          >
            {/* Auth Mode Toggle */}
            <View style={styles.authModeContainer}>
              <TouchableOpacity
                style={[styles.authModeButton, isLogin && styles.authModeActive]}
                onPress={() => handleModeSwitch(true)}
                activeOpacity={0.7}
                disabled={loading}
              >
                <Text style={[styles.authModeText, isLogin && styles.authModeTextActive]}>
                  Sign in
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.authModeButton, !isLogin && styles.authModeActive]}
                onPress={() => handleModeSwitch(false)}
                activeOpacity={0.7}
                disabled={loading}
              >
                <Text style={[styles.authModeText, !isLogin && styles.authModeTextActive]}>
                  Sign up
                </Text>
              </TouchableOpacity>
            </View>

            {/* Form Fields */}
            <View style={styles.inputSection}>
              {!isLogin && (
                renderInput(
                  'Full name',
                  formData.name,
                  (value) => handleInputChange('name', value),
                  {
                    autoCapitalize: 'words',
                    testID: 'name-input',
                    autoComplete: 'name',
                  }
                )
              )}

              {renderInput(
                'Email',
                formData.email,
                (value) => handleInputChange('email', value),
                {
                  keyboardType: 'email-address',
                  testID: 'email-input',
                  autoComplete: 'email',
                }
              )}

              <View>
                {renderInput(
                  'Password',
                  formData.password,
                  (value) => handleInputChange('password', value),
                  {
                    secureTextEntry: !showPassword,
                    rightIcon: showPassword ? 'eye-off-outline' : 'eye-outline',
                    onRightIconPress: () => setShowPassword(!showPassword),
                    testID: 'password-input',
                    autoComplete: isLogin ? 'current-password' : 'new-password',
                  }
                )}

                {/* Password Strength - Claude style */}
                {!isLogin && formData.password.length > 0 && (
                  <View style={styles.passwordStrengthContainer}>
                    <View style={styles.passwordStrengthBar}>
                      <View
                        style={[
                          styles.passwordStrengthFill,
                          {
                            width: `${(passwordStrength / 5) * 100}%`,
                            backgroundColor: ValidationUtils.getPasswordStrength(formData.password).color
                          }
                        ]}
                      />
                    </View>
                    <Text style={[styles.passwordStrengthText, { color: ValidationUtils.getPasswordStrength(formData.password).color }]}>
                      {ValidationUtils.getPasswordStrength(formData.password).label}
                    </Text>
                  </View>
                )}
              </View>

              {!isLogin && (
                renderInput(
                  'Confirm password',
                  formData.confirmPassword,
                  (value) => handleInputChange('confirmPassword', value),
                  {
                    secureTextEntry: !showConfirmPassword,
                    rightIcon: showConfirmPassword ? 'eye-off-outline' : 'eye-outline',
                    onRightIconPress: () => setShowConfirmPassword(!showConfirmPassword),
                    testID: 'confirm-password-input',
                    autoComplete: 'new-password',
                  }
                )
              )}
            </View>

            {/* Forgot Password */}
            {isLogin && (
              <TouchableOpacity
                style={styles.forgotPasswordButton}
                onPress={handleForgotPassword}
                activeOpacity={0.7}
              >
                <Text style={styles.forgotPasswordText}>Forgot your password?</Text>
              </TouchableOpacity>
            )}

            {/* Primary Action Button */}
            <Animated.View style={{ transform: [{ scale: buttonScale }] }}>
              <TouchableOpacity
                style={[styles.primaryButton, loading && styles.primaryButtonDisabled]}
                onPress={handleSubmit}
                disabled={loading}
                activeOpacity={0.8}
              >
                <Text style={styles.primaryButtonText}>
                  {loading ? 'Please wait...' : isLogin ? 'Sign in' : 'Create account'}
                </Text>
              </TouchableOpacity>
            </Animated.View>

            {/* Divider */}
            <View style={styles.dividerContainer}>
              <View style={styles.dividerLine} />
              <Text style={styles.dividerText}>or</Text>
              <View style={styles.dividerLine} />
            </View>

            {/* Social Login Options */}
            <View style={styles.socialContainer}>
              <TouchableOpacity
                style={styles.socialButton}
                activeOpacity={0.8}
                onPress={() => showToast('Google Sign-In coming soon! 🚀', 'info')}
              >
                <Ionicons name="logo-google" size={20} color="#FFFFFF" />
                <Text style={styles.socialButtonText}>Continue with Google</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.socialButton}
                activeOpacity={0.8}
                onPress={() => showToast('Apple Sign-In coming soon! 🍎', 'info')}
              >
                <Ionicons name="logo-apple" size={20} color="#FFFFFF" />
                <Text style={styles.socialButtonText}>Continue with Apple</Text>
              </TouchableOpacity>

              {/* Biometric Option */}
              {isLogin && (
                <TouchableOpacity
                  style={styles.biometricButton}
                  activeOpacity={0.7}
                  onPress={() => showToast('Biometric authentication coming soon! 👆', 'info')}
                >
                  <Ionicons name="finger-print" size={18} color="rgba(255, 255, 255, 0.8)" />
                  <Text style={styles.biometricText}>Use biometric</Text>
                </TouchableOpacity>
              )}
            </View>
          </Animated.View>

          {/* Footer */}
          <Animated.View
            style={[
              styles.footer,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }],
              },
            ]}
          >
            <View style={styles.footerTextContainer}>
              <Text style={styles.footerText}>By continuing, you agree to our </Text>
              <TouchableOpacity onPress={() => navigation.navigate('TermsPrivacy')}>
                <Text style={styles.footerLink}>Terms</Text>
              </TouchableOpacity>
              <Text style={styles.footerText}> and </Text>
              <TouchableOpacity onPress={() => navigation.navigate('TermsPrivacy')}>
                <Text style={styles.footerLink}>Privacy Policy</Text>
              </TouchableOpacity>
            </View>
          </Animated.View>
        </ScrollView>
      </KeyboardAvoidingView>

      {/* Toast Notification */}
      <Toast
        visible={toast.visible}
        message={toast.message}
        type={toast.type}
        onHide={hideToast}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  keyboardView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 20,
    paddingTop: Platform.OS === 'ios' ? 70 : 50,
    paddingBottom: 30,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  logoContainer: {
    marginBottom: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  logo: {
    width: 56,
    height: 56,
    borderRadius: 14,
  },
  welcomeText: {
    fontSize: 32,
    fontWeight: '300',
    color: '#FFFFFF',
    letterSpacing: -1,
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    fontWeight: '400',
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
    letterSpacing: -0.2,
  },
  formContainer: {
    flex: 1,
  },
  authModeContainer: {
    flexDirection: 'row',
    marginBottom: 32,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 8,
    padding: 2,
  },
  authModeButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 6,
  },
  authModeActive: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  authModeText: {
    fontSize: 15,
    fontWeight: '500',
    color: 'rgba(255, 255, 255, 0.6)',
    letterSpacing: -0.2,
  },
  authModeTextActive: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  inputSection: {
    gap: 16,
    marginBottom: 24,
  },
  inputContainer: {
    position: 'relative',
  },
  input: {
    backgroundColor: 'rgba(255, 255, 255, 0.08)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.12)',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 16,
    fontSize: 16,
    fontWeight: '400',
    color: '#FFFFFF',
    letterSpacing: -0.2,
  },
  inputDisabled: {
    opacity: 0.6,
    backgroundColor: 'rgba(255, 255, 255, 0.04)',
  },
  rightIconContainer: {
    position: 'absolute',
    right: 16,
    top: 16,
    padding: 4,
  },
  forgotPasswordButton: {
    alignItems: 'center',
    marginBottom: 24,
  },
  forgotPasswordText: {
    fontSize: 14,
    fontWeight: '500',
    color: 'rgba(255, 255, 255, 0.7)',
    letterSpacing: -0.1,
  },
  primaryButton: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    paddingVertical: 16,
    paddingHorizontal: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 24,
    shadowColor: '#FFFFFF',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  primaryButtonDisabled: {
    opacity: 0.6,
  },
  primaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    letterSpacing: -0.2,
  },
  dividerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  dividerText: {
    fontSize: 14,
    fontWeight: '500',
    color: 'rgba(255, 255, 255, 0.5)',
    marginHorizontal: 16,
    letterSpacing: -0.1,
  },
  socialContainer: {
    gap: 12,
  },
  socialButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.08)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.12)',
    borderRadius: 8,
    paddingVertical: 14,
    paddingHorizontal: 16,
  },
  socialButtonText: {
    fontSize: 15,
    fontWeight: '500',
    color: '#FFFFFF',
    marginLeft: 10,
    letterSpacing: -0.2,
  },
  biometricButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.08)',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  biometricText: {
    fontSize: 14,
    fontWeight: '500',
    color: 'rgba(255, 255, 255, 0.8)',
    marginLeft: 8,
    letterSpacing: -0.1,
  },
  footer: {
    alignItems: 'center',
    paddingTop: 32,
    paddingHorizontal: 20,
  },
  footerTextContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    alignItems: 'center',
  },
  footerText: {
    fontSize: 13,
    fontWeight: '400',
    color: 'rgba(255, 255, 255, 0.5)',
    textAlign: 'center',
    lineHeight: 20,
    letterSpacing: -0.1,
  },
  footerLink: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontWeight: '500',
    fontSize: 13,
    lineHeight: 20,
    letterSpacing: -0.1,
  },
  passwordStrengthContainer: {
    marginTop: 8,
  },
  passwordStrengthBar: {
    height: 2,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 1,
    marginBottom: 6,
  },
  passwordStrengthFill: {
    height: '100%',
    borderRadius: 1,
  },
  passwordStrengthText: {
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'right',
    letterSpacing: -0.1,
  },
});

export default AuthScreen;