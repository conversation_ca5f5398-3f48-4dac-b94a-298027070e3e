import { useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import { AuthService, AuthUser } from '../lib/auth';
import type { User, Session } from '@supabase/supabase-js';

export interface AuthState {
  user: User | null;
  profile: AuthUser | null;
  session: Session | null;
  loading: boolean;
}

export function useAuth() {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    profile: null,
    session: null,
    loading: true,
  });

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setAuthState(prev => ({
        ...prev,
        session,
        user: session?.user ?? null,
        loading: false,
      }));

      // Get user profile if user exists
      if (session?.user) {
        AuthService.getUserProfile(session.user.id).then(profile => {
          setAuthState(prev => ({
            ...prev,
            profile,
          }));
        });
      }
    });

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      setAuthState(prev => ({
        ...prev,
        session,
        user: session?.user ?? null,
        loading: false,
      }));

      // Get user profile when signed in
      if (session?.user) {
        const profile = await AuthService.getUserProfile(session.user.id);
        setAuthState(prev => ({
          ...prev,
          profile,
        }));
      } else {
        setAuthState(prev => ({
          ...prev,
          profile: null,
        }));
      }
    });

    return () => subscription.unsubscribe();
  }, []);

  const signIn = async (email: string, password: string) => {
    setAuthState(prev => ({ ...prev, loading: true }));
    try {
      const result = await AuthService.signIn(email, password);
      return result;
    } finally {
      setAuthState(prev => ({ ...prev, loading: false }));
    }
  };

  const signUp = async (email: string, password: string, fullName?: string) => {
    setAuthState(prev => ({ ...prev, loading: true }));
    try {
      const result = await AuthService.signUp(email, password, fullName);
      return result;
    } finally {
      setAuthState(prev => ({ ...prev, loading: false }));
    }
  };

  const signOut = async () => {
    setAuthState(prev => ({ ...prev, loading: true }));
    try {
      await AuthService.signOut();
    } finally {
      setAuthState(prev => ({ ...prev, loading: false }));
    }
  };

  const updateProfile = async (updates: Partial<AuthUser>) => {
    if (!authState.user) return null;
    
    try {
      const updatedProfile = await AuthService.updateProfile(authState.user.id, updates);
      setAuthState(prev => ({
        ...prev,
        profile: updatedProfile,
      }));
      return updatedProfile;
    } catch (error) {
      throw error;
    }
  };

  return {
    ...authState,
    signIn,
    signUp,
    signOut,
    updateProfile,
    isAuthenticated: !!authState.user,
  };
}