/**
 * Branding constants for PostureApp
 * Centralized branding assets and configurations
 */

// Logo and Icon Assets
export const BRANDING_ASSETS = {
  // Main logo with text (for headers, splash screens)
  LOGO_WITH_TEXT: require('../../assets/logo.svg'),
  
  // App icon (for navigation, small spaces)
  APP_ICON: require('../../assets/app-icon.svg'),
  
  // Legacy assets (fallback)
  LEGACY_ICON: require('../../assets/icon.png'),
  LEGACY_ADAPTIVE_ICON: require('../../assets/adaptive-icon.png'),
  LEGACY_FAVICON: require('../../assets/favicon.png'),
  LEGACY_SPLASH: require('../../assets/splash-icon.png'),
} as const;

// Brand Colors
export const BRAND_COLORS = {
  // Primary brand color
  PRIMARY: '#FF6B35',
  PRIMARY_LIGHT: '#FF8A65',
  PRIMARY_DARK: '#E55A2B',
  
  // Secondary/accent colors
  WELLNESS_GREEN: '#4ADE80',
  WELLNESS_GREEN_LIGHT: '#34D399',
  
  // Background colors for branding
  BRAND_BACKGROUND: '#FF6B35',
  BRAND_BACKGROUND_LIGHT: '#FFF7F5',
  
  // Text colors on brand backgrounds
  BRAND_TEXT_ON_PRIMARY: '#FFFFFF',
  BRAND_TEXT_ON_LIGHT: '#1F2937',
} as const;

// Logo Usage Guidelines
export const LOGO_USAGE = {
  // Minimum sizes (in pixels)
  MIN_SIZE_LOGO: 120,
  MIN_SIZE_ICON: 24,
  
  // Recommended sizes for different contexts
  SIZES: {
    SPLASH: { width: 200, height: 200 },
    HEADER: { width: 150, height: 150 },
    NAVIGATION: { width: 32, height: 32 },
    BUTTON: { width: 24, height: 24 },
    LARGE_DISPLAY: { width: 300, height: 300 },
  },
  
  // Clear space requirements (minimum padding around logo)
  CLEAR_SPACE: {
    LOGO: 20, // 20px minimum clear space
    ICON: 8,  // 8px minimum clear space
  },
} as const;

// Brand Typography
export const BRAND_TYPOGRAPHY = {
  // Font family for brand text
  BRAND_FONT: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
  
  // Font weights
  WEIGHTS: {
    LIGHT: '300',
    REGULAR: '400',
    MEDIUM: '500',
    SEMIBOLD: '600',
    BOLD: '700',
  },
  
  // Brand text styles
  STYLES: {
    LOGO_TEXT: {
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      fontWeight: '600',
      color: BRAND_COLORS.BRAND_TEXT_ON_LIGHT,
    },
    BRAND_HEADING: {
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      fontWeight: '700',
      color: BRAND_COLORS.PRIMARY,
    },
  },
} as const;

// App Store / Marketing Assets
export const MARKETING_ASSETS = {
  // App store screenshots sizes
  SCREENSHOT_SIZES: {
    IOS_6_7: { width: 1290, height: 2796 },
    IOS_6_5: { width: 1242, height: 2688 },
    IOS_5_5: { width: 1242, height: 2208 },
    ANDROID: { width: 1080, height: 1920 },
  },
  
  // Feature graphic sizes
  FEATURE_GRAPHIC: {
    ANDROID: { width: 1024, height: 500 },
    IOS_PREVIEW: { width: 1200, height: 675 },
  },
  
  // Icon sizes for different platforms
  ICON_SIZES: {
    IOS: [20, 29, 40, 58, 60, 76, 80, 87, 120, 152, 167, 180, 1024],
    ANDROID: [36, 48, 72, 96, 144, 192, 512],
    WEB: [16, 32, 96, 192, 512],
  },
} as const;

// Usage contexts and recommendations
export const USAGE_CONTEXTS = {
  // When to use logo vs icon
  USE_LOGO: [
    'splash_screen',
    'onboarding',
    'about_page',
    'marketing_materials',
    'email_headers',
  ],
  
  USE_ICON: [
    'navigation_bar',
    'tab_bar',
    'notifications',
    'small_buttons',
    'list_items',
    'app_launcher',
  ],
  
  // Background color recommendations
  BACKGROUND_RECOMMENDATIONS: {
    LOGO_WITH_TEXT: ['white', 'light_gray', 'brand_light'],
    APP_ICON: ['any'], // Icon is designed to work on any background
  },
} as const;

// Export helper functions
export const getBrandAsset = (assetKey: keyof typeof BRANDING_ASSETS) => {
  return BRANDING_ASSETS[assetKey];
};

export const getLogoSize = (context: keyof typeof LOGO_USAGE.SIZES) => {
  return LOGO_USAGE.SIZES[context];
};

export const getBrandColor = (colorKey: keyof typeof BRAND_COLORS) => {
  return BRAND_COLORS[colorKey];
};