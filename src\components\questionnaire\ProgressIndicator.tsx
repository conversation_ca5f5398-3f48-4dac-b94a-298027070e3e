import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Theme } from '../../constants/designTokens';

const { width: screenWidth } = Dimensions.get('window');

interface ProgressIndicatorProps {
  currentStep: number;
  totalSteps: number;
  completionPercentage: number;
  estimatedTimeRemaining?: number;
  language: string;
  showStepNumbers?: boolean;
  showTimeRemaining?: boolean;
}

const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({
  currentStep,
  totalSteps,
  completionPercentage,
  estimatedTimeRemaining,
  language,
  showStepNumbers = true,
  showTimeRemaining = true,
}) => {
  const [progressAnim] = useState(new Animated.Value(0));
  const [fadeAnim] = useState(new Animated.Value(0));

  useEffect(() => {
    // Animate progress bar
    Animated.timing(progressAnim, {
      toValue: completionPercentage / 100,
      duration: 500,
      useNativeDriver: false,
    }).start();

    // Fade in animation
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, [completionPercentage]);

  const formatTimeRemaining = (seconds: number): string => {
    const minutes = Math.ceil(seconds / 60);
    if (language === 'hi') {
      return minutes === 1 ? '1 मिनट बचा' : `${minutes} मिनट बचे`;
    }
    return minutes === 1 ? '1 min left' : `${minutes} mins left`;
  };

  const getProgressColor = (): string => {
    if (completionPercentage < 25) return Theme.colors.neutral[400];
    if (completionPercentage < 50) return Theme.colors.warning[500];
    if (completionPercentage < 75) return Theme.colors.primary[500];
    return Theme.colors.success[500];
  };

  const getProgressIcon = (): string => {
    if (completionPercentage < 25) return 'hourglass-outline';
    if (completionPercentage < 50) return 'time-outline';
    if (completionPercentage < 75) return 'checkmark-circle-outline';
    return 'trophy-outline';
  };

  return (
    <Animated.View style={[styles.container, { opacity: fadeAnim }]}>
      {/* Header with step info */}
      <View style={styles.header}>
        <View style={styles.stepInfo}>
          {showStepNumbers && (
            <Text style={styles.stepText}>
              {language === 'hi' 
                ? `प्रश्न ${currentStep} का ${totalSteps}`
                : `Question ${currentStep} of ${totalSteps}`
              }
            </Text>
          )}
          <Text style={styles.percentageText}>
            {Math.round(completionPercentage)}%
          </Text>
        </View>
        
        {showTimeRemaining && estimatedTimeRemaining && (
          <View style={styles.timeContainer}>
            <Ionicons 
              name="time-outline" 
              size={16} 
              color={Theme.colors.text.secondary} 
            />
            <Text style={styles.timeText}>
              {formatTimeRemaining(estimatedTimeRemaining)}
            </Text>
          </View>
        )}
      </View>

      {/* Progress bar */}
      <View style={styles.progressBarContainer}>
        <View style={styles.progressBarBackground}>
          <Animated.View
            style={[
              styles.progressBarFill,
              {
                width: progressAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: ['0%', '100%'],
                }),
                backgroundColor: getProgressColor(),
              },
            ]}
          />
        </View>
        
        {/* Progress icon */}
        <Animated.View
          style={[
            styles.progressIcon,
            {
              left: progressAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [0, screenWidth - 80],
                extrapolate: 'clamp',
              }),
            },
          ]}
        >
          <Ionicons
            name={getProgressIcon() as any}
            size={20}
            color={getProgressColor()}
          />
        </Animated.View>
      </View>

      {/* Step indicators */}
      <View style={styles.stepIndicators}>
        {Array.from({ length: Math.min(totalSteps, 10) }, (_, index) => {
          const stepNumber = index + 1;
          const isCompleted = stepNumber <= currentStep;
          const isCurrent = stepNumber === currentStep;
          
          return (
            <View
              key={index}
              style={[
                styles.stepIndicator,
                isCompleted && styles.completedStep,
                isCurrent && styles.currentStep,
              ]}
            >
              {isCompleted ? (
                <Ionicons
                  name="checkmark"
                  size={12}
                  color={Theme.colors.success[600]}
                />
              ) : (
                <Text style={[
                  styles.stepNumber,
                  isCurrent && styles.currentStepNumber,
                ]}>
                  {stepNumber}
                </Text>
              )}
            </View>
          );
        })}
        
        {totalSteps > 10 && (
          <Text style={styles.moreStepsText}>
            +{totalSteps - 10}
          </Text>
        )}
      </View>

      {/* Motivational message */}
      <View style={styles.motivationContainer}>
        <Text style={styles.motivationText}>
          {completionPercentage < 25 
            ? (language === 'hi' ? 'बहुत बढ़िया शुरुआत!' : 'Great start!')
            : completionPercentage < 50
            ? (language === 'hi' ? 'आप अच्छा कर रहे हैं!' : 'You\'re doing great!')
            : completionPercentage < 75
            ? (language === 'hi' ? 'लगभग हो गया!' : 'Almost there!')
            : (language === 'hi' ? 'बहुत बढ़िया!' : 'Excellent progress!')
          }
        </Text>
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: Theme.colors.neutral[50],
    borderBottomWidth: 1,
    borderBottomColor: Theme.colors.neutral[200],
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  stepInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  stepText: {
    fontSize: 14,
    color: Theme.colors.text.secondary,
    fontWeight: '500',
  },
  percentageText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Theme.colors.primary[600],
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  timeText: {
    fontSize: 12,
    color: Theme.colors.text.secondary,
  },
  progressBarContainer: {
    position: 'relative',
    marginBottom: 16,
  },
  progressBarBackground: {
    height: 8,
    backgroundColor: Theme.colors.neutral[200],
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressBarFill: {
    height: '100%',
    borderRadius: 4,
  },
  progressIcon: {
    position: 'absolute',
    top: -6,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  stepIndicators: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 8,
    marginBottom: 12,
  },
  stepIndicator: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: Theme.colors.neutral[200],
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Theme.colors.neutral[300],
  },
  completedStep: {
    backgroundColor: Theme.colors.success[100],
    borderColor: Theme.colors.success[500],
  },
  currentStep: {
    backgroundColor: Theme.colors.primary[100],
    borderColor: Theme.colors.primary[500],
    borderWidth: 2,
  },
  stepNumber: {
    fontSize: 10,
    fontWeight: '600',
    color: Theme.colors.text.tertiary,
  },
  currentStepNumber: {
    color: Theme.colors.primary[600],
  },
  moreStepsText: {
    fontSize: 12,
    color: Theme.colors.text.tertiary,
    marginLeft: 8,
  },
  motivationContainer: {
    alignItems: 'center',
  },
  motivationText: {
    fontSize: 12,
    color: Theme.colors.text.secondary,
    fontStyle: 'italic',
  },
});

export default ProgressIndicator;
