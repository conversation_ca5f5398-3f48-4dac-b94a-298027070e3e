import { PoseKeyPoint, PostureAnalysis, PostureIssue, ARPoseDetection, PostureIssueType, BodyPart } from '../types';
import { logger } from '../utils/logger';
import { performanceMonitor } from './performanceMonitorService';

interface RealTimePostureConfig {
  analysisFrequency: number; // How often to analyze posture (ms)
  confidenceThreshold: number; // Minimum confidence for analysis
  smoothingWindow: number; // Number of frames to smooth over
  alertThresholds: {
    poor: number; // Score below this triggers poor posture alert
    warning: number; // Score below this triggers warning
  };
}

interface PostureAlert {
  type: 'poor_posture' | 'improvement' | 'excellent' | 'warning';
  message: string;
  messageHindi: string;
  severity: 'low' | 'medium' | 'high';
  timestamp: number;
  score: number;
  issues: PostureIssue[];
}

interface RealTimeMetrics {
  currentScore: number;
  averageScore: number;
  trendDirection: 'improving' | 'declining' | 'stable';
  sessionDuration: number;
  totalAnalyses: number;
  issueFrequency: { [key: string]: number };
}

export class RealTimePostureService {
  private static instance: RealTimePostureService;
  private config: RealTimePostureConfig;
  private scoreHistory: number[] = [];
  private analysisHistory: PostureAnalysis[] = [];
  private alerts: PostureAlert[] = [];
  private metrics: RealTimeMetrics;
  private sessionStartTime: number = 0;
  private lastAnalysisTime: number = 0;
  private isActive: boolean = false;

  private constructor(config: Partial<RealTimePostureConfig> = {}) {
    this.config = {
      analysisFrequency: 1000, // Analyze every second
      confidenceThreshold: 0.6,
      smoothingWindow: 5,
      alertThresholds: {
        poor: 60,
        warning: 75,
      },
      ...config,
    };

    this.metrics = {
      currentScore: 0,
      averageScore: 0,
      trendDirection: 'stable',
      sessionDuration: 0,
      totalAnalyses: 0,
      issueFrequency: {},
    };
  }

  public static getInstance(config?: Partial<RealTimePostureConfig>): RealTimePostureService {
    if (!RealTimePostureService.instance) {
      RealTimePostureService.instance = new RealTimePostureService(config);
    }
    return RealTimePostureService.instance;
  }

  // Start real-time posture monitoring
  startMonitoring(): void {
    this.isActive = true;
    this.sessionStartTime = Date.now();
    this.lastAnalysisTime = 0;
    
    logger.info('Real-time posture monitoring started', {
      config: this.config,
      sessionStart: new Date(this.sessionStartTime).toISOString()
    }, 'RealTimePostureService');
  }

  // Stop monitoring
  stopMonitoring(): void {
    this.isActive = false;
    
    const sessionDuration = Date.now() - this.sessionStartTime;
    logger.info('Real-time posture monitoring stopped', {
      sessionDuration: `${(sessionDuration / 1000).toFixed(1)}s`,
      totalAnalyses: this.metrics.totalAnalyses,
      averageScore: this.metrics.averageScore.toFixed(1),
      alerts: this.alerts.length
    }, 'RealTimePostureService');
  }

  // Analyze pose detection for real-time feedback
  analyzeRealTime(detection: ARPoseDetection, userId: string): PostureAnalysis | null {
    if (!this.isActive || detection.confidence < this.config.confidenceThreshold) {
      return null;
    }

    const now = Date.now();
    
    // Check if enough time has passed since last analysis
    if (now - this.lastAnalysisTime < this.config.analysisFrequency) {
      return null;
    }

    this.lastAnalysisTime = now;

    try {
      // Perform posture analysis
      const analysis = this.performDetailedAnalysis(detection.landmarks, userId);
      
      // Update metrics
      this.updateMetrics(analysis);
      
      // Check for alerts
      this.checkForAlerts(analysis);
      
      // Store analysis
      this.analysisHistory.push(analysis);
      if (this.analysisHistory.length > 100) {
        this.analysisHistory.shift();
      }

      return analysis;
      
    } catch (error) {
      logger.error('Real-time posture analysis failed', error, 'RealTimePostureService');
      return null;
    }
  }

  // Perform detailed posture analysis
  private performDetailedAnalysis(landmarks: PoseKeyPoint[], userId: string): PostureAnalysis {
    const issues: PostureIssue[] = [];
    const recommendations: string[] = [];

    // Get key landmarks for analysis
    const keyLandmarks = this.extractKeyLandmarks(landmarks);
    
    if (!keyLandmarks.isValid) {
      throw new Error('Insufficient landmarks for posture analysis');
    }

    // Analyze different aspects of posture
    const headPosture = this.analyzeHeadPosture(keyLandmarks);
    const shoulderPosture = this.analyzeShoulderPosture(keyLandmarks);
    const spineAlignment = this.analyzeSpineAlignment(keyLandmarks);
    const hipAlignment = this.analyzeHipAlignment(keyLandmarks);

    // Collect issues and recommendations
    [headPosture, shoulderPosture, spineAlignment, hipAlignment].forEach(result => {
      if (result.issue.severity !== 'low') {
        issues.push(result.issue);
        recommendations.push(...result.recommendations);
      }
    });

    // Calculate overall score
    const overallScore = this.calculateOverallScore(
      headPosture.score,
      shoulderPosture.score,
      spineAlignment.score,
      hipAlignment.score
    );

    return {
      id: `realtime_${Date.now()}`,
      userId,
      overallScore,
      neckAngle: headPosture.angle,
      shoulderAlignment: shoulderPosture.score,
      spineAlignment: spineAlignment.score,
      hipAlignment: hipAlignment.score,
      recommendations,
      landmarks,
      issues,
      sessionDuration: Math.floor((Date.now() - this.sessionStartTime) / 1000),
      createdAt: new Date(),
    };
  }

  // Extract key landmarks for analysis
  private extractKeyLandmarks(landmarks: PoseKeyPoint[]) {
    const nose = landmarks.find(l => l.name === 'nose');
    const leftShoulder = landmarks.find(l => l.name === 'left_shoulder');
    const rightShoulder = landmarks.find(l => l.name === 'right_shoulder');
    const leftHip = landmarks.find(l => l.name === 'left_hip');
    const rightHip = landmarks.find(l => l.name === 'right_hip');
    const leftEar = landmarks.find(l => l.name === 'left_ear');
    const rightEar = landmarks.find(l => l.name === 'right_ear');

    return {
      nose,
      leftShoulder,
      rightShoulder,
      leftHip,
      rightHip,
      leftEar,
      rightEar,
      isValid: !!(nose && leftShoulder && rightShoulder && leftHip && rightHip),
    };
  }

  // Analyze head posture
  private analyzeHeadPosture(landmarks: any) {
    const { nose, leftShoulder, rightShoulder, leftEar, rightEar } = landmarks;
    
    // Calculate head forward position
    const shoulderMidpoint = {
      x: (leftShoulder.x + rightShoulder.x) / 2,
      y: (leftShoulder.y + rightShoulder.y) / 2,
    };

    const headForwardDistance = Math.abs(nose.x - shoulderMidpoint.x);
    
    // Calculate head tilt
    const earMidpoint = leftEar && rightEar ? {
      x: (leftEar.x + rightEar.x) / 2,
      y: (leftEar.y + rightEar.y) / 2,
    } : null;

    const headTilt = earMidpoint ? 
      Math.abs(leftEar.y - rightEar.y) : 0;

    // Score head posture (0-100)
    let score = 100;
    let severity: 'low' | 'medium' | 'high' = 'low';
    const recommendations: string[] = [];

    if (headForwardDistance > 0.08) {
      score -= 30;
      severity = 'high';
      recommendations.push('Pull your head back over your shoulders');
      recommendations.push('अपना सिर कंधों के ऊपर वापस खींचें');
    } else if (headForwardDistance > 0.05) {
      score -= 15;
      severity = 'medium';
      recommendations.push('Slightly adjust head position');
      recommendations.push('सिर की स्थिति को थोड़ा समायोजित करें');
    }

    if (headTilt > 0.04) {
      score -= 20;
      recommendations.push('Level your head - avoid tilting');
      recommendations.push('अपना सिर सीधा रखें - झुकाव से बचें');
    }

    return {
      score: Math.max(0, score),
      angle: headForwardDistance * 100, // Convert to degrees approximation
      issue: {
        type: 'forward_head' as PostureIssueType,
        severity,
        description: severity === 'low' ? 'Good head alignment' : 'Head positioned forward',
        affectedBodyParts: ['head', 'neck'] as BodyPart[],
      } as PostureIssue,
      recommendations,
    };
  }

  // Analyze shoulder posture
  private analyzeShoulderPosture(landmarks: any) {
    const { leftShoulder, rightShoulder } = landmarks;
    
    // Check shoulder height difference
    const heightDifference = Math.abs(leftShoulder.y - rightShoulder.y);
    
    // Check shoulder forward position (rounded shoulders)
    const shoulderForwardness = Math.max(leftShoulder.z || 0, rightShoulder.z || 0);

    let score = 100;
    let severity: 'low' | 'medium' | 'high' = 'low';
    const recommendations: string[] = [];

    // Uneven shoulders
    if (heightDifference > 0.05) {
      score -= 25;
      severity = 'high';
      recommendations.push('Level your shoulders evenly');
      recommendations.push('अपने कंधों को समान स्तर पर रखें');
    } else if (heightDifference > 0.03) {
      score -= 12;
      severity = 'medium';
    }

    // Rounded shoulders
    if (shoulderForwardness > 0.1) {
      score -= 20;
      if (severity !== 'high') {
        severity = 'medium';
      }
      recommendations.push('Pull shoulders back and down');
      recommendations.push('कंधों को पीछे और नीचे खींचें');
    }

    return {
      score: Math.max(0, score),
      issue: {
        type: (heightDifference > shoulderForwardness ? 'uneven_shoulders' : 'rounded_shoulders') as PostureIssueType,
        severity,
        description: severity === 'low' ? 'Good shoulder alignment' : 'Shoulder posture needs adjustment',
        affectedBodyParts: ['shoulders', 'upper_back'] as BodyPart[],
      } as PostureIssue,
      recommendations,
    };
  }

  // Analyze spine alignment
  private analyzeSpineAlignment(landmarks: any) {
    const { leftShoulder, rightShoulder, leftHip, rightHip } = landmarks;
    
    const shoulderMidpoint = {
      x: (leftShoulder.x + rightShoulder.x) / 2,
      y: (leftShoulder.y + rightShoulder.y) / 2,
    };
    
    const hipMidpoint = {
      x: (leftHip.x + rightHip.x) / 2,
      y: (leftHip.y + rightHip.y) / 2,
    };

    const spinalDeviation = Math.abs(shoulderMidpoint.x - hipMidpoint.x);
    
    let score = 100;
    let severity: 'low' | 'medium' | 'high' = 'low';
    const recommendations: string[] = [];

    if (spinalDeviation > 0.06) {
      score -= 30;
      severity = 'high';
      recommendations.push('Align your spine - stand straighter');
      recommendations.push('अपनी रीढ़ को सीधा करें - सीधे खड़े हों');
    } else if (spinalDeviation > 0.03) {
      score -= 15;
      severity = 'medium';
      recommendations.push('Minor spine alignment adjustment needed');
      recommendations.push('रीढ़ की हड्डी में मामूली समायोजन की आवश्यकता');
    }

    return {
      score: Math.max(0, score),
      issue: {
        type: 'poor_spinal_alignment' as const,
        severity,
        description: severity === 'low' ? 'Good spinal alignment' : 'Spine alignment needs improvement',
        affectedBodyParts: ['upper_back', 'lower_back'] as BodyPart[],
      },
      recommendations,
    };
  }

  // Analyze hip alignment
  private analyzeHipAlignment(landmarks: any) {
    const { leftHip, rightHip } = landmarks;
    
    const hipTilt = Math.abs(leftHip.y - rightHip.y);
    const hipForwardness = Math.abs((leftHip.z || 0) - (rightHip.z || 0));
    
    let score = 100;
    let severity: 'low' | 'medium' | 'high' = 'low';
    const recommendations: string[] = [];

    if (hipTilt > 0.04) {
      score -= 20;
      severity = 'medium';
      recommendations.push('Level your hips evenly');
      recommendations.push('अपने कूल्हों को समान स्तर पर रखें');
    }

    if (hipForwardness > 0.05) {
      score -= 15;
      if (severity === 'low') {
        severity = 'medium';
      }
      recommendations.push('Align hips properly');
      recommendations.push('कूल्हों को सही तरीके से संरेखित करें');
    }

    return {
      score: Math.max(0, score),
      issue: {
        type: 'tilted_pelvis' as const,
        severity,
        description: severity === 'low' ? 'Good hip alignment' : 'Hip alignment needs adjustment',
        affectedBodyParts: ['pelvis', 'hips'] as BodyPart[],
      },
      recommendations,
    };
  }

  // Calculate overall posture score
  private calculateOverallScore(headScore: number, shoulderScore: number, spineScore: number, hipScore: number): number {
    // Weighted average - spine and shoulders are most important
    const weights = {
      head: 0.2,
      shoulder: 0.3,
      spine: 0.35,
      hip: 0.15,
    };

    return Math.round(
      headScore * weights.head +
      shoulderScore * weights.shoulder +
      spineScore * weights.spine +
      hipScore * weights.hip
    );
  }

  // Update real-time metrics
  private updateMetrics(analysis: PostureAnalysis): void {
    this.metrics.totalAnalyses++;
    this.metrics.currentScore = analysis.overallScore;
    this.metrics.sessionDuration = Math.floor((Date.now() - this.sessionStartTime) / 1000);
    
    // Update score history
    this.scoreHistory.push(analysis.overallScore);
    if (this.scoreHistory.length > 60) { // Keep last 60 scores (1 minute at 1Hz)
      this.scoreHistory.shift();
    }
    
    // Calculate average score
    this.metrics.averageScore = this.scoreHistory.reduce((a, b) => a + b, 0) / this.scoreHistory.length;
    
    // Determine trend
    if (this.scoreHistory.length >= 10) {
      const recent = this.scoreHistory.slice(-5);
      const older = this.scoreHistory.slice(-10, -5);
      const recentAvg = recent.reduce((a, b) => a + b, 0) / recent.length;
      const olderAvg = older.reduce((a, b) => a + b, 0) / older.length;
      
      const difference = recentAvg - olderAvg;
      if (difference > 3) {
        this.metrics.trendDirection = 'improving';
      } else if (difference < -3) {
        this.metrics.trendDirection = 'declining';
      } else {
        this.metrics.trendDirection = 'stable';
      }
    }
    
    // Update issue frequency
    analysis.issues?.forEach(issue => {
      this.metrics.issueFrequency[issue.type] = 
        (this.metrics.issueFrequency[issue.type] || 0) + 1;
    });
  }

  // Check for posture alerts
  private checkForAlerts(analysis: PostureAnalysis): void {
    const score = analysis.overallScore;
    const now = Date.now();

    // Poor posture alert
    if (score < this.config.alertThresholds.poor) {
      this.addAlert({
        type: 'poor_posture',
        message: 'Poor posture detected! Please adjust your position.',
        messageHindi: 'खराब मुद्रा का पता चला! कृपया अपनी स्थिति को समायोजित करें।',
        severity: 'high',
        timestamp: now,
        score,
        issues: analysis.issues || [],
      });
    }
    // Warning alert
    else if (score < this.config.alertThresholds.warning) {
      this.addAlert({
        type: 'warning',
        message: 'Posture could be improved. Small adjustments recommended.',
        messageHindi: 'मुद्रा में सुधार हो सकता है। छोटे समायोजन की सिफारिश की जाती है।',
        severity: 'medium',
        timestamp: now,
        score,
        issues: analysis.issues || [],
      });
    }
    // Excellent posture
    else if (score > 90) {
      this.addAlert({
        type: 'excellent',
        message: 'Excellent posture! Keep it up!',
        messageHindi: 'उत्कृष्ट मुद्रा! इसे बनाए रखें!',
        severity: 'low',
        timestamp: now,
        score,
        issues: [],
      });
    }
  }

  // Add alert to history
  private addAlert(alert: PostureAlert): void {
    // Avoid duplicate alerts within 5 seconds
    const recentAlert = this.alerts.find(a => 
      a.type === alert.type && 
      alert.timestamp - a.timestamp < 5000
    );
    
    if (!recentAlert) {
      this.alerts.push(alert);
      
      // Limit alert history
      if (this.alerts.length > 50) {
        this.alerts.shift();
      }

      // Log important alerts
      if (alert.severity === 'high') {
        logger.warn('Posture alert triggered', alert, 'RealTimePostureService');
      }
    }
  }

  // Get current metrics
  getCurrentMetrics(): RealTimeMetrics {
    return { ...this.metrics };
  }

  // Get recent alerts
  getRecentAlerts(limit: number = 10): PostureAlert[] {
    return this.alerts.slice(-limit);
  }

  // Get posture trend analysis
  getTrendAnalysis() {
    if (this.scoreHistory.length < 10) {
      return { trend: 'insufficient_data', confidence: 0 };
    }

    const recent = this.scoreHistory.slice(-10);
    const slope = this.calculateTrendSlope(recent);
    
    return {
      trend: this.metrics.trendDirection,
      confidence: Math.abs(slope) > 0.5 ? 'high' : 'medium',
      slope,
      prediction: this.predictNextScore(),
    };
  }

  // Calculate trend slope
  private calculateTrendSlope(scores: number[]): number {
    const n = scores.length;
    const sumX = (n * (n - 1)) / 2;
    const sumY = scores.reduce((a, b) => a + b, 0);
    const sumXY = scores.reduce((sum, score, index) => sum + index * score, 0);
    const sumXX = (n * (n - 1) * (2 * n - 1)) / 6;
    
    return (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
  }

  // Predict next score based on trend
  private predictNextScore(): number {
    if (this.scoreHistory.length < 5) return this.metrics.currentScore;
    
    const recent = this.scoreHistory.slice(-5);
    const slope = this.calculateTrendSlope(recent);
    
    return Math.max(0, Math.min(100, this.metrics.currentScore + slope));
  }

  // Reset service state
  reset(): void {
    this.scoreHistory = [];
    this.analysisHistory = [];
    this.alerts = [];
    this.metrics = {
      currentScore: 0,
      averageScore: 0,
      trendDirection: 'stable',
      sessionDuration: 0,
      totalAnalyses: 0,
      issueFrequency: {},
    };
    this.sessionStartTime = Date.now();
  }
}

export const realTimePostureService = RealTimePostureService.getInstance();
