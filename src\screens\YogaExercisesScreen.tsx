import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Dimensions,
  Image,
  StatusBar,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { LinearGradient } from 'expo-linear-gradient';
import { useAuth } from '../contexts/AuthContext';
import { RootStackParamList, YogaPose, SUBSCRIPTION_FEATURES } from '../types';
import { Theme } from '../constants/designTokens';
import YogaPoseCard from '../components/YogaPoseCard';
import SearchBar from '../components/SearchBar';

type YogaExercisesNavigationProp = StackNavigationProp<RootStackParamList>;

const { width } = Dimensions.get('window');

const YogaExercisesScreen: React.FC = () => {
  const { t, i18n } = useTranslation();
  const navigation = useNavigation<YogaExercisesNavigationProp>();
  const { user } = useAuth();
  
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedDifficulty, setSelectedDifficulty] = useState<'all' | 'beginner' | 'intermediate' | 'advanced'>('all');
  const [selectedCategory, setSelectedCategory] = useState<'all' | 'recommended' | 'posture'>('all');

  const [yogaPoses, setYogaPoses] = useState<YogaPose[]>([]);


  const filteredPoses = yogaPoses.filter(pose => {
    const matchesSearch = pose.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         pose.nameHindi.includes(searchQuery);
    const matchesDifficulty = selectedDifficulty === 'all' || pose.difficulty === selectedDifficulty;
    const matchesCategory = selectedCategory === 'all' || 
                           (selectedCategory === 'recommended' && pose.difficulty === 'beginner') ||
                           (selectedCategory === 'posture' && pose.targetAreas.some(area => 
                             ['shoulders', 'upper_back', 'lower_back', 'neck'].includes(area)));
    
    return matchesSearch && matchesDifficulty && matchesCategory;
  });

  const canAccessPose = (pose: YogaPose) => {
    if (!user) return false;
    const features = SUBSCRIPTION_FEATURES[user.subscriptionTier];
    return features.exercises === -1 || filteredPoses.indexOf(pose) < features.exercises;
  };

  const startPose = (pose: YogaPose) => {
    if (canAccessPose(pose)) {
      navigation.navigate('YogaSession', { poseId: pose.id });
    } else {
      navigation.navigate('Subscription');
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return '#10B981';
      case 'intermediate': return '#F59E0B';
      case 'advanced': return '#EF4444';
      default: return '#6B7280';
    }
  };

  const renderPoseCard = (pose: YogaPose) => {
    const accessible = canAccessPose(pose);
    
    return (
      <YogaPoseCard
        key={pose.id}
        pose={pose}
        accessible={accessible}
        onPress={() => startPose(pose)}
        getDifficultyColor={getDifficultyColor}
      />
    );
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#000000" />
      
      {/* Background Gradient */}
      <LinearGradient
        colors={['#000000', '#1a1a1a', '#2d2d2d']}
        style={StyleSheet.absoluteFillObject}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      />
      
      {/* Modern Header */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <View style={styles.headerTop}>
            <View>
              <Text style={styles.headerTitle}>{t('yoga.title')}</Text>
              <Text style={styles.headerSubtitle}>
                {i18n.language === 'hi' 
                  ? 'अपने पोस्चर को सुधारने के लिए योग करें'
                  : 'Practice yoga to improve your posture'}
              </Text>
            </View>
            <View style={styles.headerStats}>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>{filteredPoses.length}</Text>
                <Text style={styles.statLabel}>
                  {i18n.language === 'hi' ? 'आसन' : 'Poses'}
                </Text>
              </View>
            </View>
          </View>
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <SearchBar
            placeholder={i18n.language === 'hi' ? 'योग आसन खोजें...' : 'Search poses...'}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>

        {/* Filter Tabs */}
        <View style={styles.filterContainer}>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <TouchableOpacity
              style={[styles.filterTab, selectedCategory === 'all' && styles.filterTabActive]}
              onPress={() => setSelectedCategory('all')}
            >
              <Text style={[styles.filterTabText, selectedCategory === 'all' && styles.filterTabTextActive]}>
                {t('yoga.allPoses')}
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.filterTab, selectedCategory === 'recommended' && styles.filterTabActive]}
              onPress={() => setSelectedCategory('recommended')}
            >
              <Text style={[styles.filterTabText, selectedCategory === 'recommended' && styles.filterTabTextActive]}>
                {t('yoga.recommended')}
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.filterTab, selectedCategory === 'posture' && styles.filterTabActive]}
              onPress={() => setSelectedCategory('posture')}
            >
              <Text style={[styles.filterTabText, selectedCategory === 'posture' && styles.filterTabTextActive]}>
                {i18n.language === 'hi' ? 'पोस्चर सुधार' : 'Posture Fix'}
              </Text>
            </TouchableOpacity>
          </ScrollView>
        </View>

        {/* Difficulty Filter */}
        <View style={styles.difficultyContainer}>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {['all', 'beginner', 'intermediate', 'advanced'].map((difficulty) => (
              <TouchableOpacity
                key={difficulty}
                style={[styles.difficultyChip, selectedDifficulty === difficulty && styles.difficultyChipActive]}
                onPress={() => setSelectedDifficulty(difficulty as any)}
              >
                <Text style={[styles.difficultyChipText, selectedDifficulty === difficulty && styles.difficultyChipTextActive]}>
                  {difficulty === 'all' ? (i18n.language === 'hi' ? 'सभी' : 'All') : t(`yoga.${difficulty}`)}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Poses List */}
        <View style={styles.posesContainer}>
          {filteredPoses.length > 0 ? (
            filteredPoses.map(renderPoseCard)
          ) : (
            <View style={styles.emptyState}>
              <Ionicons name="search" size={48} color="#6B7280" />
              <Text style={styles.emptyStateText}>
                {i18n.language === 'hi' 
                  ? 'कोई योग आसन नहीं मिला'
                  : 'No poses found'}
              </Text>
              <Text style={styles.emptyStateSubtext}>
                {i18n.language === 'hi'
                  ? 'अपनी खोज या फिल्टर बदलने का प्रयास करें'
                  : 'Try changing your search or filters'}
              </Text>
            </View>
          )}
        </View>

        {/* Subscription Prompt */}
        {user?.subscriptionTier === 'free' && (
          <View style={styles.subscriptionPrompt}>
            <LinearGradient
              colors={['#6366F1', '#8B5CF6']}
              style={styles.subscriptionCard}
            >
              <Ionicons name="star" size={24} color="#FFFFFF" />
              <Text style={styles.subscriptionTitle}>
                {i18n.language === 'hi' 
                  ? 'सभी योग आसन अनलॉक करें'
                  : 'Unlock All Yoga Poses'}
              </Text>
              <Text style={styles.subscriptionSubtitle}>
                {i18n.language === 'hi'
                  ? 'प्रीमियम सब्सक्रिप्शन के साथ 50+ आसन प्राप्त करें'
                  : 'Get 50+ poses with premium subscription'}
              </Text>
              <TouchableOpacity
                style={styles.subscriptionButton}
                onPress={() => navigation.navigate('Subscription')}
              >
                <Text style={styles.subscriptionButtonText}>
                  {i18n.language === 'hi' ? 'अपग्रेड करें' : 'Upgrade Now'}
                </Text>
              </TouchableOpacity>
            </LinearGradient>
          </View>
        )}

        <View style={styles.bottomSpacing} />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  header: {
    paddingTop: 50,
    paddingBottom: Theme.spacing['2xl'],
    paddingHorizontal: Theme.spacing.xl,
  },
  headerContent: {
    flex: 1,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: '300',
    color: '#FFFFFF',
    marginBottom: Theme.spacing.xs,
    letterSpacing: 0.5,
  },
  headerSubtitle: {
    fontSize: Theme.typography.fontSize.base,
    color: 'rgba(255, 255, 255, 0.7)',
    lineHeight: Theme.typography.lineHeight.normal * Theme.typography.fontSize.base,
  },
  headerStats: {
    alignItems: 'center',
  },
  statItem: {
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    paddingHorizontal: Theme.spacing.lg,
    paddingVertical: Theme.spacing.sm,
    borderRadius: Theme.borderRadius.xl,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  statNumber: {
    fontSize: Theme.typography.fontSize.lg,
    fontWeight: Theme.typography.fontWeight.bold,
    color: '#FFFFFF',
  },
  statLabel: {
    fontSize: Theme.typography.fontSize.xs,
    color: 'rgba(255, 255, 255, 0.7)',
    marginTop: 2,
  },
  content: {
    flex: 1,
  },
  searchContainer: {
    paddingHorizontal: Theme.spacing.xl,
    paddingBottom: Theme.spacing.lg,
  },
  filterContainer: {
    paddingHorizontal: Theme.spacing.xl,
    paddingBottom: Theme.spacing.lg,
  },
  filterTab: {
    paddingHorizontal: Theme.spacing.lg,
    paddingVertical: Theme.spacing.sm,
    borderRadius: Theme.borderRadius.full,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    marginRight: Theme.spacing.sm,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  filterTabActive: {
    backgroundColor: '#FFFFFF',
    borderColor: '#FFFFFF',
  },
  filterTabText: {
    fontSize: Theme.typography.fontSize.sm,
    fontWeight: Theme.typography.fontWeight.medium,
    color: 'rgba(255, 255, 255, 0.7)',
  },
  filterTabTextActive: {
    color: '#000000',
  },
  difficultyContainer: {
    paddingHorizontal: Theme.spacing.xl,
    paddingBottom: Theme.spacing.lg,
  },
  difficultyChip: {
    paddingHorizontal: Theme.spacing.md,
    paddingVertical: Theme.spacing.xs,
    borderRadius: Theme.borderRadius.lg,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    marginRight: Theme.spacing.xs,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  difficultyChipActive: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderColor: 'rgba(255, 255, 255, 0.4)',
  },
  difficultyChipText: {
    fontSize: Theme.typography.fontSize.xs,
    fontWeight: Theme.typography.fontWeight.medium,
    color: 'rgba(255, 255, 255, 0.7)',
  },
  difficultyChipTextActive: {
    color: '#FFFFFF',
  },
  posesContainer: {
    paddingHorizontal: Theme.spacing.xl,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: Theme.spacing['6xl'],
    paddingHorizontal: Theme.spacing.xl,
  },
  emptyStateText: {
    fontSize: Theme.typography.fontSize.lg,
    fontWeight: Theme.typography.fontWeight.semibold,
    color: '#FFFFFF',
    marginTop: Theme.spacing.lg,
    marginBottom: Theme.spacing.xs,
    textAlign: 'center',
  },
  emptyStateSubtext: {
    fontSize: Theme.typography.fontSize.sm,
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
    lineHeight: Theme.typography.lineHeight.normal * Theme.typography.fontSize.sm,
  },
  subscriptionPrompt: {
    paddingHorizontal: Theme.spacing.xl,
    paddingTop: Theme.spacing.xl,
  },
  subscriptionCard: {
    borderRadius: Theme.borderRadius['2xl'],
    padding: Theme.spacing['3xl'],
    alignItems: 'center',
  },
  subscriptionTitle: {
    fontSize: Theme.typography.fontSize.lg,
    fontWeight: Theme.typography.fontWeight.semibold,
    color: Theme.colors.text.inverse,
    marginTop: Theme.spacing.md,
    marginBottom: Theme.spacing.xs,
    textAlign: 'center',
  },
  subscriptionSubtitle: {
    fontSize: Theme.typography.fontSize.sm,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    marginBottom: Theme.spacing.lg,
    lineHeight: Theme.typography.lineHeight.normal * Theme.typography.fontSize.sm,
  },
  subscriptionButton: {
    backgroundColor: Theme.colors.surface.primary,
    paddingHorizontal: Theme.spacing['2xl'],
    paddingVertical: Theme.spacing.md,
    borderRadius: Theme.borderRadius.lg,
  },
  subscriptionButtonText: {
    fontSize: Theme.typography.fontSize.sm,
    fontWeight: Theme.typography.fontWeight.semibold,
    color: Theme.colors.primary[500],
  },
  bottomSpacing: {
    height: Theme.layout.tabBar.height + Theme.spacing.xl,
  },
});

export default YogaExercisesScreen;