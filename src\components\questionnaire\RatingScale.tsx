import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
} from 'react-native';
import { Theme } from '../../constants/designTokens';

interface RatingScaleProps {
  min: number;
  max: number;
  step: number;
  value: number | null;
  onValueChange: (value: number) => void;
  labels?: { [key: number]: string };
  language: string;
  showValue?: boolean;
}

const RatingScale: React.FC<RatingScaleProps> = ({
  min,
  max,
  step,
  value,
  onValueChange,
  labels,
  language,
  showValue = true,
}) => {
  const [animatedValue] = useState(new Animated.Value(0));
  const [sliderWidth, setSliderWidth] = useState(0);

  const range = max - min;
  const steps = Math.floor(range / step) + 1;

  useEffect(() => {
    if (value !== null) {
      const normalizedValue = (value - min) / range;
      Animated.spring(animatedValue, {
        toValue: normalizedValue,
        useNativeDriver: false,
        tension: 100,
        friction: 8,
      }).start();
    }
  }, [value, min, max, range]);

  const handlePress = (index: number) => {
    const newValue = min + (index * step);
    onValueChange(newValue);
  };

  const renderScalePoints = () => {
    const points = [];
    for (let i = 0; i < steps; i++) {
      const currentValue = min + (i * step);
      const isSelected = value === currentValue;
      const isActive = value !== null && currentValue <= value;

      points.push(
        <TouchableOpacity
          key={i}
          style={[
            styles.scalePoint,
            isSelected && styles.selectedPoint,
            isActive && styles.activePoint,
          ]}
          onPress={() => handlePress(i)}
          activeOpacity={0.7}
        >
          <View style={[
            styles.pointCircle,
            isSelected && styles.selectedCircle,
            isActive && styles.activeCircle,
          ]} />
          {labels && labels[currentValue] && (
            <Text style={[
              styles.pointLabel,
              isSelected && styles.selectedLabel,
            ]}>
              {labels[currentValue]}
            </Text>
          )}
        </TouchableOpacity>
      );
    }
    return points;
  };

  const renderSlider = () => {
    if (sliderWidth === 0) return null;

    const thumbPosition = animatedValue.interpolate({
      inputRange: [0, 1],
      outputRange: [0, sliderWidth - 24],
      extrapolate: 'clamp',
    });

    const trackFillWidth = animatedValue.interpolate({
      inputRange: [0, 1],
      outputRange: [0, sliderWidth],
      extrapolate: 'clamp',
    });

    return (
      <View style={styles.sliderContainer}>
        <View
          style={styles.sliderTrack}
          onLayout={(event) => {
            const { width } = event.nativeEvent.layout;
            setSliderWidth(width);
          }}
        >
          <Animated.View
            style={[
              styles.sliderTrackFill,
              { width: trackFillWidth },
            ]}
          />
          <Animated.View
            style={[
              styles.sliderThumb,
              {
                transform: [{ translateX: thumbPosition }],
              },
            ]}
          />
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {showValue && value !== null && (
        <View style={styles.valueContainer}>
          <Text style={styles.valueText}>{value}</Text>
          <Text style={styles.valueUnit}>
            {range <= 10 ? (language === 'hi' ? 'स्कोर' : 'score') : 
             range <= 24 ? (language === 'hi' ? 'घंटे' : 'hours') : 
             (language === 'hi' ? 'मिनट' : 'minutes')}
          </Text>
        </View>
      )}

      {/* Discrete scale points */}
      <View style={styles.scaleContainer}>
        {renderScalePoints()}
      </View>

      {/* Continuous slider for larger ranges */}
      {range > 10 && renderSlider()}

      {/* Min/Max labels */}
      <View style={styles.extremeLabels}>
        <Text style={styles.extremeLabel}>{min}</Text>
        <Text style={styles.extremeLabel}>{max}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: 20,
  },
  valueContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  valueText: {
    fontSize: 32,
    fontWeight: 'bold',
    color: Theme.colors.primary[600],
  },
  valueUnit: {
    fontSize: 14,
    color: Theme.colors.text.secondary,
    marginTop: 4,
  },
  scaleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 10,
  },
  scalePoint: {
    alignItems: 'center',
    padding: 8,
  },
  pointCircle: {
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: Theme.colors.neutral[200],
    borderWidth: 2,
    borderColor: Theme.colors.neutral[300],
  },
  selectedPoint: {
    // Additional styling for selected point container
  },
  activePoint: {
    // Additional styling for active point container
  },
  selectedCircle: {
    backgroundColor: Theme.colors.primary[500],
    borderColor: Theme.colors.primary[600],
    transform: [{ scale: 1.2 }],
  },
  activeCircle: {
    backgroundColor: Theme.colors.primary[300],
    borderColor: Theme.colors.primary[400],
  },
  pointLabel: {
    fontSize: 12,
    color: Theme.colors.text.tertiary,
    marginTop: 4,
    textAlign: 'center',
    maxWidth: 60,
  },
  selectedLabel: {
    color: Theme.colors.primary[600],
    fontWeight: '600',
  },
  sliderContainer: {
    paddingHorizontal: 20,
    marginVertical: 20,
  },
  sliderTrack: {
    height: 6,
    backgroundColor: Theme.colors.neutral[200],
    borderRadius: 3,
    position: 'relative',
  },
  sliderTrackFill: {
    height: 6,
    backgroundColor: Theme.colors.primary[500],
    borderRadius: 3,
    position: 'absolute',
    left: 0,
    top: 0,
  },
  sliderThumb: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: Theme.colors.primary[600],
    position: 'absolute',
    top: -9,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  extremeLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 28,
    marginTop: 10,
  },
  extremeLabel: {
    fontSize: 14,
    color: Theme.colors.text.secondary,
    fontWeight: '500',
  },
});

export default RatingScale;
