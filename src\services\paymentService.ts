import { supabase } from '../lib/supabase';
import RazorpayCheckout from 'react-native-razorpay';
import { logger } from '../utils/logger';
import { SubscriptionTier } from '../types';

export interface PaymentOptions {
  amount: number;
  currency: string;
  description: string;
  prefill?: {
    email?: string;
    contact?: string;
    name?: string;
  };
  theme?: {
    color?: string;
  };
}

export interface PaymentResult {
  razorpay_payment_id: string;
  razorpay_order_id: string;
  razorpay_signature: string;
}

export interface PaymentError {
  code: number;
  description: string;
  source: string;
  step: string;
  reason: string;
}

export interface Subscription {
  id: string;
  userId: string;
  tier: SubscriptionTier;
  status: 'active' | 'cancelled' | 'expired' | 'pending';
  startDate: Date;
  endDate: Date;
  paymentId?: string;
  orderId?: string;
  amount: number;
  currency: string;
  duration: 'monthly' | 'yearly';
  autoRenew: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export class PaymentService {
  private static readonly RAZORPAY_KEY = process.env.EXPO_PUBLIC_RAZORPAY_KEY_ID;

  // Initialize payment for subscription
  static async initiateSubscriptionPayment(
    tier: SubscriptionTier,
    duration: 'monthly' | 'yearly',
    userId: string
  ): Promise<PaymentResult> {
    try {
      if (!this.RAZORPAY_KEY) {
        throw new Error('Razorpay key not configured');
      }

      const amount = this.getSubscriptionAmount(tier, duration);
      const orderId = await this.createOrder(amount, userId, tier, duration);

      const options: PaymentOptions = {
        amount: amount * 100, // Razorpay expects amount in paise
        currency: 'INR',
        description: `${tier} subscription (${duration})`,
        prefill: {
          email: '', // Will be filled from user data
          name: '', // Will be filled from user data
        },
        theme: {
          color: '#007AFF',
        },
      };

      // Get user details for prefill
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        const { data: profile } = await supabase
          .from('profiles')
          .select('email, full_name')
          .eq('id', user.id)
          .single();
        
        if (profile) {
          options.prefill!.email = profile.email;
          options.prefill!.name = profile.full_name || '';
        }
      }

      return new Promise((resolve, reject) => {
        RazorpayCheckout.open({
          key: this.RAZORPAY_KEY!,
          ...options,
        })
          .then(async (data: any) => {
            // Create subscription after successful payment
            await this.createSubscription(userId, tier, duration, data, orderId);
            resolve(data);
          })
          .catch((error: PaymentError) => {
            reject(new Error(`Payment failed: ${error.description}`));
          });
      });
    } catch (error: any) {
      logger.error('Payment initiation failed', error, 'PaymentService');
      throw error;
    }
  }

  // Get subscription amount based on tier and duration
  private static getSubscriptionAmount(tier: SubscriptionTier, duration: 'monthly' | 'yearly'): number {
    const prices = {
      plus: {
        monthly: 99, // ₹99 per month
        yearly: 999, // ₹999 per year (save ₹189)
      },
      pro: {
        monthly: 199, // ₹199 per month
        yearly: 1999, // ₹1999 per year (save ₹389)
      },
      premium: {
        monthly: 299, // ₹299 per month
        yearly: 2999, // ₹2999 per year (save ₹589)
      },
    };

    if (tier === 'free') return 0;
    return prices[tier as keyof typeof prices][duration];
  }

  // Create order in database
  private static async createOrder(
    amount: number,
    userId: string,
    tier: SubscriptionTier,
    duration: 'monthly' | 'yearly'
  ): Promise<string> {
    try {
      const orderId = `order_${Date.now()}_${userId}`;
      
      // Store order details in Supabase
      const { error } = await supabase
        .from('orders')
        .insert({
          order_id: orderId,
          user_id: userId,
          amount,
          tier,
          duration,
          status: 'created',
          created_at: new Date().toISOString(),
        });

      if (error) throw error;
      return orderId;
    } catch (error: any) {
      logger.error('Failed to create order', error, 'PaymentService');
      throw new Error('Failed to create order');
    }
  }

  // Create subscription after successful payment
  private static async createSubscription(
    userId: string,
    tier: SubscriptionTier,
    duration: 'monthly' | 'yearly',
    paymentResult: PaymentResult,
    orderId: string
  ): Promise<Subscription> {
    try {
      const now = new Date();
      const endDate = new Date(now);
      
      if (duration === 'monthly') {
        endDate.setMonth(endDate.getMonth() + 1);
      } else {
        endDate.setFullYear(endDate.getFullYear() + 1);
      }

      const subscription: Omit<Subscription, 'id' | 'createdAt' | 'updatedAt'> = {
        userId,
        tier,
        status: 'active',
        startDate: now,
        endDate,
        paymentId: paymentResult.razorpay_payment_id,
        orderId,
        amount: this.getSubscriptionAmount(tier, duration),
        currency: 'INR',
        duration,
        autoRenew: true,
      };

      // Store subscription in Supabase
      const { data, error } = await supabase
        .from('subscriptions')
        .insert({
          user_id: subscription.userId,
          tier: subscription.tier,
          status: subscription.status,
          start_date: subscription.startDate.toISOString(),
          end_date: subscription.endDate.toISOString(),
          payment_id: subscription.paymentId,
          order_id: subscription.orderId,
          amount: subscription.amount,
          currency: subscription.currency,
          duration: subscription.duration,
          auto_renew: subscription.autoRenew,
        })
        .select()
        .single();

      if (error) throw error;

      // Update user's subscription tier
      await supabase
        .from('profiles')
        .update({ subscription_tier: tier })
        .eq('id', userId);

      // Update order status
      await supabase
        .from('orders')
        .update({ 
          status: 'completed',
          payment_id: paymentResult.razorpay_payment_id,
          updated_at: new Date().toISOString(),
        })
        .eq('order_id', orderId);

      logger.info('Subscription created successfully', { userId, tier, duration }, 'PaymentService');

      return {
        id: data.id,
        userId: data.user_id,
        tier: data.tier,
        status: data.status,
        startDate: new Date(data.start_date),
        endDate: new Date(data.end_date),
        paymentId: data.payment_id,
        orderId: data.order_id,
        amount: data.amount,
        currency: data.currency,
        duration: data.duration,
        autoRenew: data.auto_renew,
        createdAt: new Date(data.created_at),
        updatedAt: new Date(data.updated_at),
      };
    } catch (error: any) {
      logger.error('Failed to create subscription', error, 'PaymentService');
      throw error;
    }
  }

  // Cancel subscription
  static async cancelSubscription(subscriptionId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('subscriptions')
        .update({ 
          status: 'cancelled',
          auto_renew: false,
          updated_at: new Date().toISOString(),
        })
        .eq('id', subscriptionId);

      if (error) throw error;
      
      logger.info('Subscription cancelled', { subscriptionId }, 'PaymentService');
    } catch (error: any) {
      logger.error('Failed to cancel subscription', error, 'PaymentService');
      throw error;
    }
  }

  // Reactivate subscription
  static async reactivateSubscription(subscriptionId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('subscriptions')
        .update({ 
          status: 'active',
          auto_renew: true,
          updated_at: new Date().toISOString(),
        })
        .eq('id', subscriptionId);

      if (error) throw error;
      
      logger.info('Subscription reactivated', { subscriptionId }, 'PaymentService');
    } catch (error: any) {
      logger.error('Failed to reactivate subscription', error, 'PaymentService');
      throw error;
    }
  }

  // Get user's current subscription
  static async getUserSubscription(userId: string): Promise<Subscription | null> {
    try {
      const { data, error } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('user_id', userId)
        .eq('status', 'active')
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      if (error) {
        if (error.code === 'PGRST116') return null; // Not found
        throw error;
      }

      return {
        id: data.id,
        userId: data.user_id,
        tier: data.tier,
        status: data.status,
        startDate: new Date(data.start_date),
        endDate: new Date(data.end_date),
        paymentId: data.payment_id,
        orderId: data.order_id,
        amount: data.amount,
        currency: data.currency,
        duration: data.duration,
        autoRenew: data.auto_renew,
        createdAt: new Date(data.created_at),
        updatedAt: new Date(data.updated_at),
      };
    } catch (error: any) {
      logger.error('Failed to get user subscription', error, 'PaymentService');
      return null;
    }
  }

  // Check if subscription is expired and update status
  static async checkAndUpdateExpiredSubscriptions(): Promise<void> {
    try {
      const now = new Date().toISOString();
      
      const { error } = await supabase
        .from('subscriptions')
        .update({ 
          status: 'expired',
          updated_at: now,
        })
        .eq('status', 'active')
        .lt('end_date', now);

      if (error) throw error;
      
      logger.info('Expired subscriptions updated', undefined, 'PaymentService');
    } catch (error: any) {
      logger.error('Failed to update expired subscriptions', error, 'PaymentService');
    }
  }

  // Get subscription history for user
  static async getSubscriptionHistory(userId: string): Promise<Subscription[]> {
    try {
      const { data, error } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) throw error;

      return data.map(sub => ({
        id: sub.id,
        userId: sub.user_id,
        tier: sub.tier,
        status: sub.status,
        startDate: new Date(sub.start_date),
        endDate: new Date(sub.end_date),
        paymentId: sub.payment_id,
        orderId: sub.order_id,
        amount: sub.amount,
        currency: sub.currency,
        duration: sub.duration,
        autoRenew: sub.auto_renew,
        createdAt: new Date(sub.created_at),
        updatedAt: new Date(sub.updated_at),
      }));
    } catch (error: any) {
      logger.error('Failed to get subscription history', error, 'PaymentService');
      return [];
    }
  }
}