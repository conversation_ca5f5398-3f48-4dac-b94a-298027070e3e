import { supabase } from '../lib/supabase';
import { PostureAnalysis } from '../types';
import { logger } from '../utils/logger';

export interface PostureAnalysisRecord {
  id: string;
  user_id: string;
  overall_score: number;
  neck_angle: number;
  shoulder_alignment: number;
  spine_alignment: number;
  hip_alignment: number;
  recommendations: string[];
  session_duration: number;
  landmarks_data?: any;
  created_at: string;
  updated_at: string;
}

export interface PostureAnalysisStats {
  totalAnalyses: number;
  averageScore: number;
  bestScore: number;
  worstScore: number;
  improvementTrend: number; // Percentage change from first to latest
  recentAnalyses: PostureAnalysisRecord[];
}

class PostureAnalysisService {
  private static instance: PostureAnalysisService;

  static getInstance(): PostureAnalysisService {
    if (!PostureAnalysisService.instance) {
      PostureAnalysisService.instance = new PostureAnalysisService();
    }
    return PostureAnalysisService.instance;
  }

  // Save posture analysis to database
  async saveAnalysis(analysis: PostureAnalysis): Promise<PostureAnalysisRecord | null> {
    try {
      logger.info('Saving posture analysis', { analysisId: analysis.id }, 'PostureAnalysisService');

      const analysisRecord: Omit<PostureAnalysisRecord, 'id' | 'created_at' | 'updated_at'> = {
        user_id: analysis.userId || '',
        overall_score: analysis.overallScore,
        neck_angle: analysis.neckAngle,
        shoulder_alignment: analysis.shoulderAlignment,
        spine_alignment: analysis.spineAlignment,
        hip_alignment: analysis.hipAlignment,
        recommendations: analysis.recommendations,
        session_duration: analysis.sessionDuration,
        landmarks_data: analysis.landmarks ? JSON.stringify(analysis.landmarks) : null,
      };

      const { data, error } = await supabase
        .from('posture_analyses')
        .insert([analysisRecord])
        .select()
        .single();

      if (error) {
        logger.error('Failed to save posture analysis', error, 'PostureAnalysisService');
        throw error;
      }

      logger.info('Posture analysis saved successfully', { recordId: data.id }, 'PostureAnalysisService');
      return data;
    } catch (error) {
      logger.error('Error saving posture analysis', error, 'PostureAnalysisService');
      return null;
    }
  }

  // Get user's posture analysis history
  async getUserAnalyses(userId: string, limit: number = 50): Promise<PostureAnalysisRecord[]> {
    try {
      const { data, error } = await supabase
        .from('posture_analyses')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        logger.error('Failed to fetch user analyses', error, 'PostureAnalysisService');
        throw error;
      }

      return data || [];
    } catch (error) {
      logger.error('Error fetching user analyses', error, 'PostureAnalysisService');
      return [];
    }
  }

  // Get user's posture statistics
  async getUserStats(userId: string): Promise<PostureAnalysisStats> {
    try {
      const analyses = await this.getUserAnalyses(userId, 100);

      if (analyses.length === 0) {
        return {
          totalAnalyses: 0,
          averageScore: 0,
          bestScore: 0,
          worstScore: 0,
          improvementTrend: 0,
          recentAnalyses: [],
        };
      }

      const scores = analyses.map(a => a.overall_score);
      const totalAnalyses = analyses.length;
      const averageScore = Math.round(scores.reduce((sum, score) => sum + score, 0) / totalAnalyses);
      const bestScore = Math.max(...scores);
      const worstScore = Math.min(...scores);

      // Calculate improvement trend (last 5 vs first 5)
      let improvementTrend = 0;
      if (totalAnalyses >= 10) {
        const recent5 = analyses.slice(0, 5).map(a => a.overall_score);
        const first5 = analyses.slice(-5).map(a => a.overall_score);
        const recentAvg = recent5.reduce((sum, score) => sum + score, 0) / 5;
        const firstAvg = first5.reduce((sum, score) => sum + score, 0) / 5;
        improvementTrend = Math.round(((recentAvg - firstAvg) / firstAvg) * 100);
      }

      return {
        totalAnalyses,
        averageScore,
        bestScore,
        worstScore,
        improvementTrend,
        recentAnalyses: analyses.slice(0, 10), // Last 10 analyses
      };
    } catch (error) {
      logger.error('Error calculating user stats', error, 'PostureAnalysisService');
      return {
        totalAnalyses: 0,
        averageScore: 0,
        bestScore: 0,
        worstScore: 0,
        improvementTrend: 0,
        recentAnalyses: [],
      };
    }
  }

  // Delete a specific analysis
  async deleteAnalysis(analysisId: string, userId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('posture_analyses')
        .delete()
        .eq('id', analysisId)
        .eq('user_id', userId); // Ensure user can only delete their own analyses

      if (error) {
        logger.error('Failed to delete analysis', error, 'PostureAnalysisService');
        return false;
      }

      logger.info('Analysis deleted successfully', { analysisId }, 'PostureAnalysisService');
      return true;
    } catch (error) {
      logger.error('Error deleting analysis', error, 'PostureAnalysisService');
      return false;
    }
  }

  // Get analysis by ID
  async getAnalysisById(analysisId: string, userId: string): Promise<PostureAnalysisRecord | null> {
    try {
      const { data, error } = await supabase
        .from('posture_analyses')
        .select('*')
        .eq('id', analysisId)
        .eq('user_id', userId)
        .single();

      if (error) {
        logger.error('Failed to fetch analysis', error, 'PostureAnalysisService');
        return null;
      }

      return data;
    } catch (error) {
      logger.error('Error fetching analysis', error, 'PostureAnalysisService');
      return null;
    }
  }
}

export const postureAnalysisService = PostureAnalysisService.getInstance();
export default postureAnalysisService;