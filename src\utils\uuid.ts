/**
 * UUID utility functions for generating proper UUIDs
 */

/**
 * Generate a proper UUID v4
 * This creates a valid UUID that can be used in PostgreSQL UUID columns
 */
export function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

/**
 * Generate a UUID for questionnaire responses
 */
export function generateResponseId(): string {
  return generateUUID();
}

/**
 * Generate a UUID for questionnaire sessions
 */
export function generateSessionId(): string {
  return generateUUID();
}

/**
 * Generate a UUID for insights
 */
export function generateInsightId(): string {
  return generateUUID();
}

/**
 * Generate a UUID for recommendations
 */
export function generateRecommendationId(): string {
  return generateUUID();
}

/**
 * Validate if a string is a valid UUID
 */
export function isValidUUID(uuid: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
}

/**
 * Generate a short ID for logging/debugging (not a UUID)
 * Use this for non-database IDs like request IDs, session IDs for logging, etc.
 */
export function generateShortId(): string {
  return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}
