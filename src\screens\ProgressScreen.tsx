import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  StatusBar,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { LinearGradient } from 'expo-linear-gradient';
import { useAuth } from '../contexts/AuthContext';
import { UserAnalytics, DailyProgress, MonthlyProgress, RootStackParamList } from '../types';

const { width } = Dimensions.get('window');

type ProgressScreenNavigationProp = StackNavigationProp<RootStackParamList>;

interface Achievement {
  id: string;
  name: string;
  nameHindi: string;
  description: string;
  descriptionHindi: string;
  icon: string;
  earned: boolean;
  earnedDate?: Date;
}

const ProgressScreen: React.FC = () => {
  const { t, i18n } = useTranslation();
  const navigation = useNavigation<ProgressScreenNavigationProp>();
  const { user } = useAuth();

  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'year'>('week');
  const [analytics, setAnalytics] = useState<UserAnalytics | null>(null);

  // Load real analytics data from Firebase
  useEffect(() => {
    if (!user) return;
    
    // TODO: Load real analytics data from Firebase
    // For now, set to null to show no data state
    setAnalytics(null);
  }, [user]);

  const achievements: Achievement[] = [
    {
      id: '1',
      name: 'First Session',
      nameHindi: 'पहला सत्र',
      description: 'Complete your first yoga session',
      descriptionHindi: 'अपना पहला योग सत्र पूरा करें',
      icon: 'play-circle',
      earned: true,
      earnedDate: new Date('2024-01-10'),
    },
    {
      id: '2',
      name: '7 Day Streak',
      nameHindi: '7 दिन की लगातार अभ्यास',
      description: 'Practice for 7 consecutive days',
      descriptionHindi: '7 लगातार दिनों तक अभ्यास करें',
      icon: 'flame',
      earned: true,
      earnedDate: new Date('2024-01-17'),
    },
    {
      id: '3',
      name: 'Perfect Posture',
      nameHindi: 'परफेक्ट पोस्चर',
      description: 'Achieve a posture score of 95+',
      descriptionHindi: '95+ का पोस्चर स्कोर प्राप्त करें',
      icon: 'trophy',
      earned: false,
    },
    {
      id: '4',
      name: 'Yoga Master',
      nameHindi: 'योग मास्टर',
      description: 'Complete 50 yoga sessions',
      descriptionHindi: '50 योग सत्र पूरे करें',
      icon: 'medal',
      earned: false,
    },
  ];

  const getScoreColor = (score: number) => {
    if (score >= 90) return '#10B981';
    if (score >= 75) return '#F59E0B';
    if (score >= 60) return '#EF4444';
    return '#DC2626';
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  };

  const renderProgressChart = () => {
    if (!analytics) return null;

    const data = selectedPeriod === 'week' ? analytics.weeklyProgress : [];
    const maxScore = Math.max(...data.map((d: any) => d.score));

    return (
      <View style={styles.chartContainer}>
        <Text style={styles.chartTitle}>
          {selectedPeriod === 'week'
            ? (i18n.language === 'hi' ? 'साप्ताहिक प्रगति' : 'Weekly Progress')
            : (i18n.language === 'hi' ? 'मासिक प्रगति' : 'Monthly Progress')}
        </Text>

        <View style={styles.chart}>
          {data.map((item: any, index: number) => {
            const height = (item.averageScore / maxScore) * 100;
            const date = new Date(item.date);
            const dayName = date.toLocaleDateString(i18n.language === 'hi' ? 'hi-IN' : 'en-IN', { weekday: 'short' });

            return (
              <View key={index} style={styles.chartBar}>
                <View style={styles.barContainer}>
                  <View
                    style={[
                      styles.bar,
                      {
                        height: `${height}%`,
                        backgroundColor: getScoreColor(item.averageScore)
                      }
                    ]}
                  />
                </View>
                <Text style={styles.barLabel}>{dayName}</Text>
                <Text style={styles.barValue}>{item.averageScore}</Text>
              </View>
            );
          })}
        </View>
      </View>
    );
  };

  // Show login prompt if user is not authenticated
  if (!user) {
    return (
      <View style={styles.container}>
        <StatusBar barStyle="light-content" backgroundColor="#000000" />

        {/* Background Gradient */}
        <LinearGradient
          colors={['#000000', '#1a1a1a', '#2d2d2d']}
          style={StyleSheet.absoluteFillObject}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        />

        <ScrollView showsVerticalScrollIndicator={false}>
          <View style={styles.authPromptContainer}>
            {/* Guest Mode Banner */}
            <TouchableOpacity
              style={styles.guestBanner}
              onPress={() => {
                // Navigate to auth screen
                console.log('Navigate to Auth screen');
                navigation.navigate('Auth');
              }}
              activeOpacity={0.8}
            >
              <View style={styles.guestBannerContent}>
                <View style={styles.guestBannerIcon}>
                  <Ionicons name="analytics" size={20} color="#FFD700" />
                </View>
                <View style={styles.guestBannerText}>
                  <Text style={styles.guestBannerTitle}>
                    {i18n.language === 'hi' ? 'अपनी प्रगति ट्रैक करें' : 'Track Your Progress'}
                  </Text>
                  <Text style={styles.guestBannerSubtitle}>
                    {i18n.language === 'hi'
                      ? 'व्यक्तिगत इनसाइट्स और विस्तृत एनालिटिक्स के लिए साइन अप करें'
                      : 'Sign up for personalized insights & detailed analytics'}
                  </Text>
                </View>
                <Ionicons name="arrow-forward" size={16} color="rgba(255, 255, 255, 0.8)" />
              </View>
            </TouchableOpacity>

            {/* Guest Features */}
            <View style={styles.guestFeaturesCard}>
              <Text style={styles.guestFeaturesTitle}>
                {i18n.language === 'hi' ? 'गेस्ट के रूप में उपलब्ध' : 'Available as Guest'}
              </Text>
              
              <View style={styles.guestFeaturesList}>
                <View style={styles.guestFeatureItem}>
                  <View style={styles.guestFeatureLeft}>
                    <View style={styles.guestFeatureIcon}>
                      <Ionicons name="eye-outline" size={20} color="rgba(255, 255, 255, 0.8)" />
                    </View>
                    <Text style={styles.guestFeatureText}>
                      {i18n.language === 'hi' ? 'बेसिक प्रगति देखें' : 'View Basic Progress'}
                    </Text>
                  </View>
                </View>
                
                <View style={styles.guestFeatureItem}>
                  <View style={styles.guestFeatureLeft}>
                    <View style={styles.guestFeatureIcon}>
                      <Ionicons name="trophy-outline" size={20} color="rgba(255, 255, 255, 0.8)" />
                    </View>
                    <Text style={styles.guestFeatureText}>
                      {i18n.language === 'hi' ? 'प्रगति ट्रैकिंग' : 'Progress Tracking'}
                    </Text>
                  </View>
                </View>
              </View>
            </View>
          </View>
          
          <View style={styles.bottomSpacing} />
        </ScrollView>
      </View>
    );
  }

  if (!analytics) {
    return (
      <View style={styles.container}>
        <StatusBar barStyle="light-content" backgroundColor="#000000" />
        <LinearGradient
          colors={['#000000', '#1a1a1a', '#2d2d2d']}
          style={StyleSheet.absoluteFillObject}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        />
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <View style={styles.header}>
            <Text style={styles.headerTitle}>{t('progress.title')}</Text>
            <Text style={styles.headerSubtitle}>
              {i18n.language === 'hi'
                ? 'अपनी यात्रा को ट्रैक करें'
                : 'Track your journey'}
            </Text>
          </View>
          <View style={styles.emptyStateContainer}>
            <Ionicons name="analytics-outline" size={64} color="rgba(255, 255, 255, 0.3)" />
            <Text style={styles.emptyStateTitle}>
              {i18n.language === 'hi' ? 'कोई डेटा उपलब्ध नहीं' : 'No Data Available'}
            </Text>
            <Text style={styles.emptyStateText}>
              {i18n.language === 'hi'
                ? 'अपना पहला पोस्चर चेक या योग सत्र पूरा करें'
                : 'Complete your first posture check or yoga session to see your progress'}
            </Text>
          </View>
        </ScrollView>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#000000" />

      {/* Background Gradient */}
      <LinearGradient
        colors={['#000000', '#1a1a1a', '#2d2d2d']}
        style={StyleSheet.absoluteFillObject}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.headerTitle}>{t('progress.title')}</Text>
          <Text style={styles.headerSubtitle}>
            {i18n.language === 'hi'
              ? 'अपनी यात्रा को ट्रैक करें'
              : 'Track your journey'}
          </Text>
        </View>

        {/* Overview Stats */}
        <View style={styles.statsContainer}>
          <View style={styles.statCard}>
            <Ionicons name="fitness" size={24} color="#6366F1" />
            <Text style={styles.statNumber}>{analytics.totalSessions}</Text>
            <Text style={styles.statLabel}>{t('progress.totalSessions')}</Text>
          </View>

          <View style={styles.statCard}>
            <Ionicons name="time" size={24} color="#10B981" />
            <Text style={styles.statNumber}>{analytics.totalSessions * 15} min</Text>
            <Text style={styles.statLabel}>{t('progress.totalMinutes')}</Text>
          </View>

          <View style={styles.statCard}>
            <Ionicons name="analytics" size={24} color="#F59E0B" />
            <Text style={[styles.statNumber, { color: getScoreColor(analytics.averageScore) }]}>
              {analytics.averageScore}
            </Text>
            <Text style={styles.statLabel}>{t('progress.averageScore')}</Text>
          </View>
        </View>

        {/* Improvement Card */}
        <View style={styles.improvementCard}>
          <View style={styles.improvementHeader}>
            <View>
              <Text style={styles.improvementTitle}>
                {i18n.language === 'hi' ? 'इस महीने सुधार' : 'This Month\'s Improvement'}
              </Text>
              <Text style={styles.improvementSubtitle}>
                {i18n.language === 'hi' ? 'पिछले महीने की तुलना में' : 'Compared to last month'}
              </Text>
            </View>
            <View style={styles.improvementBadge}>
              <Ionicons name="trending-up" size={16} color="#10B981" />
              <Text style={styles.improvementPercentage}>+{analytics.improvementRate}%</Text>
            </View>
          </View>

          <View style={styles.improvementDetails}>
            <View style={styles.improvementItem}>
              <Text style={styles.improvementItemLabel}>
                {i18n.language === 'hi' ? 'स्ट्रीक दिन' : 'Streak Days'}
              </Text>
              <Text style={styles.improvementItemValue}>{analytics.streakDays}</Text>
            </View>

            <View style={styles.improvementItem}>
              <Text style={styles.improvementItemLabel}>
                {i18n.language === 'hi' ? 'सर्वोत्तम स्कोर' : 'Best Score'}
              </Text>
              <Text style={styles.improvementItemValue}>94</Text>
            </View>
          </View>
        </View>

        {/* Period Selector */}
        <View style={styles.periodSelector}>
          {['week', 'month', 'year'].map((period) => (
            <TouchableOpacity
              key={period}
              style={[styles.periodButton, selectedPeriod === period && styles.periodButtonActive]}
              onPress={() => setSelectedPeriod(period as any)}
            >
              <Text style={[styles.periodButtonText, selectedPeriod === period && styles.periodButtonTextActive]}>
                {period === 'week'
                  ? (i18n.language === 'hi' ? 'सप्ताह' : 'Week')
                  : period === 'month'
                    ? (i18n.language === 'hi' ? 'महीना' : 'Month')
                    : (i18n.language === 'hi' ? 'साल' : 'Year')}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Progress Chart */}
        {renderProgressChart()}

        {/* Achievements */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('progress.achievements')}</Text>

          <View style={styles.achievementsGrid}>
            {achievements.map((achievement) => (
              <View key={achievement.id} style={[styles.achievementCard, achievement.earned && styles.achievementCardEarned]}>
                <View style={[styles.achievementIcon, achievement.earned && styles.achievementIconEarned]}>
                  <Ionicons
                    name={achievement.icon as any}
                    size={24}
                    color={achievement.earned ? '#F59E0B' : '#9CA3AF'}
                  />
                </View>

                <Text style={[styles.achievementName, achievement.earned && styles.achievementNameEarned]}>
                  {i18n.language === 'hi' ? achievement.nameHindi : achievement.name}
                </Text>

                <Text style={styles.achievementDescription}>
                  {i18n.language === 'hi' ? achievement.descriptionHindi : achievement.description}
                </Text>

                {achievement.earned && achievement.earnedDate && (
                  <Text style={styles.achievementDate}>
                    {achievement.earnedDate.toLocaleDateString(i18n.language === 'hi' ? 'hi-IN' : 'en-IN')}
                  </Text>
                )}
              </View>
            ))}
          </View>
        </View>

        {/* Weekly Goal */}
        <View style={styles.goalCard}>
          <View style={styles.goalHeader}>
            <Text style={styles.goalTitle}>
              {i18n.language === 'hi' ? 'साप्ताहिक लक्ष्य' : 'Weekly Goal'}
            </Text>
            <Text style={styles.goalProgress}>5/7 {i18n.language === 'hi' ? 'दिन' : 'days'}</Text>
          </View>

          <View style={styles.goalProgressBar}>
            <View style={[styles.goalProgressFill, { width: '71%' }]} />
          </View>

          <Text style={styles.goalDescription}>
            {i18n.language === 'hi'
              ? '2 और दिन अभ्यास करके अपना साप्ताहिक लक्ष्य पूरा करें'
              : 'Complete 2 more days to reach your weekly goal'}
          </Text>
        </View>

        {/* Share Progress */}
        <TouchableOpacity style={styles.shareButton}>
          <Ionicons name="share-social" size={20} color="#6366F1" />
          <Text style={styles.shareButtonText}>
            {i18n.language === 'hi' ? 'प्रगति साझा करें' : 'Share Progress'}
          </Text>
        </TouchableOpacity>

        <View style={styles.bottomSpacing} />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000000',
  },
  loadingText: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  header: {
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
    paddingBottom: 30,
    paddingHorizontal: 20,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: '300',
    color: '#FFFFFF',
    marginBottom: 8,
    letterSpacing: 0.5,
  },
  headerSubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.7)',
  },
  statsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    marginTop: -20,
  },
  statCard: {
    flex: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
    marginHorizontal: 4,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginTop: 12,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
  },
  improvementCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    marginHorizontal: 20,
    marginTop: 20,
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  improvementHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  improvementTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  improvementSubtitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.7)',
    marginTop: 2,
  },
  improvementBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(16, 185, 129, 0.2)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  improvementPercentage: {
    fontSize: 14,
    fontWeight: '600',
    color: '#10B981',
    marginLeft: 4,
  },
  improvementDetails: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  improvementItem: {
    alignItems: 'center',
  },
  improvementItemLabel: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.7)',
    marginBottom: 4,
  },
  improvementItemValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  periodSelector: {
    flexDirection: 'row',
    marginHorizontal: 20,
    marginTop: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 12,
    padding: 4,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  periodButton: {
    flex: 1,
    paddingVertical: 8,
    alignItems: 'center',
    borderRadius: 8,
  },
  periodButtonActive: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  periodButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: 'rgba(255, 255, 255, 0.6)',
  },
  periodButtonTextActive: {
    color: '#FFFFFF',
  },
  chartContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    marginHorizontal: 20,
    marginTop: 20,
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  chartTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: 16,
  },
  chart: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    height: 120,
  },
  chartBar: {
    flex: 1,
    alignItems: 'center',
  },
  barContainer: {
    height: 80,
    width: 20,
    justifyContent: 'flex-end',
    marginBottom: 8,
  },
  bar: {
    width: '100%',
    borderRadius: 4,
    minHeight: 4,
  },
  barLabel: {
    fontSize: 10,
    color: 'rgba(255, 255, 255, 0.7)',
    marginBottom: 2,
  },
  barValue: {
    fontSize: 10,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  section: {
    marginHorizontal: 20,
    marginTop: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: 16,
  },
  achievementsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  achievementCard: {
    width: (width - 52) / 2,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  achievementCardEarned: {
    borderWidth: 2,
    borderColor: 'rgba(245, 158, 11, 0.5)',
    backgroundColor: 'rgba(245, 158, 11, 0.1)',
  },
  achievementIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  achievementIconEarned: {
    backgroundColor: 'rgba(245, 158, 11, 0.2)',
  },
  achievementName: {
    fontSize: 14,
    fontWeight: '600',
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
    marginBottom: 4,
  },
  achievementNameEarned: {
    color: '#FFFFFF',
  },
  achievementDescription: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.5)',
    textAlign: 'center',
    marginBottom: 8,
  },
  achievementDate: {
    fontSize: 10,
    color: '#F59E0B',
    fontWeight: '500',
  },
  goalCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    marginHorizontal: 20,
    marginTop: 20,
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  goalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  goalTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  goalProgress: {
    fontSize: 14,
    fontWeight: '500',
    color: '#4CAF50',
  },
  goalProgressBar: {
    height: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 4,
    marginBottom: 12,
  },
  goalProgressFill: {
    height: '100%',
    backgroundColor: '#4CAF50',
    borderRadius: 4,
  },
  goalDescription: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.7)',
  },
  shareButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    marginHorizontal: 20,
    marginTop: 20,
    borderRadius: 12,
    paddingVertical: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  shareButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#4CAF50',
    marginLeft: 8,
  },
  bottomSpacing: {
    height: Platform.OS === 'ios' ? 122 : 102, // Tab bar height + extra spacing
  },
  authPromptContainer: {
    paddingTop: 60,
    paddingHorizontal: 20,
  },
  guestBanner: {
    backgroundColor: 'rgba(255, 215, 0, 0.1)',
    marginBottom: 20,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 215, 0, 0.3)',
    padding: 16,
  },
  guestBannerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  guestBannerIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(255, 215, 0, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  guestBannerText: {
    flex: 1,
  },
  guestBannerTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFD700',
    marginBottom: 2,
    letterSpacing: 0.2,
  },
  guestBannerSubtitle: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.8)',
    lineHeight: 16,
  },
  guestFeaturesCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 20,
    padding: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  guestFeaturesTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: 16,
    textAlign: 'center',
  },
  guestFeaturesList: {
    gap: 8,
  },
  guestFeatureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
  },
  guestFeatureLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  guestFeatureIcon: {
    width: 32,
    height: 32,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  guestFeatureText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#FFFFFF',
    flex: 1,
  },
  emptyStateContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
    paddingHorizontal: 40,
  },
  emptyStateTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'rgba(255, 255, 255, 0.9)',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyStateText: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
    lineHeight: 24,
  },
});

export default ProgressScreen;