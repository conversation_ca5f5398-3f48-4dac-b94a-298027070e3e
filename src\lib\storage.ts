import { supabase } from './supabase';

export class StorageService {
  // Upload file to Supabase Storage
  static async uploadFile(
    bucket: string,
    filePath: string,
    file: File | Blob | ArrayBuffer,
    options?: {
      contentType?: string;
      upsert?: boolean;
    }
  ) {
    try {
      const { data, error } = await supabase.storage
        .from(bucket)
        .upload(filePath, file, {
          contentType: options?.contentType,
          upsert: options?.upsert || false,
        });

      if (error) throw error;
      return data;
    } catch (error: any) {
      throw error;
    }
  }

  // Get public URL for a file
  static getPublicUrl(bucket: string, filePath: string) {
    const { data } = supabase.storage
      .from(bucket)
      .getPublicUrl(filePath);
    
    return data.publicUrl;
  }

  // Download file
  static async downloadFile(bucket: string, filePath: string) {
    try {
      const { data, error } = await supabase.storage
        .from(bucket)
        .download(filePath);

      if (error) throw error;
      return data;
    } catch (error: any) {
      throw error;
    }
  }

  // Delete file
  static async deleteFile(bucket: string, filePath: string) {
    try {
      const { error } = await supabase.storage
        .from(bucket)
        .remove([filePath]);

      if (error) throw error;
    } catch (error: any) {
      throw error;
    }
  }

  // Upload avatar image
  static async uploadAvatar(userId: string, imageUri: string) {
    try {
      // Convert image URI to blob for upload
      const response = await fetch(imageUri);
      const blob = await response.blob();
      
      const fileExt = imageUri.split('.').pop();
      const fileName = `${userId}/avatar.${fileExt}`;

      const { data, error } = await supabase.storage
        .from('avatars')
        .upload(fileName, blob, {
          contentType: `image/${fileExt}`,
          upsert: true,
        });

      if (error) throw error;

      // Get public URL
      const publicUrl = this.getPublicUrl('avatars', fileName);
      return publicUrl;
    } catch (error: any) {
      throw error;
    }
  }

  // Upload posture session data/images
  static async uploadSessionData(userId: string, sessionId: string, data: any) {
    try {
      const fileName = `${userId}/sessions/${sessionId}.json`;
      const blob = new Blob([JSON.stringify(data)], { type: 'application/json' });

      const { data: uploadData, error } = await supabase.storage
        .from('session-data')
        .upload(fileName, blob, {
          contentType: 'application/json',
          upsert: true,
        });

      if (error) throw error;
      return uploadData;
    } catch (error: any) {
      throw error;
    }
  }
}