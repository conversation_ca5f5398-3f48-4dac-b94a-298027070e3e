import { AppStorage, STORAGE_KEYS } from '../utils/storage';
import NetInfo from '@react-native-community/netinfo';
import { logger } from '../utils/logger';
import { DataService } from './dataService';
import { PostureAnalysis, YogaSession, User } from '../types';

/**
 * Offline synchronization service for seamless user experience
 * Handles data caching, queue management, and automatic sync when online
 */
export class OfflineSyncService {
  private static instance: OfflineSyncService;
  private dataService: DataService;
  private syncQueue: SyncOperation[] = [];
  private isOnline: boolean = true;
  private syncInProgress: boolean = false;
  private readonly OFFLINE_STORAGE_KEYS = {
    SYNC_QUEUE: STORAGE_KEYS.SYNC_QUEUE,
    CACHED_DATA: STORAGE_KEYS.CACHED_DATA,
    LAST_SYNC: STORAGE_KEYS.LAST_SYNC,
  };

  private constructor() {
    this.dataService = DataService.getInstance();
    this.initializeNetworkListener();
    this.loadSyncQueue();
  }

  public static getInstance(): OfflineSyncService {
    if (!OfflineSyncService.instance) {
      OfflineSyncService.instance = new OfflineSyncService();
    }
    return OfflineSyncService.instance;
  }

  /**
   * Initialize network state listener
   */
  private async initializeNetworkListener(): Promise<void> {
    try {
      // Get initial network state
      const netInfo = await NetInfo.fetch();
      this.isOnline = netInfo.isConnected ?? false;
      
      // Listen for network changes
      NetInfo.addEventListener(state => {
        const wasOffline = !this.isOnline;
        this.isOnline = state.isConnected ?? false;
        
        logger.info('Network state changed', { 
          isOnline: this.isOnline,
          connectionType: state.type 
        }, 'OfflineSync');
        
        // Trigger sync when coming back online
        if (wasOffline && this.isOnline) {
          this.syncPendingOperations();
        }
      });
    } catch (error) {
      logger.error('Failed to initialize network listener', error, 'OfflineSync');
    }
  }

  /**
   * Load sync queue from storage
   */
  private async loadSyncQueue(): Promise<void> {
    try {
      const queueData = await AppStorage.getItem(this.OFFLINE_STORAGE_KEYS.SYNC_QUEUE);
      if (queueData) {
        this.syncQueue = JSON.parse(queueData);
        logger.info('Loaded sync queue', { queueLength: this.syncQueue.length }, 'OfflineSync');
      }
    } catch (error) {
      logger.error('Failed to load sync queue', error, 'OfflineSync');
      this.syncQueue = [];
    }
  }

  /**
   * Save sync queue to storage
   */
  private async saveSyncQueue(): Promise<void> {
    try {
      await AppStorage.setItem(this.OFFLINE_STORAGE_KEYS.SYNC_QUEUE, JSON.stringify(this.syncQueue));
    } catch (error) {
      logger.error('Failed to save sync queue', error, 'OfflineSync');
    }
  }

  /**
   * Add operation to sync queue
   */
  private async addToSyncQueue(operation: SyncOperation): Promise<void> {
    this.syncQueue.push(operation);
    await this.saveSyncQueue();
    logger.debug('Added operation to sync queue', { operation: operation.type }, 'OfflineSync');
  }

  /**
   * Cache data for offline access
   */
  async cacheData(key: string, data: any): Promise<void> {
    try {
      const cacheKey = `${this.OFFLINE_STORAGE_KEYS.CACHED_DATA}_${key}`;
      const cacheEntry = {
        data,
        timestamp: Date.now(),
        version: '1.0'
      };
      await AppStorage.setItem(cacheKey, JSON.stringify(cacheEntry));
      logger.debug('Data cached successfully', { key }, 'OfflineSync');
    } catch (error) {
      logger.error('Failed to cache data', error, 'OfflineSync');
    }
  }

  /**
   * Get cached data
   */
  async getCachedData<T>(key: string, maxAge: number = 24 * 60 * 60 * 1000): Promise<T | null> {
    try {
      const cacheKey = `${this.OFFLINE_STORAGE_KEYS.CACHED_DATA}_${key}`;
      const cachedData = await AppStorage.getItem(cacheKey);
      
      if (!cachedData) {
        return null;
      }

      const cacheEntry = JSON.parse(cachedData);
      const age = Date.now() - cacheEntry.timestamp;
      
      if (age > maxAge) {
        // Cache expired, remove it
        await AppStorage.removeItem(cacheKey);
        return null;
      }

      logger.debug('Retrieved cached data', { key, age }, 'OfflineSync');
      return cacheEntry.data as T;
    } catch (error) {
      logger.error('Failed to get cached data', error, 'OfflineSync');
      return null;
    }
  }

  /**
   * Save posture analysis offline
   */
  async savePostureAnalysisOffline(analysis: Omit<PostureAnalysis, 'id' | 'createdAt'>): Promise<string> {
    const tempId = `offline_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    if (this.isOnline) {
      try {
        // Try to save online first
        const savedAnalysis = await this.dataService.savePostureAnalysis(analysis);
        return savedAnalysis.id;
      } catch (error) {
        logger.warn('Failed to save online, queuing for offline sync', error, 'OfflineSync');
      }
    }

    // Save offline
    await this.addToSyncQueue({
      id: tempId,
      type: 'CREATE_POSTURE_ANALYSIS',
      data: analysis,
      timestamp: Date.now(),
      retryCount: 0
    });

    // Cache locally
    await this.cacheData(`posture_analysis_${tempId}`, {
      ...analysis,
      id: tempId,
      createdAt: new Date().toISOString(),
      isOffline: true
    });

    return tempId;
  }

  /**
   * Save yoga session offline
   */
  async saveYogaSessionOffline(session: Omit<YogaSession, 'id' | 'createdAt'>): Promise<string> {
    const tempId = `offline_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    if (this.isOnline) {
      try {
        // Try to save online first
        const savedSession = await this.dataService.saveYogaSession(session);
        return savedSession.id;
      } catch (error) {
        logger.warn('Failed to save online, queuing for offline sync', error, 'OfflineSync');
      }
    }

    // Save offline
    await this.addToSyncQueue({
      id: tempId,
      type: 'CREATE_YOGA_SESSION',
      data: session,
      timestamp: Date.now(),
      retryCount: 0
    });

    // Cache locally
    await this.cacheData(`yoga_session_${tempId}`, {
      ...session,
      id: tempId,
      createdAt: new Date().toISOString(),
      isOffline: true
    });

    return tempId;
  }

  /**
   * Get offline posture analyses
   */
  async getOfflinePostureAnalyses(userId: string): Promise<PostureAnalysis[]> {
    try {
      const analyses: PostureAnalysis[] = [];
      
      // Get all cached analyses for this user
      const keys = await AppStorage.getAllKeys();
      const analysisKeys = keys.filter(key => 
        key.startsWith(`${this.OFFLINE_STORAGE_KEYS.CACHED_DATA}_posture_analysis_`)
      );

      for (const key of analysisKeys) {
        const cachedData = await this.getCachedData<PostureAnalysis>(key.replace(`${this.OFFLINE_STORAGE_KEYS.CACHED_DATA}_`, ''));
        if (cachedData && cachedData.userId === userId) {
          analyses.push(cachedData);
        }
      }

      return analyses.sort((a, b) => 
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      );
    } catch (error) {
      logger.error('Failed to get offline posture analyses', error, 'OfflineSync');
      return [];
    }
  }

  /**
   * Sync pending operations when online
   */
  async syncPendingOperations(): Promise<void> {
    if (!this.isOnline || this.syncInProgress || this.syncQueue.length === 0) {
      return;
    }

    this.syncInProgress = true;
    logger.info('Starting sync of pending operations', { queueLength: this.syncQueue.length }, 'OfflineSync');

    const successfulSyncs: string[] = [];
    const failedSyncs: SyncOperation[] = [];

    for (const operation of this.syncQueue) {
      try {
        await this.executeSyncOperation(operation);
        successfulSyncs.push(operation.id);
        logger.debug('Sync operation completed', { operationId: operation.id, type: operation.type }, 'OfflineSync');
      } catch (error) {
        operation.retryCount++;
        if (operation.retryCount < 3) {
          failedSyncs.push(operation);
          logger.warn('Sync operation failed, will retry', { 
            operationId: operation.id, 
            retryCount: operation.retryCount,
            error: error instanceof Error ? error.message : String(error) 
          }, 'OfflineSync');
        } else {
          logger.error('Sync operation failed permanently', { 
            operationId: operation.id, 
            retryCount: operation.retryCount,
            error 
          }, 'OfflineSync');
        }
      }
    }

    // Update sync queue
    this.syncQueue = failedSyncs;
    await this.saveSyncQueue();

    // Update last sync timestamp
    await AppStorage.setItem(this.OFFLINE_STORAGE_KEYS.LAST_SYNC, Date.now().toString());

    this.syncInProgress = false;
    logger.info('Sync completed', { 
      successful: successfulSyncs.length, 
      failed: failedSyncs.length 
    }, 'OfflineSync');
  }

  /**
   * Execute a single sync operation
   */
  private async executeSyncOperation(operation: SyncOperation): Promise<void> {
    switch (operation.type) {
      case 'CREATE_POSTURE_ANALYSIS':
        await this.dataService.savePostureAnalysis(operation.data);
        // Remove from local cache after successful sync
        await AppStorage.removeItem(`${this.OFFLINE_STORAGE_KEYS.CACHED_DATA}_posture_analysis_${operation.id}`);
        break;
        
      case 'CREATE_YOGA_SESSION':
        await this.dataService.saveYogaSession(operation.data);
        // Remove from local cache after successful sync
        await AppStorage.removeItem(`${this.OFFLINE_STORAGE_KEYS.CACHED_DATA}_yoga_session_${operation.id}`);
        break;
        
      case 'UPDATE_USER_PROFILE':
        await this.dataService.createOrUpdateUser(operation.data);
        break;
        
      default:
        throw new Error(`Unknown sync operation type: ${operation.type}`);
    }
  }

  /**
   * Clear all offline data
   */
  async clearOfflineData(): Promise<void> {
    try {
      const keys = await AppStorage.getAllKeys();
      const offlineKeys = keys.filter(key => 
        key.startsWith(this.OFFLINE_STORAGE_KEYS.CACHED_DATA) || 
        key === this.OFFLINE_STORAGE_KEYS.SYNC_QUEUE ||
        key === this.OFFLINE_STORAGE_KEYS.LAST_SYNC
      );
      
      await AppStorage.multiRemove(offlineKeys);
      this.syncQueue = [];
      
      logger.info('Offline data cleared', { keysRemoved: offlineKeys.length }, 'OfflineSync');
    } catch (error) {
      logger.error('Failed to clear offline data', error, 'OfflineSync');
    }
  }

  /**
   * Get sync status
   */
  getSyncStatus(): {
    isOnline: boolean;
    pendingOperations: number;
    syncInProgress: boolean;
    lastSync: number | null;
  } {
    return {
      isOnline: this.isOnline,
      pendingOperations: this.syncQueue.length,
      syncInProgress: this.syncInProgress,
      lastSync: null // Will be loaded from storage if needed
    };
  }
}

// Types
interface SyncOperation {
  id: string;
  type: 'CREATE_POSTURE_ANALYSIS' | 'CREATE_YOGA_SESSION' | 'UPDATE_USER_PROFILE';
  data: any;
  timestamp: number;
  retryCount: number;
}

// Export singleton instance
export const offlineSyncService = OfflineSyncService.getInstance();